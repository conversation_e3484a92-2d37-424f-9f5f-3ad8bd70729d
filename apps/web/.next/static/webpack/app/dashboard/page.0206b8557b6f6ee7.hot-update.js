"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/AnalyticsDashboard.tsx":
/*!***********************************************!*\
  !*** ./src/components/AnalyticsDashboard.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AnalyticsDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/chart/PieChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/polar/Pie.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/component/Cell.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/YAxis.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Cell,Legend,Line,LineChart,Pie,PieChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(app-pages-browser)/../../node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst COLORS = [\n    '#0088FE',\n    '#00C49F',\n    '#FFBB28',\n    '#FF8042',\n    '#8884D8',\n    '#82CA9D',\n    '#FFC658'\n];\nfunction AnalyticsDashboard(param) {\n    let { data, dateRange, onDateRangeChange } = param;\n    _s();\n    const [selectedChart, setSelectedChart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    const { formatCurrency } = (0,_repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore)();\n    const handleDateRangeSubmit = (e)=>{\n        e.preventDefault();\n        const formData = new FormData(e.currentTarget);\n        const startDate = formData.get('startDate');\n        const endDate = formData.get('endDate');\n        if (startDate && endDate) {\n            onDateRangeChange({\n                startDate,\n                endDate\n            });\n        }\n    };\n    // Prepare data for charts\n    const pieChartData = data.categoryBreakdown.filter((item)=>item.total > 0).slice(0, 8) // Limit to top 8 categories for readability\n    .map((item)=>({\n            name: item.category.name,\n            value: item.total,\n            percentage: item.percentage\n        }));\n    const trendChartData = data.monthlyTrends.map((item)=>({\n            month: _repo_shared__WEBPACK_IMPORTED_MODULE_2__.AnalyticsService.formatMonth(item.month),\n            income: item.income,\n            expenses: item.expenses,\n            net: item.net\n        }));\n    const topCategoriesData = data.topCategories.map((item)=>({\n            name: item.category.name,\n            amount: item.total,\n            transactions: item.transactionCount\n        }));\n    const CustomTooltip = (param)=>{\n        let { active, payload, label } = param;\n        if (active && payload && payload.length) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface-elevated p-4 border border-border rounded-xl shadow-lg backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-semibold text-text-primary mb-2\",\n                        children: label\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this),\n                    payload.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm\",\n                            style: {\n                                color: entry.color\n                            },\n                            children: \"\".concat(entry.dataKey, \": \").concat(formatCurrency(entry.value))\n                        }, index, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 13\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                lineNumber: 92,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    const PieTooltip = (param)=>{\n        let { active, payload } = param;\n        if (active && payload && payload.length && payload[0].payload) {\n            const data = payload[0].payload;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface-elevated p-4 border border-border rounded-xl shadow-lg backdrop-blur-sm\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-semibold text-text-primary mb-1\",\n                        children: data.name\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm mb-1\",\n                        style: {\n                            color: payload[0].color\n                        },\n                        children: \"Amount: \".concat(formatCurrency(data.value))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-text-secondary\",\n                        children: \"\".concat(data.percentage.toFixed(1), \"% of total\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface-elevated rounded-xl border border-border-light p-6 shadow-sm hover:shadow-md transition-all duration-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleDateRangeSubmit,\n                    className: \"flex flex-col sm:flex-row items-start sm:items-end gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"startDate\",\n                                    className: \"block text-sm font-medium text-text-primary mb-2\",\n                                    children: \"Start Date\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                    lineNumber: 129,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"date\",\n                                    id: \"startDate\",\n                                    name: \"startDate\",\n                                    defaultValue: dateRange.startDate,\n                                    className: \"w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-4 focus:ring-primary-blue/10\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"endDate\",\n                                    className: \"block text-sm font-medium text-text-primary mb-2\",\n                                    children: \"End Date\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"date\",\n                                    id: \"endDate\",\n                                    name: \"endDate\",\n                                    defaultValue: dateRange.endDate,\n                                    className: \"w-full bg-surface border-2 border-border rounded-lg px-4 py-3 text-base transition-all focus:border-primary-blue focus:outline-none focus:ring-4 focus:ring-primary-blue/10\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 whitespace-nowrap\",\n                            children: \"Update Range\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-surface-elevated rounded-xl border border-border-light p-6 shadow-sm hover:shadow-md transition-all duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-success-green/10 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-success-green\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-text-secondary mb-1\",\n                                            children: \"Total Income\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 173,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-text-primary\",\n                                            children: formatCurrency(data.totalIncome)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                            lineNumber: 164,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-surface-elevated rounded-xl border border-border-light p-6 shadow-sm hover:shadow-md transition-all duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 bg-error-red/10 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 text-error-red\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M13 17h8m0 0V9m0 8l-8-8-4 4-6-6\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-text-secondary mb-1\",\n                                            children: \"Total Expenses\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-text-primary\",\n                                            children: formatCurrency(data.totalExpenses)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 192,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-surface-elevated rounded-xl border border-border-light p-6 shadow-sm hover:shadow-md transition-all duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-12 h-12 \".concat(data.netIncome >= 0 ? 'bg-primary-blue/10' : 'bg-warning-orange/10', \" rounded-lg flex items-center justify-center\"),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-6 h-6 \".concat(data.netIncome >= 0 ? 'text-primary-blue' : 'text-warning-orange'),\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"ml-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-text-secondary mb-1\",\n                                            children: \"Net Income\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold \".concat(data.netIncome >= 0 ? 'text-success-green' : 'text-error-red'),\n                                            children: formatCurrency(data.netIncome)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                lineNumber: 162,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface-elevated rounded-xl border border-border-light p-6 shadow-sm hover:shadow-md transition-all duration-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap gap-2 mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedChart('overview'),\n                                className: \"px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-200 border \".concat(selectedChart === 'overview' ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md border-transparent' : 'bg-white text-gray-700 hover:text-gray-900 hover:bg-gray-50 border-gray-300 dark:bg-gray-800 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700 dark:border-gray-600'),\n                                children: \"Overview\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedChart('trends'),\n                                className: \"px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-200 border \".concat(selectedChart === 'trends' ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md border-transparent' : 'bg-white text-gray-700 hover:text-gray-900 hover:bg-gray-50 border-gray-300 dark:bg-gray-800 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700 dark:border-gray-600'),\n                                children: \"Monthly Trends\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setSelectedChart('categories'),\n                                className: \"px-6 py-3 rounded-lg text-sm font-semibold transition-all duration-200 border \".concat(selectedChart === 'categories' ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md border-transparent' : 'bg-white text-gray-700 hover:text-gray-900 hover:bg-gray-50 border-gray-300 dark:bg-gray-800 dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700 dark:border-gray-600'),\n                                children: \"Categories\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    selectedChart === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-text-primary mb-4\",\n                                        children: \"Expense Breakdown\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this),\n                                    pieChartData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.ResponsiveContainer, {\n                                                width: \"100%\",\n                                                height: 280,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_4__.PieChart, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_5__.Pie, {\n                                                            data: pieChartData,\n                                                            cx: \"50%\",\n                                                            cy: \"50%\",\n                                                            outerRadius: 80,\n                                                            fill: \"#8884d8\",\n                                                            dataKey: \"value\",\n                                                            children: pieChartData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Cell, {\n                                                                    fill: COLORS[index % COLORS.length]\n                                                                }, \"cell-\".concat(index), false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                                            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PieTooltip, {}, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 41\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm\",\n                                                children: pieChartData.map((entry, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-3 h-3 rounded-full flex-shrink-0\",\n                                                                style: {\n                                                                    backgroundColor: COLORS[index % COLORS.length]\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-text-secondary truncate\",\n                                                                title: entry.name,\n                                                                children: [\n                                                                    entry.name,\n                                                                    \": \",\n                                                                    entry.percentage.toFixed(1),\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                                lineNumber: 287,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, entry.name, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center h-64 text-text-tertiary\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-12 h-12 mx-auto mb-4 opacity-50\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 1,\n                                                        d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"No expense data available\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 296,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 257,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold text-text-primary mb-4\",\n                                        children: \"Income vs Expenses\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.ResponsiveContainer, {\n                                        width: \"100%\",\n                                        height: 300,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.BarChart, {\n                                            data: [\n                                                {\n                                                    name: 'Total',\n                                                    income: data.totalIncome,\n                                                    expenses: data.totalExpenses\n                                                }\n                                            ],\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.CartesianGrid, {\n                                                    strokeDasharray: \"3 3\",\n                                                    stroke: \"var(--border)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.XAxis, {\n                                                    dataKey: \"name\",\n                                                    stroke: \"var(--text-secondary)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {\n                                                    stroke: \"var(--text-secondary)\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                    lineNumber: 313,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                                    content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                        lineNumber: 314,\n                                                        columnNumber: 37\n                                                    }, void 0)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Legend, {}, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                    lineNumber: 315,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Bar, {\n                                                    dataKey: \"income\",\n                                                    fill: \"var(--success-green)\",\n                                                    name: \"Income\",\n                                                    radius: [\n                                                        4,\n                                                        4,\n                                                        0,\n                                                        0\n                                                    ]\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Bar, {\n                                                    dataKey: \"expenses\",\n                                                    fill: \"var(--error-red)\",\n                                                    name: \"Expenses\",\n                                                    radius: [\n                                                        4,\n                                                        4,\n                                                        0,\n                                                        0\n                                                    ]\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, this),\n                    selectedChart === 'trends' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-text-primary mb-4\",\n                                children: \"Monthly Trends\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 326,\n                                columnNumber: 13\n                            }, this),\n                            trendChartData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.ResponsiveContainer, {\n                                width: \"100%\",\n                                height: 400,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_14__.LineChart, {\n                                    data: trendChartData,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.CartesianGrid, {\n                                            strokeDasharray: \"3 3\",\n                                            stroke: \"var(--border)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.XAxis, {\n                                            dataKey: \"month\",\n                                            angle: -45,\n                                            textAnchor: \"end\",\n                                            height: 80,\n                                            stroke: \"var(--text-secondary)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {\n                                            stroke: \"var(--text-secondary)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CustomTooltip, {}, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 339,\n                                                columnNumber: 37\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Legend, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.Line, {\n                                            type: \"monotone\",\n                                            dataKey: \"income\",\n                                            stroke: \"var(--success-green)\",\n                                            name: \"Income\",\n                                            strokeWidth: 3,\n                                            dot: {\n                                                r: 6\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.Line, {\n                                            type: \"monotone\",\n                                            dataKey: \"expenses\",\n                                            stroke: \"var(--error-red)\",\n                                            name: \"Expenses\",\n                                            strokeWidth: 3,\n                                            dot: {\n                                                r: 6\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_15__.Line, {\n                                            type: \"monotone\",\n                                            dataKey: \"net\",\n                                            stroke: \"var(--primary-blue)\",\n                                            name: \"Net\",\n                                            strokeWidth: 3,\n                                            dot: {\n                                                r: 6\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-64 text-text-tertiary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-12 h-12 mx-auto mb-4 opacity-50\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 1,\n                                                d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 350,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No trend data available\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 325,\n                        columnNumber: 11\n                    }, this),\n                    selectedChart === 'categories' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xl font-semibold text-text-primary mb-4\",\n                                children: \"Top Categories\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 361,\n                                columnNumber: 13\n                            }, this),\n                            topCategoriesData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_3__.ResponsiveContainer, {\n                                width: \"100%\",\n                                height: 400,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_8__.BarChart, {\n                                    data: topCategoriesData,\n                                    layout: \"horizontal\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_9__.CartesianGrid, {\n                                            strokeDasharray: \"3 3\",\n                                            stroke: \"var(--border)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_10__.XAxis, {\n                                            type: \"number\",\n                                            stroke: \"var(--text-secondary)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_11__.YAxis, {\n                                            dataKey: \"name\",\n                                            type: \"category\",\n                                            width: 120,\n                                            stroke: \"var(--text-secondary)\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                            formatter: (value, name)=>[\n                                                    name === 'amount' ? formatCurrency(value) : value,\n                                                    name === 'amount' ? 'Amount' : 'Transactions'\n                                                ],\n                                            contentStyle: {\n                                                backgroundColor: 'var(--surface-elevated)',\n                                                border: '1px solid var(--border)',\n                                                borderRadius: 'var(--radius-xl)',\n                                                boxShadow: 'var(--shadow-lg)'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_12__.Legend, {}, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Cell_Legend_Line_LineChart_Pie_PieChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_13__.Bar, {\n                                            dataKey: \"amount\",\n                                            fill: \"var(--primary-blue)\",\n                                            name: \"Amount\",\n                                            radius: [\n                                                0,\n                                                4,\n                                                4,\n                                                0\n                                            ]\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                    lineNumber: 364,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 363,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center h-64 text-text-tertiary\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-12 h-12 mx-auto mb-4 opacity-50\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 1,\n                                                d: \"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No category data available\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            data.topCategories.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-surface-elevated rounded-xl border border-border-light p-6 shadow-sm hover:shadow-md transition-all duration-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-semibold text-text-primary mb-6\",\n                        children: \"Top Spending Categories\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: data.topCategories.map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between p-4 bg-surface rounded-lg hover:bg-surface-elevated transition-colors border border-border-light\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-6 h-6 rounded-full shadow-sm\",\n                                                            style: {\n                                                                backgroundColor: item.category.color\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute -top-1 -right-1 w-5 h-5 bg-text-tertiary rounded-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs font-medium text-surface\",\n                                                                children: [\n                                                                    \"#\",\n                                                                    index + 1\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                            lineNumber: 412,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                    lineNumber: 407,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-semibold text-text-primary\",\n                                                            children: item.category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-text-secondary flex items-center gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    className: \"w-3 h-3\",\n                                                                    fill: \"none\",\n                                                                    stroke: \"currentColor\",\n                                                                    viewBox: \"0 0 24 24\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        strokeLinecap: \"round\",\n                                                                        strokeLinejoin: \"round\",\n                                                                        strokeWidth: 2,\n                                                                        d: \"M9 5H7a2 2 0 00-2 2v8a2 2 0 002 2h2m0 0h2a2 2 0 002-2V7a2 2 0 00-2-2h-2m0 0V5a2 2 0 112 0v.01M9 5a2 2 0 112 0v.01M9 5a2 2 0 00-2 2v3a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2H9z\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                                        lineNumber: 420,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                item.transactionCount,\n                                                                \" transactions\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl font-bold text-text-primary\",\n                                                children: formatCurrency(item.total)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-text-secondary\",\n                                                children: [\n                                                    (item.total / data.totalExpenses * 100).toFixed(1),\n                                                    \"% of expenses\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                                lineNumber: 431,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, item.category.id, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                        lineNumber: 402,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n                lineNumber: 400,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AnalyticsDashboard.tsx\",\n        lineNumber: 124,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsDashboard, \"/Zqdd7VzLVmzfvXGcBVjI9PZsq4=\", false, function() {\n    return [\n        _repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore\n    ];\n});\n_c = AnalyticsDashboard;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsDashboard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AnalyticsDashboard.tsx\n"));

/***/ })

});