"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/./src/components/TransactionTemplates.tsx":
/*!*************************************************!*\
  !*** ./src/components/TransactionTemplates.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionTemplates: () => (/* binding */ TransactionTemplates)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst DEFAULT_TEMPLATES = [\n    {\n        name: 'Coffee',\n        amount: 5,\n        description: 'Daily coffee',\n        transaction_type: 'expense'\n    },\n    {\n        name: 'Lunch',\n        amount: 15,\n        description: 'Lunch break',\n        transaction_type: 'expense'\n    },\n    {\n        name: 'Groceries',\n        amount: 50,\n        description: 'Weekly groceries',\n        transaction_type: 'expense'\n    },\n    {\n        name: 'Gas',\n        amount: 40,\n        description: 'Fuel expense',\n        transaction_type: 'expense'\n    },\n    {\n        name: 'Salary',\n        amount: 3000,\n        description: 'Monthly salary',\n        transaction_type: 'income'\n    }\n];\nfunction TransactionTemplates(param) {\n    let { onUseTemplate, categories: propCategories = [], className = '' } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { formatCurrency } = (0,_repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore)();\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showCreateForm, setShowCreateForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newTemplate, setNewTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        amount: 0,\n        category_id: '',\n        description: '',\n        transaction_type: 'expense',\n        is_recurring: false,\n        frequency: 'monthly',\n        auto_create: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionTemplates.useEffect\": ()=>{\n            if (user) {\n                fetchTemplates();\n            }\n        }\n    }[\"TransactionTemplates.useEffect\"], [\n        user\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionTemplates.useEffect\": ()=>{\n            if (propCategories.length > 0) {\n                setCategories(propCategories);\n            } else if (user) {\n                fetchCategories();\n            }\n        }\n    }[\"TransactionTemplates.useEffect\"], [\n        propCategories,\n        user\n    ]);\n    const fetchTemplates = async ()=>{\n        try {\n            const { data, error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('transaction_templates').select('*').eq('user_id', user.id).order('created_at', {\n                ascending: false\n            });\n            if (error) {\n                console.error('Error fetching templates:', error);\n                return;\n            }\n            setTemplates(data || []);\n        } catch (error) {\n            console.error('Error fetching templates:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchCategories = async ()=>{\n        try {\n            const { data, error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('categories').select('*').eq('user_id', user.id).order('name');\n            if (error) {\n                console.error('Error fetching categories:', error);\n                return;\n            }\n            setCategories(data || []);\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n        }\n    };\n    const createTemplate = async (e)=>{\n        e.preventDefault();\n        if (!user || !newTemplate.name || !newTemplate.category_id) return;\n        try {\n            const { data, error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('transaction_templates').insert({\n                ...newTemplate,\n                user_id: user.id\n            }).select().single();\n            if (error) {\n                console.error('Error creating template:', error);\n                return;\n            }\n            setTemplates((prev)=>[\n                    data,\n                    ...prev\n                ]);\n            setNewTemplate({\n                name: '',\n                amount: 0,\n                category_id: '',\n                description: '',\n                transaction_type: 'expense',\n                is_recurring: false,\n                frequency: 'monthly',\n                auto_create: false\n            });\n            setShowCreateForm(false);\n        } catch (error) {\n            console.error('Error creating template:', error);\n        }\n    };\n    const deleteTemplate = async (templateId)=>{\n        try {\n            const { error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('transaction_templates').delete().eq('id', templateId);\n            if (error) {\n                console.error('Error deleting template:', error);\n                return;\n            }\n            setTemplates((prev)=>prev.filter((t)=>t.id !== templateId));\n        } catch (error) {\n            console.error('Error deleting template:', error);\n        }\n    };\n    const createDefaultTemplatesForUser = async ()=>{\n        if (!user || !categories.length) return;\n        const defaultCategory = categories.find((c)=>c.type === 'expense') || categories[0];\n        const incomeCategory = categories.find((c)=>c.type === 'income') || categories[0];\n        const templatesWithCategories = DEFAULT_TEMPLATES.map((template)=>({\n                ...template,\n                category_id: template.transaction_type === 'income' ? incomeCategory.id : defaultCategory.id,\n                user_id: user.id\n            }));\n        try {\n            const { data, error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('transaction_templates').insert(templatesWithCategories).select();\n            if (error) {\n                console.error('Error creating default templates:', error);\n                return;\n            }\n            setTemplates((prev)=>[\n                    ...data || [],\n                    ...prev\n                ]);\n        } catch (error) {\n            console.error('Error creating default templates:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n                    children: [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5,\n                        6\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-16 bg-gray-200 dark:bg-gray-700 rounded\"\n                        }, i, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-text-primary\",\n                        children: \"Quick Templates\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowCreateForm(!showCreateForm),\n                        className: \"text-sm bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent hover:from-blue-600 hover:to-purple-700 font-medium\",\n                        children: showCreateForm ? 'Cancel' : 'Add Template'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            showCreateForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: createTemplate,\n                className: \"bg-surface-elevated p-4 rounded-lg mb-4 border border-border-light\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Template name\",\n                                value: newTemplate.name,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            name: e.target.value\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                placeholder: \"Amount\",\n                                step: \"0.01\",\n                                value: newTemplate.amount,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            amount: parseFloat(e.target.value) || 0\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: newTemplate.category_id,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            category_id: e.target.value\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                required: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select category\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    categories.filter((category)=>category.type === newTemplate.transaction_type).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: category.id,\n                                            children: [\n                                                category.icon,\n                                                \" \",\n                                                category.name\n                                            ]\n                                        }, category.id, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: newTemplate.transaction_type,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            transaction_type: e.target.value\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"expense\",\n                                        children: \"Expense\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"income\",\n                                        children: \"Income\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: newTemplate.is_recurring,\n                                    onChange: (e)=>setNewTemplate((prev)=>({\n                                                ...prev,\n                                                is_recurring: e.target.checked\n                                            })),\n                                    className: \"rounded border-gray-300 dark:border-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-text-primary\",\n                                    children: \"Make this a recurring transaction\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this),\n                    newTemplate.is_recurring && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-surface rounded-lg border border-border-light\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: newTemplate.frequency,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            frequency: e.target.value\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"weekly\",\n                                        children: \"Weekly\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"monthly\",\n                                        children: \"Monthly\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"yearly\",\n                                        children: \"Yearly\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: newTemplate.auto_create,\n                                        onChange: (e)=>setNewTemplate((prev)=>({\n                                                    ...prev,\n                                                    auto_create: e.target.checked\n                                                })),\n                                        className: \"rounded border-gray-300 dark:border-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-text-primary\",\n                                        children: \"Auto-create transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        placeholder: \"Description (optional)\",\n                        value: newTemplate.description,\n                        onChange: (e)=>setNewTemplate((prev)=>({\n                                    ...prev,\n                                    description: e.target.value\n                                })),\n                        className: \"mt-2 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: \"mt-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-md hover:from-blue-600 hover:to-purple-700 text-sm font-medium transition-all\",\n                        children: \"Create Template\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, this),\n            templates.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-text-secondary mb-4\",\n                        children: \"No templates yet\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: createDefaultTemplatesForUser,\n                        className: \"px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-md hover:from-blue-600 hover:to-purple-700 text-sm font-medium transition-all\",\n                        children: \"Create Default Templates\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                lineNumber: 298,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n                children: templates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative group p-3 bg-surface border border-border-light rounded-lg hover:shadow-md hover:border-blue-300 transition-all cursor-pointer\",\n                        onClick: ()=>onUseTemplate(template),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    deleteTemplate(template.id);\n                                },\n                                className: \"absolute top-1 right-1 opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-700 transition-opacity\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-text-primary\",\n                                        children: template.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this),\n                                    template.is_recurring && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-xs bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3 h-3 mr-1\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this),\n                                            template.frequency\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg font-bold \".concat(template.transaction_type === 'income' ? 'text-green-600' : 'text-red-600'),\n                                children: [\n                                    template.transaction_type === 'income' ? '+' : '-',\n                                    formatCurrency(template.amount)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this),\n                            template.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-text-secondary mt-1 truncate\",\n                                children: template.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this),\n                            template.is_recurring && template.next_due_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-text-secondary mt-1\",\n                                children: [\n                                    \"Next due: \",\n                                    new Date(template.next_due_date).toLocaleDateString(),\n                                    template.auto_create && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 px-1 py-0.5 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 rounded text-xs\",\n                                        children: \"Auto\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, template.id, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n_s(TransactionTemplates, \"kx3fYjJNRN2OPDbOO6b3boSJCD8=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore\n    ];\n});\n_c = TransactionTemplates;\nvar _c;\n$RefreshReg$(_c, \"TransactionTemplates\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TransactionTemplates.tsx\n"));

/***/ })

});