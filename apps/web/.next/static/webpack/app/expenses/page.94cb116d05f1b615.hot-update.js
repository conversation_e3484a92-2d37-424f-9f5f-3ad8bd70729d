"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/./src/components/AccountDropdown.tsx":
/*!********************************************!*\
  !*** ./src/components/AccountDropdown.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AccountDropdown)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(app-pages-browser)/./src/contexts/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction AccountDropdown() {\n    var _user_user_metadata, _user_user_metadata1;\n    _s();\n    const { user, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { theme, setTheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.useTheme)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            setIsOpen(false);\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    const handleThemeChange = (newTheme)=>{\n        setTheme(newTheme);\n    };\n    const getThemeIcon = (currentTheme)=>{\n        switch(currentTheme){\n            case 'light':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this);\n            case 'dark':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        d: \"M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                    lineNumber: 37,\n                    columnNumber: 11\n                }, this);\n            case 'system':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        fillRule: \"evenodd\",\n                        d: \"M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7l.159-1H8a1 1 0 010-2h1.159l.159-1H8a1 1 0 010-2h1.518l.07-.555A1 1 0 0110.604 5H11a1 1 0 110 2h-.396l-.07.555A1 1 0 0011.518 8H13a1 1 0 110 2h-1.518l-.07.555A1 1 0 0010.396 11H10a1 1 0 110 2h.771z\",\n                        clipRule: \"evenodd\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                        lineNumber: 44,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 11\n                }, this);\n            default:\n                return null;\n        }\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AccountDropdown.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"AccountDropdown.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"AccountDropdown.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"AccountDropdown.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"AccountDropdown.useEffect\"];\n        }\n    }[\"AccountDropdown.useEffect\"], []);\n    // Get user initials for avatar\n    const getUserInitials = ()=>{\n        var _user_user_metadata;\n        const name = (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.name) || (user === null || user === void 0 ? void 0 : user.email) || '';\n        return name.split(' ').map((word)=>word[0]).join('').toUpperCase().slice(0, 2);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center gap-3 px-3 py-2 rounded-lg text-text-primary hover:bg-surface transition-all duration-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold\",\n                        children: getUserInitials()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium hidden sm:block truncate max-w-32\",\n                        children: (user === null || user === void 0 ? void 0 : (_user_user_metadata = user.user_metadata) === null || _user_user_metadata === void 0 ? void 0 : _user_user_metadata.name) || (user === null || user === void 0 ? void 0 : user.email)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-4 h-4 text-text-secondary transition-transform duration-200 \".concat(isOpen ? 'rotate-180' : ''),\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 9l-7 7-7-7\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                lineNumber: 80,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-64 bg-surface-elevated border border-border-light rounded-xl shadow-lg z-50 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-border-light bg-surface\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold\",\n                                    children: getUserInitials()\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        (user === null || user === void 0 ? void 0 : (_user_user_metadata1 = user.user_metadata) === null || _user_user_metadata1 === void 0 ? void 0 : _user_user_metadata1.name) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-semibold text-text-primary truncate\",\n                                            children: user.user_metadata.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-text-secondary truncate\",\n                                            children: user === null || user === void 0 ? void 0 : user.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/profile\",\n                                onClick: ()=>setIsOpen(false),\n                                className: \"flex items-center gap-3 px-4 py-2 text-sm text-text-primary hover:bg-surface transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Profile Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs font-semibold text-text-secondary uppercase tracking-wide mb-2\",\n                                        children: \"Theme\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1\",\n                                        children: [\n                                            'light',\n                                            'dark',\n                                            'system'\n                                        ].map((themeOption)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleThemeChange(themeOption),\n                                                className: \"w-full flex items-center gap-3 px-3 py-2 text-sm rounded-lg transition-colors \".concat(theme === themeOption ? 'bg-primary-blue/10 text-primary-blue' : 'text-text-primary hover:bg-surface'),\n                                                children: [\n                                                    getThemeIcon(themeOption),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"capitalize\",\n                                                        children: themeOption\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    theme === themeOption && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 ml-auto\",\n                                                        fill: \"currentColor\",\n                                                        viewBox: \"0 0 20 20\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            fillRule: \"evenodd\",\n                                                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                                                            clipRule: \"evenodd\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                                        lineNumber: 160,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, themeOption, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-t border-border-light my-2\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSignOut,\n                                className: \"w-full flex items-center gap-3 px-4 py-2 text-sm text-error-red hover:bg-error-red/10 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Sign Out\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                lineNumber: 107,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(AccountDropdown, \"UD7fe7+ECAy7sAhAwBKxC21QeO4=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_4__.useTheme\n    ];\n});\n_c = AccountDropdown;\nvar _c;\n$RefreshReg$(_c, \"AccountDropdown\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AccountDropdown.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AccountDropdown__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AccountDropdown */ \"(app-pages-browser)/./src/components/AccountDropdown.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Navbar(param) {\n    let { currentPage } = param;\n    const navItems = [\n        {\n            href: '/dashboard',\n            label: 'Dashboard',\n            key: 'dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 18,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            href: '/expenses',\n            label: 'Expenses',\n            key: 'expenses',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            href: '/budgets',\n            label: 'Budgets',\n            key: 'budgets',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 002 2z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-surface-elevated border-b border-border-light shadow-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/dashboard\",\n                            className: \"flex items-center gap-3 text-xl font-semibold text-text-primary hover:text-text-secondary transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-white\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 114 0 2 2 0 01-4 0zm8-2a2 2 0 100 4 2 2 0 000-4z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 15\n                                }, this),\n                                \"Portfolio Tracker\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-1\",\n                        children: navItems.map((item)=>{\n                            const isActive = item.key === currentPage;\n                            if (isActive) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-2 rounded-lg text-white text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        item.icon,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.key, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 19\n                                }, this);\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                className: \"flex items-center gap-2 px-4 py-2 rounded-lg text-text-secondary hover:text-text-primary hover:bg-surface transition-all\",\n                                children: [\n                                    item.icon,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, item.key, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AccountDropdown__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                lineNumber: 48,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_c = Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Navbar.tsx\n"));

/***/ })

});