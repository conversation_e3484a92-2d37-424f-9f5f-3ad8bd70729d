"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/./src/components/TransactionTemplates.tsx":
/*!*************************************************!*\
  !*** ./src/components/TransactionTemplates.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionTemplates: () => (/* binding */ TransactionTemplates)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst DEFAULT_TEMPLATES = [\n    {\n        name: 'Coffee',\n        amount: 5,\n        description: 'Daily coffee',\n        transaction_type: 'expense'\n    },\n    {\n        name: 'Lunch',\n        amount: 15,\n        description: 'Lunch break',\n        transaction_type: 'expense'\n    },\n    {\n        name: 'Groceries',\n        amount: 50,\n        description: 'Weekly groceries',\n        transaction_type: 'expense'\n    },\n    {\n        name: 'Gas',\n        amount: 40,\n        description: 'Fuel expense',\n        transaction_type: 'expense'\n    },\n    {\n        name: 'Salary',\n        amount: 3000,\n        description: 'Monthly salary',\n        transaction_type: 'income'\n    }\n];\nfunction TransactionTemplates(param) {\n    let { onUseTemplate, categories: propCategories = [], className = '' } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { formatCurrency } = (0,_repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore)();\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showCreateForm, setShowCreateForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newTemplate, setNewTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        amount: 0,\n        category_id: '',\n        description: '',\n        transaction_type: 'expense',\n        is_recurring: false,\n        frequency: 'monthly',\n        auto_create: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionTemplates.useEffect\": ()=>{\n            if (user) {\n                fetchTemplates();\n            }\n        }\n    }[\"TransactionTemplates.useEffect\"], [\n        user\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionTemplates.useEffect\": ()=>{\n            if (propCategories.length > 0) {\n                setCategories(propCategories);\n            } else if (user) {\n                fetchCategories();\n            }\n        }\n    }[\"TransactionTemplates.useEffect\"], [\n        propCategories,\n        user\n    ]);\n    const fetchTemplates = async ()=>{\n        try {\n            const { data, error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('transaction_templates').select('*').eq('user_id', user.id).order('created_at', {\n                ascending: false\n            });\n            if (error) {\n                console.error('Error fetching templates:', error);\n                return;\n            }\n            setTemplates(data || []);\n        } catch (error) {\n            console.error('Error fetching templates:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchCategories = async ()=>{\n        try {\n            const { data, error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('categories').select('*').eq('user_id', user.id).order('name');\n            if (error) {\n                console.error('Error fetching categories:', error);\n                return;\n            }\n            setCategories(data || []);\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n        }\n    };\n    const createTemplate = async (e)=>{\n        e.preventDefault();\n        if (!user || !newTemplate.name || !newTemplate.category_id) return;\n        try {\n            const { data, error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('transaction_templates').insert({\n                ...newTemplate,\n                user_id: user.id\n            }).select().single();\n            if (error) {\n                console.error('Error creating template:', error);\n                return;\n            }\n            setTemplates((prev)=>[\n                    data,\n                    ...prev\n                ]);\n            setNewTemplate({\n                name: '',\n                amount: 0,\n                category_id: '',\n                description: '',\n                transaction_type: 'expense',\n                is_recurring: false,\n                frequency: 'monthly',\n                auto_create: false\n            });\n            setShowCreateForm(false);\n        } catch (error) {\n            console.error('Error creating template:', error);\n        }\n    };\n    const deleteTemplate = async (templateId)=>{\n        try {\n            const { error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('transaction_templates').delete().eq('id', templateId);\n            if (error) {\n                console.error('Error deleting template:', error);\n                return;\n            }\n            setTemplates((prev)=>prev.filter((t)=>t.id !== templateId));\n        } catch (error) {\n            console.error('Error deleting template:', error);\n        }\n    };\n    const createDefaultTemplatesForUser = async ()=>{\n        if (!user || !categories.length) return;\n        const defaultCategory = categories.find((c)=>c.type === 'expense') || categories[0];\n        const incomeCategory = categories.find((c)=>c.type === 'income') || categories[0];\n        const templatesWithCategories = DEFAULT_TEMPLATES.map((template)=>({\n                ...template,\n                category_id: template.transaction_type === 'income' ? incomeCategory.id : defaultCategory.id,\n                user_id: user.id\n            }));\n        try {\n            const { data, error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('transaction_templates').insert(templatesWithCategories).select();\n            if (error) {\n                console.error('Error creating default templates:', error);\n                return;\n            }\n            setTemplates((prev)=>[\n                    ...data || [],\n                    ...prev\n                ]);\n        } catch (error) {\n            console.error('Error creating default templates:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n                    children: [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5,\n                        6\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-16 bg-gray-200 dark:bg-gray-700 rounded\"\n                        }, i, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-text-primary\",\n                        children: \"Quick Templates\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowCreateForm(!showCreateForm),\n                        className: \"text-sm bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent hover:from-blue-600 hover:to-purple-700 font-medium\",\n                        children: showCreateForm ? 'Cancel' : 'Add Template'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            showCreateForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: createTemplate,\n                className: \"bg-surface-elevated p-4 rounded-lg mb-4 border border-border-light\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Template name\",\n                                value: newTemplate.name,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            name: e.target.value\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                placeholder: \"Amount\",\n                                step: \"0.01\",\n                                value: newTemplate.amount,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            amount: parseFloat(e.target.value) || 0\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: newTemplate.category_id,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            category_id: e.target.value\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                required: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select category\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    categories.filter((category)=>category.type === newTemplate.transaction_type).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: category.id,\n                                            children: [\n                                                category.icon,\n                                                \" \",\n                                                category.name\n                                            ]\n                                        }, category.id, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: newTemplate.transaction_type,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            transaction_type: e.target.value\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"expense\",\n                                        children: \"Expense\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"income\",\n                                        children: \"Income\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: newTemplate.is_recurring,\n                                    onChange: (e)=>setNewTemplate((prev)=>({\n                                                ...prev,\n                                                is_recurring: e.target.checked\n                                            })),\n                                    className: \"rounded border-gray-300 dark:border-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-text-primary\",\n                                    children: \"Make this a recurring transaction\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this),\n                    newTemplate.is_recurring && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-surface rounded-lg border border-border-light\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: newTemplate.frequency,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            frequency: e.target.value\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"weekly\",\n                                        children: \"Weekly\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"monthly\",\n                                        children: \"Monthly\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"yearly\",\n                                        children: \"Yearly\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: newTemplate.auto_create,\n                                        onChange: (e)=>setNewTemplate((prev)=>({\n                                                    ...prev,\n                                                    auto_create: e.target.checked\n                                                })),\n                                        className: \"rounded border-gray-300 dark:border-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-text-primary\",\n                                        children: \"Auto-create transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        placeholder: \"Description (optional)\",\n                        value: newTemplate.description,\n                        onChange: (e)=>setNewTemplate((prev)=>({\n                                    ...prev,\n                                    description: e.target.value\n                                })),\n                        className: \"mt-2 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: \"mt-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-md hover:from-blue-600 hover:to-purple-700 text-sm font-medium transition-all\",\n                        children: \"Create Template\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, this),\n            templates.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-text-secondary mb-4\",\n                        children: \"No templates yet\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: createDefaultTemplatesForUser,\n                        className: \"px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-md hover:from-blue-600 hover:to-purple-700 text-sm font-medium transition-all\",\n                        children: \"Create Default Templates\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                lineNumber: 298,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n                children: templates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative group p-3 bg-surface border border-border-light rounded-lg hover:shadow-md hover:border-blue-300 transition-all cursor-pointer\",\n                        onClick: ()=>onUseTemplate(template),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    deleteTemplate(template.id);\n                                },\n                                className: \"absolute top-1 right-1 opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-700 transition-opacity\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-text-primary\",\n                                        children: template.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this),\n                                    template.is_recurring && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-xs bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3 h-3 mr-1\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this),\n                                            template.frequency\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg font-bold \".concat(template.transaction_type === 'income' ? 'text-green-600' : 'text-red-600'),\n                                children: [\n                                    template.transaction_type === 'income' ? '+' : '-',\n                                    \"$\",\n                                    template.amount.toFixed(2)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this),\n                            template.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-text-secondary mt-1 truncate\",\n                                children: template.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this),\n                            template.is_recurring && template.next_due_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-text-secondary mt-1\",\n                                children: [\n                                    \"Next due: \",\n                                    new Date(template.next_due_date).toLocaleDateString(),\n                                    template.auto_create && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 px-1 py-0.5 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 rounded text-xs\",\n                                        children: \"Auto\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, template.id, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n_s(TransactionTemplates, \"kx3fYjJNRN2OPDbOO6b3boSJCD8=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore\n    ];\n});\n_c = TransactionTemplates;\nvar _c;\n$RefreshReg$(_c, \"TransactionTemplates\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1RyYW5zYWN0aW9uVGVtcGxhdGVzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFrRDtBQUNtRztBQUNwRztBQVVqRCxNQUFNTSxvQkFBb0I7SUFDeEI7UUFBRUMsTUFBTTtRQUFVQyxRQUFRO1FBQUdDLGFBQWE7UUFBZ0JDLGtCQUFrQjtJQUFtQjtJQUMvRjtRQUFFSCxNQUFNO1FBQVNDLFFBQVE7UUFBSUMsYUFBYTtRQUFlQyxrQkFBa0I7SUFBbUI7SUFDOUY7UUFBRUgsTUFBTTtRQUFhQyxRQUFRO1FBQUlDLGFBQWE7UUFBb0JDLGtCQUFrQjtJQUFtQjtJQUN2RztRQUFFSCxNQUFNO1FBQU9DLFFBQVE7UUFBSUMsYUFBYTtRQUFnQkMsa0JBQWtCO0lBQW1CO0lBQzdGO1FBQUVILE1BQU07UUFBVUMsUUFBUTtRQUFNQyxhQUFhO1FBQWtCQyxrQkFBa0I7SUFBa0I7Q0FDcEc7QUFFTSxTQUFTQyxxQkFBcUIsS0FBNkY7UUFBN0YsRUFBRUMsYUFBYSxFQUFFQyxZQUFZQyxpQkFBaUIsRUFBRSxFQUFFQyxZQUFZLEVBQUUsRUFBNkIsR0FBN0Y7O0lBQ25DLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUdYLDhEQUFPQTtJQUN4QixNQUFNLEVBQUVZLGNBQWMsRUFBRSxHQUFHYiw4REFBZ0JBO0lBQzNDLE1BQU0sQ0FBQ2MsV0FBV0MsYUFBYSxHQUFHbEIsK0NBQVFBLENBQXlCLEVBQUU7SUFDckUsTUFBTSxDQUFDWSxZQUFZTyxjQUFjLEdBQUduQiwrQ0FBUUEsQ0FBUSxFQUFFO0lBQ3RELE1BQU0sQ0FBQ29CLFNBQVNDLFdBQVcsR0FBR3JCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ3NCLGdCQUFnQkMsa0JBQWtCLEdBQUd2QiwrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUN3QixhQUFhQyxlQUFlLEdBQUd6QiwrQ0FBUUEsQ0FBQztRQUM3Q00sTUFBTTtRQUNOQyxRQUFRO1FBQ1JtQixhQUFhO1FBQ2JsQixhQUFhO1FBQ2JDLGtCQUFrQjtRQUNsQmtCLGNBQWM7UUFDZEMsV0FBVztRQUNYQyxhQUFhO0lBQ2Y7SUFFQTVCLGdEQUFTQTswQ0FBQztZQUNSLElBQUljLE1BQU07Z0JBQ1JlO1lBQ0Y7UUFDRjt5Q0FBRztRQUFDZjtLQUFLO0lBRVRkLGdEQUFTQTswQ0FBQztZQUNSLElBQUlZLGVBQWVrQixNQUFNLEdBQUcsR0FBRztnQkFDN0JaLGNBQWNOO1lBQ2hCLE9BQU8sSUFBSUUsTUFBTTtnQkFDZmlCO1lBQ0Y7UUFDRjt5Q0FBRztRQUFDbkI7UUFBZ0JFO0tBQUs7SUFFekIsTUFBTWUsaUJBQWlCO1FBQ3JCLElBQUk7WUFDRixNQUFNLEVBQUVHLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTWhDLGtEQUFRQSxDQUNuQ2lDLElBQUksQ0FBQyx5QkFDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxXQUFXdEIsS0FBTXVCLEVBQUUsRUFDdEJDLEtBQUssQ0FBQyxjQUFjO2dCQUFFQyxXQUFXO1lBQU07WUFFMUMsSUFBSU4sT0FBTztnQkFDVE8sUUFBUVAsS0FBSyxDQUFDLDZCQUE2QkE7Z0JBQzNDO1lBQ0Y7WUFFQWhCLGFBQWFlLFFBQVEsRUFBRTtRQUN6QixFQUFFLE9BQU9DLE9BQU87WUFDZE8sUUFBUVAsS0FBSyxDQUFDLDZCQUE2QkE7UUFDN0MsU0FBVTtZQUNSYixXQUFXO1FBQ2I7SUFDRjtJQUVBLE1BQU1XLGtCQUFrQjtRQUN0QixJQUFJO1lBQ0YsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1oQyxrREFBUUEsQ0FDbkNpQyxJQUFJLENBQUMsY0FDTEMsTUFBTSxDQUFDLEtBQ1BDLEVBQUUsQ0FBQyxXQUFXdEIsS0FBTXVCLEVBQUUsRUFDdEJDLEtBQUssQ0FBQztZQUVULElBQUlMLE9BQU87Z0JBQ1RPLFFBQVFQLEtBQUssQ0FBQyw4QkFBOEJBO2dCQUM1QztZQUNGO1lBRUFmLGNBQWNjLFFBQVEsRUFBRTtRQUMxQixFQUFFLE9BQU9DLE9BQU87WUFDZE8sUUFBUVAsS0FBSyxDQUFDLDhCQUE4QkE7UUFDOUM7SUFDRjtJQUVBLE1BQU1RLGlCQUFpQixPQUFPQztRQUM1QkEsRUFBRUMsY0FBYztRQUNoQixJQUFJLENBQUM3QixRQUFRLENBQUNTLFlBQVlsQixJQUFJLElBQUksQ0FBQ2tCLFlBQVlFLFdBQVcsRUFBRTtRQUU1RCxJQUFJO1lBQ0YsTUFBTSxFQUFFTyxJQUFJLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1oQyxrREFBUUEsQ0FDbkNpQyxJQUFJLENBQUMseUJBQ0xVLE1BQU0sQ0FBQztnQkFDTixHQUFHckIsV0FBVztnQkFDZHNCLFNBQVMvQixLQUFLdUIsRUFBRTtZQUNsQixHQUNDRixNQUFNLEdBQ05XLE1BQU07WUFFVCxJQUFJYixPQUFPO2dCQUNUTyxRQUFRUCxLQUFLLENBQUMsNEJBQTRCQTtnQkFDMUM7WUFDRjtZQUVBaEIsYUFBYThCLENBQUFBLE9BQVE7b0JBQUNmO3VCQUFpQ2U7aUJBQUs7WUFDNUR2QixlQUFlO2dCQUNibkIsTUFBTTtnQkFDTkMsUUFBUTtnQkFDUm1CLGFBQWE7Z0JBQ2JsQixhQUFhO2dCQUNiQyxrQkFBa0I7Z0JBQ2xCa0IsY0FBYztnQkFDZEMsV0FBVztnQkFDWEMsYUFBYTtZQUNmO1lBQ0FOLGtCQUFrQjtRQUNwQixFQUFFLE9BQU9XLE9BQU87WUFDZE8sUUFBUVAsS0FBSyxDQUFDLDRCQUE0QkE7UUFDNUM7SUFDRjtJQUVBLE1BQU1lLGlCQUFpQixPQUFPQztRQUM1QixJQUFJO1lBQ0YsTUFBTSxFQUFFaEIsS0FBSyxFQUFFLEdBQUcsTUFBTWhDLGtEQUFRQSxDQUM3QmlDLElBQUksQ0FBQyx5QkFDTGdCLE1BQU0sR0FDTmQsRUFBRSxDQUFDLE1BQU1hO1lBRVosSUFBSWhCLE9BQU87Z0JBQ1RPLFFBQVFQLEtBQUssQ0FBQyw0QkFBNEJBO2dCQUMxQztZQUNGO1lBRUFoQixhQUFhOEIsQ0FBQUEsT0FBUUEsS0FBS0ksTUFBTSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFZixFQUFFLEtBQUtZO1FBQ2pELEVBQUUsT0FBT2hCLE9BQU87WUFDZE8sUUFBUVAsS0FBSyxDQUFDLDRCQUE0QkE7UUFDNUM7SUFDRjtJQUVBLE1BQU1vQixnQ0FBZ0M7UUFDcEMsSUFBSSxDQUFDdkMsUUFBUSxDQUFDSCxXQUFXbUIsTUFBTSxFQUFFO1FBRWpDLE1BQU13QixrQkFBa0IzQyxXQUFXNEMsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJLEtBQUssY0FBYzlDLFVBQVUsQ0FBQyxFQUFFO1FBQ25GLE1BQU0rQyxpQkFBaUIvQyxXQUFXNEMsSUFBSSxDQUFDQyxDQUFBQSxJQUFLQSxFQUFFQyxJQUFJLEtBQUssYUFBYTlDLFVBQVUsQ0FBQyxFQUFFO1FBRWpGLE1BQU1nRCwwQkFBMEJ2RCxrQkFBa0J3RCxHQUFHLENBQUNDLENBQUFBLFdBQWE7Z0JBQ2pFLEdBQUdBLFFBQVE7Z0JBQ1hwQyxhQUFhb0MsU0FBU3JELGdCQUFnQixLQUFLLFdBQVdrRCxlQUFlckIsRUFBRSxHQUFHaUIsZ0JBQWdCakIsRUFBRTtnQkFDNUZRLFNBQVMvQixLQUFLdUIsRUFBRTtZQUNsQjtRQUVBLElBQUk7WUFDRixNQUFNLEVBQUVMLElBQUksRUFBRUMsS0FBSyxFQUFFLEdBQUcsTUFBTWhDLGtEQUFRQSxDQUNuQ2lDLElBQUksQ0FBQyx5QkFDTFUsTUFBTSxDQUFDZSx5QkFDUHhCLE1BQU07WUFFVCxJQUFJRixPQUFPO2dCQUNUTyxRQUFRUCxLQUFLLENBQUMscUNBQXFDQTtnQkFDbkQ7WUFDRjtZQUVBaEIsYUFBYThCLENBQUFBLE9BQVE7dUJBQUtmLFFBQVEsRUFBRTt1QkFBZ0NlO2lCQUFLO1FBQzNFLEVBQUUsT0FBT2QsT0FBTztZQUNkTyxRQUFRUCxLQUFLLENBQUMscUNBQXFDQTtRQUNyRDtJQUNGO0lBRUEsSUFBSWQsU0FBUztRQUNYLHFCQUNFLDhEQUFDMkM7WUFBSWpELFdBQVcsaUJBQTJCLE9BQVZBOzs4QkFDL0IsOERBQUNpRDtvQkFBSWpELFdBQVU7Ozs7Ozs4QkFDZiw4REFBQ2lEO29CQUFJakQsV0FBVTs4QkFDWjt3QkFBQzt3QkFBRzt3QkFBRzt3QkFBRzt3QkFBRzt3QkFBRztxQkFBRSxDQUFDK0MsR0FBRyxDQUFDRyxDQUFBQSxrQkFDdEIsOERBQUNEOzRCQUFZakQsV0FBVTsyQkFBYmtEOzs7Ozs7Ozs7Ozs7Ozs7O0lBS3BCO0lBRUEscUJBQ0UsOERBQUNEO1FBQUlqRCxXQUFXQTs7MEJBQ2QsOERBQUNpRDtnQkFBSWpELFdBQVU7O2tDQUNiLDhEQUFDbUQ7d0JBQUduRCxXQUFVO2tDQUF3Qzs7Ozs7O2tDQUN0RCw4REFBQ29EO3dCQUNDQyxTQUFTLElBQU01QyxrQkFBa0IsQ0FBQ0Q7d0JBQ2xDUixXQUFVO2tDQUVUUSxpQkFBaUIsV0FBVzs7Ozs7Ozs7Ozs7O1lBSWhDQSxnQ0FDQyw4REFBQzhDO2dCQUFLQyxVQUFVM0I7Z0JBQWdCNUIsV0FBVTs7a0NBQ3hDLDhEQUFDaUQ7d0JBQUlqRCxXQUFVOzswQ0FDYiw4REFBQ3dEO2dDQUNDWixNQUFLO2dDQUNMYSxhQUFZO2dDQUNaQyxPQUFPaEQsWUFBWWxCLElBQUk7Z0NBQ3ZCbUUsVUFBVSxDQUFDOUIsSUFBTWxCLGVBQWV1QixDQUFBQSxPQUFTOzRDQUFFLEdBQUdBLElBQUk7NENBQUUxQyxNQUFNcUMsRUFBRStCLE1BQU0sQ0FBQ0YsS0FBSzt3Q0FBQztnQ0FDekUxRCxXQUFVO2dDQUNWNkQsUUFBUTs7Ozs7OzBDQUVWLDhEQUFDTDtnQ0FDQ1osTUFBSztnQ0FDTGEsYUFBWTtnQ0FDWkssTUFBSztnQ0FDTEosT0FBT2hELFlBQVlqQixNQUFNO2dDQUN6QmtFLFVBQVUsQ0FBQzlCLElBQU1sQixlQUFldUIsQ0FBQUEsT0FBUzs0Q0FBRSxHQUFHQSxJQUFJOzRDQUFFekMsUUFBUXNFLFdBQVdsQyxFQUFFK0IsTUFBTSxDQUFDRixLQUFLLEtBQUs7d0NBQUU7Z0NBQzVGMUQsV0FBVTtnQ0FDVjZELFFBQVE7Ozs7OzswQ0FFViw4REFBQ3ZDO2dDQUNDb0MsT0FBT2hELFlBQVlFLFdBQVc7Z0NBQzlCK0MsVUFBVSxDQUFDOUIsSUFBTWxCLGVBQWV1QixDQUFBQSxPQUFTOzRDQUFFLEdBQUdBLElBQUk7NENBQUV0QixhQUFhaUIsRUFBRStCLE1BQU0sQ0FBQ0YsS0FBSzt3Q0FBQztnQ0FDaEYxRCxXQUFVO2dDQUNWNkQsUUFBUTs7a0RBRVIsOERBQUNHO3dDQUFPTixPQUFNO2tEQUFHOzs7Ozs7b0NBQ2hCNUQsV0FBV3dDLE1BQU0sQ0FBQzJCLENBQUFBLFdBQVlBLFNBQVNyQixJQUFJLEtBQUtsQyxZQUFZZixnQkFBZ0IsRUFBRW9ELEdBQUcsQ0FBQ2tCLENBQUFBLHlCQUNqRiw4REFBQ0Q7NENBQXlCTixPQUFPTyxTQUFTekMsRUFBRTs7Z0RBQ3pDeUMsU0FBU0MsSUFBSTtnREFBQztnREFBRUQsU0FBU3pFLElBQUk7OzJDQURuQnlFLFNBQVN6QyxFQUFFOzs7Ozs7Ozs7OzswQ0FLNUIsOERBQUNGO2dDQUNDb0MsT0FBT2hELFlBQVlmLGdCQUFnQjtnQ0FDbkNnRSxVQUFVLENBQUM5QixJQUFNbEIsZUFBZXVCLENBQUFBLE9BQVM7NENBQUUsR0FBR0EsSUFBSTs0Q0FBRXZDLGtCQUFrQmtDLEVBQUUrQixNQUFNLENBQUNGLEtBQUs7d0NBQXlCO2dDQUM3RzFELFdBQVU7O2tEQUVWLDhEQUFDZ0U7d0NBQU9OLE9BQU07a0RBQVU7Ozs7OztrREFDeEIsOERBQUNNO3dDQUFPTixPQUFNO2tEQUFTOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSzNCLDhEQUFDVDt3QkFBSWpELFdBQVU7a0NBQ2IsNEVBQUNtRTs0QkFBTW5FLFdBQVU7OzhDQUNmLDhEQUFDd0Q7b0NBQ0NaLE1BQUs7b0NBQ0x3QixTQUFTMUQsWUFBWUcsWUFBWTtvQ0FDakM4QyxVQUFVLENBQUM5QixJQUFNbEIsZUFBZXVCLENBQUFBLE9BQVM7Z0RBQUUsR0FBR0EsSUFBSTtnREFBRXJCLGNBQWNnQixFQUFFK0IsTUFBTSxDQUFDUSxPQUFPOzRDQUFDO29DQUNuRnBFLFdBQVU7Ozs7Ozs4Q0FFWiw4REFBQ3FFO29DQUFLckUsV0FBVTs4Q0FBNEI7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQUkvQ1UsWUFBWUcsWUFBWSxrQkFDdkIsOERBQUNvQzt3QkFBSWpELFdBQVU7OzBDQUNiLDhEQUFDc0I7Z0NBQ0NvQyxPQUFPaEQsWUFBWUksU0FBUztnQ0FDNUI2QyxVQUFVLENBQUM5QixJQUFNbEIsZUFBZXVCLENBQUFBLE9BQVM7NENBQUUsR0FBR0EsSUFBSTs0Q0FBRXBCLFdBQVdlLEVBQUUrQixNQUFNLENBQUNGLEtBQUs7d0NBQXVCO2dDQUNwRzFELFdBQVU7O2tEQUVWLDhEQUFDZ0U7d0NBQU9OLE9BQU07a0RBQVM7Ozs7OztrREFDdkIsOERBQUNNO3dDQUFPTixPQUFNO2tEQUFVOzs7Ozs7a0RBQ3hCLDhEQUFDTTt3Q0FBT04sT0FBTTtrREFBUzs7Ozs7Ozs7Ozs7OzBDQUd6Qiw4REFBQ1M7Z0NBQU1uRSxXQUFVOztrREFDZiw4REFBQ3dEO3dDQUNDWixNQUFLO3dDQUNMd0IsU0FBUzFELFlBQVlLLFdBQVc7d0NBQ2hDNEMsVUFBVSxDQUFDOUIsSUFBTWxCLGVBQWV1QixDQUFBQSxPQUFTO29EQUFFLEdBQUdBLElBQUk7b0RBQUVuQixhQUFhYyxFQUFFK0IsTUFBTSxDQUFDUSxPQUFPO2dEQUFDO3dDQUNsRnBFLFdBQVU7Ozs7OztrREFFWiw4REFBQ3FFO3dDQUFLckUsV0FBVTtrREFBNEI7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLbEQsOERBQUN3RDt3QkFDQ1osTUFBSzt3QkFDTGEsYUFBWTt3QkFDWkMsT0FBT2hELFlBQVloQixXQUFXO3dCQUM5QmlFLFVBQVUsQ0FBQzlCLElBQU1sQixlQUFldUIsQ0FBQUEsT0FBUztvQ0FBRSxHQUFHQSxJQUFJO29DQUFFeEMsYUFBYW1DLEVBQUUrQixNQUFNLENBQUNGLEtBQUs7Z0NBQUM7d0JBQ2hGMUQsV0FBVTs7Ozs7O2tDQUVaLDhEQUFDb0Q7d0JBQ0NSLE1BQUs7d0JBQ0w1QyxXQUFVO2tDQUNYOzs7Ozs7Ozs7Ozs7WUFNSkcsVUFBVWMsTUFBTSxLQUFLLG1CQUNwQiw4REFBQ2dDO2dCQUFJakQsV0FBVTs7a0NBQ2IsOERBQUNzRTt3QkFBRXRFLFdBQVU7a0NBQTJCOzs7Ozs7a0NBQ3hDLDhEQUFDb0Q7d0JBQ0NDLFNBQVNiO3dCQUNUeEMsV0FBVTtrQ0FDWDs7Ozs7Ozs7Ozs7OzBCQU1MLDhEQUFDaUQ7Z0JBQUlqRCxXQUFVOzBCQUNaRyxVQUFVNEMsR0FBRyxDQUFDLENBQUNDLHlCQUNkLDhEQUFDQzt3QkFFQ2pELFdBQVU7d0JBQ1ZxRCxTQUFTLElBQU14RCxjQUFjbUQ7OzBDQUU3Qiw4REFBQ0k7Z0NBQ0NDLFNBQVMsQ0FBQ3hCO29DQUNSQSxFQUFFMEMsZUFBZTtvQ0FDakJwQyxlQUFlYSxTQUFTeEIsRUFBRTtnQ0FDNUI7Z0NBQ0F4QixXQUFVOzBDQUVWLDRFQUFDd0U7b0NBQUl4RSxXQUFVO29DQUFVeUUsTUFBSztvQ0FBZUMsU0FBUTs4Q0FDbkQsNEVBQUNDO3dDQUFLQyxVQUFTO3dDQUFVQyxHQUFFO3dDQUFxTUMsVUFBUzs7Ozs7Ozs7Ozs7Ozs7OzswQ0FHN08sOERBQUM3QjtnQ0FBSWpELFdBQVU7O2tEQUNiLDhEQUFDaUQ7d0NBQUlqRCxXQUFVO2tEQUF5Q2dELFNBQVN4RCxJQUFJOzs7Ozs7b0NBQ3BFd0QsU0FBU25DLFlBQVksa0JBQ3BCLDhEQUFDb0M7d0NBQUlqRCxXQUFVOzswREFDYiw4REFBQ3dFO2dEQUFJeEUsV0FBVTtnREFBZXlFLE1BQUs7Z0RBQWVDLFNBQVE7MERBQ3hELDRFQUFDQztvREFBS0MsVUFBUztvREFBVUMsR0FBRTtvREFBdVNDLFVBQVM7Ozs7Ozs7Ozs7OzRDQUU1VTlCLFNBQVNsQyxTQUFTOzs7Ozs7Ozs7Ozs7OzBDQUl6Qiw4REFBQ21DO2dDQUFJakQsV0FBVyxxQkFBZ0csT0FBM0VnRCxTQUFTckQsZ0JBQWdCLEtBQUssV0FBVyxtQkFBbUI7O29DQUM5RnFELFNBQVNyRCxnQkFBZ0IsS0FBSyxXQUFXLE1BQU07b0NBQUk7b0NBQUVxRCxTQUFTdkQsTUFBTSxDQUFDc0YsT0FBTyxDQUFDOzs7Ozs7OzRCQUUvRS9CLFNBQVN0RCxXQUFXLGtCQUNuQiw4REFBQ3VEO2dDQUFJakQsV0FBVTswQ0FDWmdELFNBQVN0RCxXQUFXOzs7Ozs7NEJBR3hCc0QsU0FBU25DLFlBQVksSUFBSW1DLFNBQVNnQyxhQUFhLGtCQUM5Qyw4REFBQy9CO2dDQUFJakQsV0FBVTs7b0NBQW1DO29DQUNyQyxJQUFJaUYsS0FBS2pDLFNBQVNnQyxhQUFhLEVBQUVFLGtCQUFrQjtvQ0FDN0RsQyxTQUFTakMsV0FBVyxrQkFDbkIsOERBQUNzRDt3Q0FBS3JFLFdBQVU7a0RBQThGOzs7Ozs7Ozs7Ozs7O3VCQXRDL0dnRCxTQUFTeEIsRUFBRTs7Ozs7Ozs7Ozs7Ozs7OztBQWlENUI7R0FwVmdCNUI7O1FBQ0dOLDBEQUFPQTtRQUNHRCwwREFBZ0JBOzs7S0FGN0JPIiwic291cmNlcyI6WyIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL2FwcHMvd2ViL3NyYy9jb21wb25lbnRzL1RyYW5zYWN0aW9uVGVtcGxhdGVzLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgc3VwYWJhc2UsIFJlY3VycmluZ1RyYW5zYWN0aW9uU2VydmljZSwgdHlwZSBJUmVjdXJyaW5nVGVtcGxhdGUsIHR5cGUgUmVjdXJyaW5nRnJlcXVlbmN5LCB0eXBlIFRhYmxlcywgdXNlQ3VycmVuY3lTdG9yZSB9IGZyb20gJ0ByZXBvL3NoYXJlZCdcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICcuLi9jb250ZXh0cy9BdXRoQ29udGV4dCdcblxuZXhwb3J0IHR5cGUgSVRyYW5zYWN0aW9uVGVtcGxhdGUgPSBUYWJsZXM8J3RyYW5zYWN0aW9uX3RlbXBsYXRlcyc+XG5cbmV4cG9ydCBpbnRlcmZhY2UgVHJhbnNhY3Rpb25UZW1wbGF0ZXNQcm9wcyB7XG4gIG9uVXNlVGVtcGxhdGU6ICh0ZW1wbGF0ZTogT21pdDxJVHJhbnNhY3Rpb25UZW1wbGF0ZSwgJ2lkJyB8ICd1c2VyX2lkJyB8ICdjcmVhdGVkX2F0JyB8ICd1cGRhdGVkX2F0Jz4pID0+IHZvaWRcbiAgY2F0ZWdvcmllcz86IGFueVtdXG4gIGNsYXNzTmFtZT86IHN0cmluZ1xufVxuXG5jb25zdCBERUZBVUxUX1RFTVBMQVRFUyA9IFtcbiAgeyBuYW1lOiAnQ29mZmVlJywgYW1vdW50OiA1LCBkZXNjcmlwdGlvbjogJ0RhaWx5IGNvZmZlZScsIHRyYW5zYWN0aW9uX3R5cGU6ICdleHBlbnNlJyBhcyBjb25zdCB9LFxuICB7IG5hbWU6ICdMdW5jaCcsIGFtb3VudDogMTUsIGRlc2NyaXB0aW9uOiAnTHVuY2ggYnJlYWsnLCB0cmFuc2FjdGlvbl90eXBlOiAnZXhwZW5zZScgYXMgY29uc3QgfSxcbiAgeyBuYW1lOiAnR3JvY2VyaWVzJywgYW1vdW50OiA1MCwgZGVzY3JpcHRpb246ICdXZWVrbHkgZ3JvY2VyaWVzJywgdHJhbnNhY3Rpb25fdHlwZTogJ2V4cGVuc2UnIGFzIGNvbnN0IH0sXG4gIHsgbmFtZTogJ0dhcycsIGFtb3VudDogNDAsIGRlc2NyaXB0aW9uOiAnRnVlbCBleHBlbnNlJywgdHJhbnNhY3Rpb25fdHlwZTogJ2V4cGVuc2UnIGFzIGNvbnN0IH0sXG4gIHsgbmFtZTogJ1NhbGFyeScsIGFtb3VudDogMzAwMCwgZGVzY3JpcHRpb246ICdNb250aGx5IHNhbGFyeScsIHRyYW5zYWN0aW9uX3R5cGU6ICdpbmNvbWUnIGFzIGNvbnN0IH0sXG5dXG5cbmV4cG9ydCBmdW5jdGlvbiBUcmFuc2FjdGlvblRlbXBsYXRlcyh7IG9uVXNlVGVtcGxhdGUsIGNhdGVnb3JpZXM6IHByb3BDYXRlZ29yaWVzID0gW10sIGNsYXNzTmFtZSA9ICcnIH06IFRyYW5zYWN0aW9uVGVtcGxhdGVzUHJvcHMpIHtcbiAgY29uc3QgeyB1c2VyIH0gPSB1c2VBdXRoKClcbiAgY29uc3QgeyBmb3JtYXRDdXJyZW5jeSB9ID0gdXNlQ3VycmVuY3lTdG9yZSgpXG4gIGNvbnN0IFt0ZW1wbGF0ZXMsIHNldFRlbXBsYXRlc10gPSB1c2VTdGF0ZTxJVHJhbnNhY3Rpb25UZW1wbGF0ZVtdPihbXSlcbiAgY29uc3QgW2NhdGVnb3JpZXMsIHNldENhdGVnb3JpZXNdID0gdXNlU3RhdGU8YW55W10+KFtdKVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbc2hvd0NyZWF0ZUZvcm0sIHNldFNob3dDcmVhdGVGb3JtXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBbbmV3VGVtcGxhdGUsIHNldE5ld1RlbXBsYXRlXSA9IHVzZVN0YXRlKHtcbiAgICBuYW1lOiAnJyxcbiAgICBhbW91bnQ6IDAsXG4gICAgY2F0ZWdvcnlfaWQ6ICcnLFxuICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICB0cmFuc2FjdGlvbl90eXBlOiAnZXhwZW5zZScgYXMgJ2luY29tZScgfCAnZXhwZW5zZScsXG4gICAgaXNfcmVjdXJyaW5nOiBmYWxzZSxcbiAgICBmcmVxdWVuY3k6ICdtb250aGx5JyBhcyBSZWN1cnJpbmdGcmVxdWVuY3ksXG4gICAgYXV0b19jcmVhdGU6IGZhbHNlXG4gIH0pXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAodXNlcikge1xuICAgICAgZmV0Y2hUZW1wbGF0ZXMoKVxuICAgIH1cbiAgfSwgW3VzZXJdKVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHByb3BDYXRlZ29yaWVzLmxlbmd0aCA+IDApIHtcbiAgICAgIHNldENhdGVnb3JpZXMocHJvcENhdGVnb3JpZXMpXG4gICAgfSBlbHNlIGlmICh1c2VyKSB7XG4gICAgICBmZXRjaENhdGVnb3JpZXMoKVxuICAgIH1cbiAgfSwgW3Byb3BDYXRlZ29yaWVzLCB1c2VyXSlcblxuICBjb25zdCBmZXRjaFRlbXBsYXRlcyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oJ3RyYW5zYWN0aW9uX3RlbXBsYXRlcycpXG4gICAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyIS5pZClcbiAgICAgICAgLm9yZGVyKCdjcmVhdGVkX2F0JywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyB0ZW1wbGF0ZXM6JywgZXJyb3IpXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICBzZXRUZW1wbGF0ZXMoZGF0YSB8fCBbXSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdGVtcGxhdGVzOicsIGVycm9yKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGZldGNoQ2F0ZWdvcmllcyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgLmZyb20oJ2NhdGVnb3JpZXMnKVxuICAgICAgICAuc2VsZWN0KCcqJylcbiAgICAgICAgLmVxKCd1c2VyX2lkJywgdXNlciEuaWQpXG4gICAgICAgIC5vcmRlcignbmFtZScpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBjYXRlZ29yaWVzOicsIGVycm9yKVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgc2V0Q2F0ZWdvcmllcyhkYXRhIHx8IFtdKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBjYXRlZ29yaWVzOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGNyZWF0ZVRlbXBsYXRlID0gYXN5bmMgKGU6IFJlYWN0LkZvcm1FdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKVxuICAgIGlmICghdXNlciB8fCAhbmV3VGVtcGxhdGUubmFtZSB8fCAhbmV3VGVtcGxhdGUuY2F0ZWdvcnlfaWQpIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlXG4gICAgICAgIC5mcm9tKCd0cmFuc2FjdGlvbl90ZW1wbGF0ZXMnKVxuICAgICAgICAuaW5zZXJ0KHtcbiAgICAgICAgICAuLi5uZXdUZW1wbGF0ZSxcbiAgICAgICAgICB1c2VyX2lkOiB1c2VyLmlkXG4gICAgICAgIH0pXG4gICAgICAgIC5zZWxlY3QoKVxuICAgICAgICAuc2luZ2xlKClcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHRlbXBsYXRlOicsIGVycm9yKVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgc2V0VGVtcGxhdGVzKHByZXYgPT4gW2RhdGEgYXMgSVRyYW5zYWN0aW9uVGVtcGxhdGUsIC4uLnByZXZdKVxuICAgICAgc2V0TmV3VGVtcGxhdGUoe1xuICAgICAgICBuYW1lOiAnJyxcbiAgICAgICAgYW1vdW50OiAwLFxuICAgICAgICBjYXRlZ29yeV9pZDogJycsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICAgICAgdHJhbnNhY3Rpb25fdHlwZTogJ2V4cGVuc2UnLFxuICAgICAgICBpc19yZWN1cnJpbmc6IGZhbHNlLFxuICAgICAgICBmcmVxdWVuY3k6ICdtb250aGx5JyxcbiAgICAgICAgYXV0b19jcmVhdGU6IGZhbHNlXG4gICAgICB9KVxuICAgICAgc2V0U2hvd0NyZWF0ZUZvcm0oZmFsc2UpXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIHRlbXBsYXRlOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGRlbGV0ZVRlbXBsYXRlID0gYXN5bmMgKHRlbXBsYXRlSWQ6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgndHJhbnNhY3Rpb25fdGVtcGxhdGVzJylcbiAgICAgICAgLmRlbGV0ZSgpXG4gICAgICAgIC5lcSgnaWQnLCB0ZW1wbGF0ZUlkKVxuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZGVsZXRpbmcgdGVtcGxhdGU6JywgZXJyb3IpXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICBzZXRUZW1wbGF0ZXMocHJldiA9PiBwcmV2LmZpbHRlcih0ID0+IHQuaWQgIT09IHRlbXBsYXRlSWQpKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBkZWxldGluZyB0ZW1wbGF0ZTonLCBlcnJvcilcbiAgICB9XG4gIH1cblxuICBjb25zdCBjcmVhdGVEZWZhdWx0VGVtcGxhdGVzRm9yVXNlciA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXVzZXIgfHwgIWNhdGVnb3JpZXMubGVuZ3RoKSByZXR1cm5cblxuICAgIGNvbnN0IGRlZmF1bHRDYXRlZ29yeSA9IGNhdGVnb3JpZXMuZmluZChjID0+IGMudHlwZSA9PT0gJ2V4cGVuc2UnKSB8fCBjYXRlZ29yaWVzWzBdXG4gICAgY29uc3QgaW5jb21lQ2F0ZWdvcnkgPSBjYXRlZ29yaWVzLmZpbmQoYyA9PiBjLnR5cGUgPT09ICdpbmNvbWUnKSB8fCBjYXRlZ29yaWVzWzBdXG5cbiAgICBjb25zdCB0ZW1wbGF0ZXNXaXRoQ2F0ZWdvcmllcyA9IERFRkFVTFRfVEVNUExBVEVTLm1hcCh0ZW1wbGF0ZSA9PiAoe1xuICAgICAgLi4udGVtcGxhdGUsXG4gICAgICBjYXRlZ29yeV9pZDogdGVtcGxhdGUudHJhbnNhY3Rpb25fdHlwZSA9PT0gJ2luY29tZScgPyBpbmNvbWVDYXRlZ29yeS5pZCA6IGRlZmF1bHRDYXRlZ29yeS5pZCxcbiAgICAgIHVzZXJfaWQ6IHVzZXIuaWRcbiAgICB9KSlcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgndHJhbnNhY3Rpb25fdGVtcGxhdGVzJylcbiAgICAgICAgLmluc2VydCh0ZW1wbGF0ZXNXaXRoQ2F0ZWdvcmllcylcbiAgICAgICAgLnNlbGVjdCgpXG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBkZWZhdWx0IHRlbXBsYXRlczonLCBlcnJvcilcbiAgICAgICAgcmV0dXJuXG4gICAgICB9XG5cbiAgICAgIHNldFRlbXBsYXRlcyhwcmV2ID0+IFsuLi4oZGF0YSB8fCBbXSBhcyBJVHJhbnNhY3Rpb25UZW1wbGF0ZVtdKSwgLi4ucHJldl0pXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGNyZWF0aW5nIGRlZmF1bHQgdGVtcGxhdGVzOicsIGVycm9yKVxuICAgIH1cbiAgfVxuXG4gIGlmIChsb2FkaW5nKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgYW5pbWF0ZS1wdWxzZSAke2NsYXNzTmFtZX1gfT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTggYmctZ3JheS0yMDAgZGFyazpiZy1ncmF5LTcwMCByb3VuZGVkIG1iLTRcIj48L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0yIG1kOmdyaWQtY29scy0zIGdhcC0yXCI+XG4gICAgICAgICAge1sxLCAyLCAzLCA0LCA1LCA2XS5tYXAoaSA9PiAoXG4gICAgICAgICAgICA8ZGl2IGtleT17aX0gY2xhc3NOYW1lPVwiaC0xNiBiZy1ncmF5LTIwMCBkYXJrOmJnLWdyYXktNzAwIHJvdW5kZWRcIj48L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPXtjbGFzc05hbWV9PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbWItNFwiPlxuICAgICAgICA8aDMgY2xhc3NOYW1lPVwidGV4dC1sZyBmb250LW1lZGl1bSB0ZXh0LXRleHQtcHJpbWFyeVwiPlF1aWNrIFRlbXBsYXRlczwvaDM+XG4gICAgICAgIDxidXR0b25cbiAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRTaG93Q3JlYXRlRm9ybSghc2hvd0NyZWF0ZUZvcm0pfVxuICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAwIHRvLXB1cnBsZS02MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnQgaG92ZXI6ZnJvbS1ibHVlLTYwMCBob3Zlcjp0by1wdXJwbGUtNzAwIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgPlxuICAgICAgICAgIHtzaG93Q3JlYXRlRm9ybSA/ICdDYW5jZWwnIDogJ0FkZCBUZW1wbGF0ZSd9XG4gICAgICAgIDwvYnV0dG9uPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHtzaG93Q3JlYXRlRm9ybSAmJiAoXG4gICAgICAgIDxmb3JtIG9uU3VibWl0PXtjcmVhdGVUZW1wbGF0ZX0gY2xhc3NOYW1lPVwiYmctc3VyZmFjZS1lbGV2YXRlZCBwLTQgcm91bmRlZC1sZyBtYi00IGJvcmRlciBib3JkZXItYm9yZGVyLWxpZ2h0XCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIlRlbXBsYXRlIG5hbWVcIlxuICAgICAgICAgICAgICB2YWx1ZT17bmV3VGVtcGxhdGUubmFtZX1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdUZW1wbGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIG5hbWU6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0yIGJvcmRlciBib3JkZXItYm9yZGVyLWxpZ2h0IHJvdW5kZWQtbWQgYmctc3VyZmFjZSB0ZXh0LXRleHQtcHJpbWFyeVwiXG4gICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIkFtb3VudFwiXG4gICAgICAgICAgICAgIHN0ZXA9XCIwLjAxXCJcbiAgICAgICAgICAgICAgdmFsdWU9e25ld1RlbXBsYXRlLmFtb3VudH1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdUZW1wbGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIGFtb3VudDogcGFyc2VGbG9hdChlLnRhcmdldC52YWx1ZSkgfHwgMCB9KSl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWJvcmRlci1saWdodCByb3VuZGVkLW1kIGJnLXN1cmZhY2UgdGV4dC10ZXh0LXByaW1hcnlcIlxuICAgICAgICAgICAgICByZXF1aXJlZFxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgdmFsdWU9e25ld1RlbXBsYXRlLmNhdGVnb3J5X2lkfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE5ld1RlbXBsYXRlKHByZXYgPT4gKHsgLi4ucHJldiwgY2F0ZWdvcnlfaWQ6IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0yIGJvcmRlciBib3JkZXItYm9yZGVyLWxpZ2h0IHJvdW5kZWQtbWQgYmctc3VyZmFjZSB0ZXh0LXRleHQtcHJpbWFyeVwiXG4gICAgICAgICAgICAgIHJlcXVpcmVkXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJcIj5TZWxlY3QgY2F0ZWdvcnk8L29wdGlvbj5cbiAgICAgICAgICAgICAge2NhdGVnb3JpZXMuZmlsdGVyKGNhdGVnb3J5ID0+IGNhdGVnb3J5LnR5cGUgPT09IG5ld1RlbXBsYXRlLnRyYW5zYWN0aW9uX3R5cGUpLm1hcChjYXRlZ29yeSA9PiAoXG4gICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2NhdGVnb3J5LmlkfSB2YWx1ZT17Y2F0ZWdvcnkuaWR9PlxuICAgICAgICAgICAgICAgICAge2NhdGVnb3J5Lmljb259IHtjYXRlZ29yeS5uYW1lfVxuICAgICAgICAgICAgICAgIDwvb3B0aW9uPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICB2YWx1ZT17bmV3VGVtcGxhdGUudHJhbnNhY3Rpb25fdHlwZX1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXROZXdUZW1wbGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIHRyYW5zYWN0aW9uX3R5cGU6IGUudGFyZ2V0LnZhbHVlIGFzICdpbmNvbWUnIHwgJ2V4cGVuc2UnIH0pKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0yIGJvcmRlciBib3JkZXItYm9yZGVyLWxpZ2h0IHJvdW5kZWQtbWQgYmctc3VyZmFjZSB0ZXh0LXRleHQtcHJpbWFyeVwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJleHBlbnNlXCI+RXhwZW5zZTwvb3B0aW9uPlxuICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiaW5jb21lXCI+SW5jb21lPC9vcHRpb24+XG4gICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICB7LyogUmVjdXJyaW5nIE9wdGlvbnMgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb2wtc3Bhbi0yXCI+XG4gICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yXCI+XG4gICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgY2hlY2tlZD17bmV3VGVtcGxhdGUuaXNfcmVjdXJyaW5nfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3VGVtcGxhdGUocHJldiA9PiAoeyAuLi5wcmV2LCBpc19yZWN1cnJpbmc6IGUudGFyZ2V0LmNoZWNrZWQgfSkpfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInJvdW5kZWQgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwXCJcbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LXRleHQtcHJpbWFyeVwiPk1ha2UgdGhpcyBhIHJlY3VycmluZyB0cmFuc2FjdGlvbjwvc3Bhbj5cbiAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgXG4gICAgICAgICAge25ld1RlbXBsYXRlLmlzX3JlY3VycmluZyAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvbC1zcGFuLTIgZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNCBwLTQgYmctc3VyZmFjZSByb3VuZGVkLWxnIGJvcmRlciBib3JkZXItYm9yZGVyLWxpZ2h0XCI+XG4gICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICB2YWx1ZT17bmV3VGVtcGxhdGUuZnJlcXVlbmN5fVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3VGVtcGxhdGUocHJldiA9PiAoeyAuLi5wcmV2LCBmcmVxdWVuY3k6IGUudGFyZ2V0LnZhbHVlIGFzIFJlY3VycmluZ0ZyZXF1ZW5jeSB9KSl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtMyBweS0yIGJvcmRlciBib3JkZXItYm9yZGVyLWxpZ2h0IHJvdW5kZWQtbWQgYmctc3VyZmFjZSB0ZXh0LXRleHQtcHJpbWFyeVwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwid2Vla2x5XCI+V2Vla2x5PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIm1vbnRobHlcIj5Nb250aGx5PC9vcHRpb24+XG4gICAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cInllYXJseVwiPlllYXJseTwvb3B0aW9uPlxuICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgICAgICBjaGVja2VkPXtuZXdUZW1wbGF0ZS5hdXRvX2NyZWF0ZX1cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0TmV3VGVtcGxhdGUocHJldiA9PiAoeyAuLi5wcmV2LCBhdXRvX2NyZWF0ZTogZS50YXJnZXQuY2hlY2tlZCB9KSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJyb3VuZGVkIGJvcmRlci1ncmF5LTMwMCBkYXJrOmJvcmRlci1ncmF5LTYwMFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtdGV4dC1wcmltYXJ5XCI+QXV0by1jcmVhdGUgdHJhbnNhY3Rpb25zPC9zcGFuPlxuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgKX1cbiAgICAgICAgICBcbiAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwiRGVzY3JpcHRpb24gKG9wdGlvbmFsKVwiXG4gICAgICAgICAgICB2YWx1ZT17bmV3VGVtcGxhdGUuZGVzY3JpcHRpb259XG4gICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldE5ld1RlbXBsYXRlKHByZXYgPT4gKHsgLi4ucHJldiwgZGVzY3JpcHRpb246IGUudGFyZ2V0LnZhbHVlIH0pKX1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTIgdy1mdWxsIHB4LTMgcHktMiBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbWQgYmctd2hpdGUgZGFyazpiZy1ncmF5LTcwMCB0ZXh0LWdyYXktOTAwIGRhcms6dGV4dC1ncmF5LTEwMFwiXG4gICAgICAgICAgLz5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cIm10LTIgcHgtNCBweS0yIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwMCB0by1wdXJwbGUtNjAwIHRleHQtd2hpdGUgcm91bmRlZC1tZCBob3Zlcjpmcm9tLWJsdWUtNjAwIGhvdmVyOnRvLXB1cnBsZS03MDAgdGV4dC1zbSBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWFsbFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgQ3JlYXRlIFRlbXBsYXRlXG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZm9ybT5cbiAgICAgICl9XG5cbiAgICAgIHt0ZW1wbGF0ZXMubGVuZ3RoID09PSAwICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS04XCI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC10ZXh0LXNlY29uZGFyeSBtYi00XCI+Tm8gdGVtcGxhdGVzIHlldDwvcD5cbiAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICBvbkNsaWNrPXtjcmVhdGVEZWZhdWx0VGVtcGxhdGVzRm9yVXNlcn1cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cInB4LTQgcHktMiBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8tcHVycGxlLTYwMCB0ZXh0LXdoaXRlIHJvdW5kZWQtbWQgaG92ZXI6ZnJvbS1ibHVlLTYwMCBob3Zlcjp0by1wdXJwbGUtNzAwIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1hbGxcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIENyZWF0ZSBEZWZhdWx0IFRlbXBsYXRlc1xuICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtMyBnYXAtMlwiPlxuICAgICAgICB7dGVtcGxhdGVzLm1hcCgodGVtcGxhdGUpID0+IChcbiAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICBrZXk9e3RlbXBsYXRlLmlkfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicmVsYXRpdmUgZ3JvdXAgcC0zIGJnLXN1cmZhY2UgYm9yZGVyIGJvcmRlci1ib3JkZXItbGlnaHQgcm91bmRlZC1sZyBob3ZlcjpzaGFkb3ctbWQgaG92ZXI6Ym9yZGVyLWJsdWUtMzAwIHRyYW5zaXRpb24tYWxsIGN1cnNvci1wb2ludGVyXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IG9uVXNlVGVtcGxhdGUodGVtcGxhdGUpfVxuICAgICAgICAgID5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17KGUpID0+IHtcbiAgICAgICAgICAgICAgICBlLnN0b3BQcm9wYWdhdGlvbigpXG4gICAgICAgICAgICAgICAgZGVsZXRlVGVtcGxhdGUodGVtcGxhdGUuaWQpXG4gICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xIHJpZ2h0LTEgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRleHQtcmVkLTUwMCBob3Zlcjp0ZXh0LXJlZC03MDAgdHJhbnNpdGlvbi1vcGFjaXR5XCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgZmlsbD1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjAgMjBcIj5cbiAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTQuMjkzIDQuMjkzYTEgMSAwIDAxMS40MTQgMEwxMCA4LjU4Nmw0LjI5My00LjI5M2ExIDEgMCAxMTEuNDE0IDEuNDE0TDExLjQxNCAxMGw0LjI5MyA0LjI5M2ExIDEgMCAwMS0xLjQxNCAxLjQxNEwxMCAxMS40MTRsLTQuMjkzIDQuMjkzYTEgMSAwIDAxLTEuNDE0LTEuNDE0TDguNTg2IDEwIDQuMjkzIDUuNzA3YTEgMSAwIDAxMC0xLjQxNHpcIiBjbGlwUnVsZT1cImV2ZW5vZGRcIiAvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtdGV4dC1wcmltYXJ5XCI+e3RlbXBsYXRlLm5hbWV9PC9kaXY+XG4gICAgICAgICAgICAgIHt0ZW1wbGF0ZS5pc19yZWN1cnJpbmcgJiYgKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgdGV4dC14cyBiZy1ncmFkaWVudC10by1yIGZyb20tYmx1ZS01MDAgdG8tcHVycGxlLTYwMCBiZy1jbGlwLXRleHQgdGV4dC10cmFuc3BhcmVudFwiPlxuICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJ3LTMgaC0zIG1yLTFcIiBmaWxsPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyMCAyMFwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBmaWxsUnVsZT1cImV2ZW5vZGRcIiBkPVwiTTQgMmExIDEgMCAwMTEgMXYyLjEwMWE3LjAwMiA3LjAwMiAwIDAxMTEuNjAxIDIuNTY2IDEgMSAwIDExLTEuODg1LjY2NkE1LjAwMiA1LjAwMiAwIDAwNS45OTkgN0g5YTEgMSAwIDAxMCAySDRhMSAxIDAgMDEtMS0xVjNhMSAxIDAgMDExLTF6bS4wMDggOS4wNTdhMSAxIDAgMDExLjI3Ni42MUE1LjAwMiA1LjAwMiAwIDAwMTQuMDAxIDEzSDExYTEgMSAwIDExMC0yaDVhMSAxIDAgMDExIDF2NWExIDEgMCAxMS0yIDB2LTIuMTAxYTcuMDAyIDcuMDAyIDAgMDEtMTEuNjAxLTIuNTY2IDEgMSAwIDAxLjYxLTEuMjc2elwiIGNsaXBSdWxlPVwiZXZlbm9kZFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgICAgICAgIHt0ZW1wbGF0ZS5mcmVxdWVuY3l9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdGV4dC1sZyBmb250LWJvbGQgJHt0ZW1wbGF0ZS50cmFuc2FjdGlvbl90eXBlID09PSAnaW5jb21lJyA/ICd0ZXh0LWdyZWVuLTYwMCcgOiAndGV4dC1yZWQtNjAwJ31gfT5cbiAgICAgICAgICAgICAge3RlbXBsYXRlLnRyYW5zYWN0aW9uX3R5cGUgPT09ICdpbmNvbWUnID8gJysnIDogJy0nfSR7dGVtcGxhdGUuYW1vdW50LnRvRml4ZWQoMil9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIHt0ZW1wbGF0ZS5kZXNjcmlwdGlvbiAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXRleHQtc2Vjb25kYXJ5IG10LTEgdHJ1bmNhdGVcIj5cbiAgICAgICAgICAgICAgICB7dGVtcGxhdGUuZGVzY3JpcHRpb259XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIHt0ZW1wbGF0ZS5pc19yZWN1cnJpbmcgJiYgdGVtcGxhdGUubmV4dF9kdWVfZGF0ZSAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXRleHQtc2Vjb25kYXJ5IG10LTFcIj5cbiAgICAgICAgICAgICAgICBOZXh0IGR1ZToge25ldyBEYXRlKHRlbXBsYXRlLm5leHRfZHVlX2RhdGUpLnRvTG9jYWxlRGF0ZVN0cmluZygpfVxuICAgICAgICAgICAgICAgIHt0ZW1wbGF0ZS5hdXRvX2NyZWF0ZSAmJiAoXG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHB4LTEgcHktMC41IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTEwMCB0by1wdXJwbGUtMTAwIHRleHQtYmx1ZS03MDAgcm91bmRlZCB0ZXh0LXhzXCI+XG4gICAgICAgICAgICAgICAgICAgIEF1dG9cbiAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkpfVxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn0iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInN1cGFiYXNlIiwidXNlQ3VycmVuY3lTdG9yZSIsInVzZUF1dGgiLCJERUZBVUxUX1RFTVBMQVRFUyIsIm5hbWUiLCJhbW91bnQiLCJkZXNjcmlwdGlvbiIsInRyYW5zYWN0aW9uX3R5cGUiLCJUcmFuc2FjdGlvblRlbXBsYXRlcyIsIm9uVXNlVGVtcGxhdGUiLCJjYXRlZ29yaWVzIiwicHJvcENhdGVnb3JpZXMiLCJjbGFzc05hbWUiLCJ1c2VyIiwiZm9ybWF0Q3VycmVuY3kiLCJ0ZW1wbGF0ZXMiLCJzZXRUZW1wbGF0ZXMiLCJzZXRDYXRlZ29yaWVzIiwibG9hZGluZyIsInNldExvYWRpbmciLCJzaG93Q3JlYXRlRm9ybSIsInNldFNob3dDcmVhdGVGb3JtIiwibmV3VGVtcGxhdGUiLCJzZXROZXdUZW1wbGF0ZSIsImNhdGVnb3J5X2lkIiwiaXNfcmVjdXJyaW5nIiwiZnJlcXVlbmN5IiwiYXV0b19jcmVhdGUiLCJmZXRjaFRlbXBsYXRlcyIsImxlbmd0aCIsImZldGNoQ2F0ZWdvcmllcyIsImRhdGEiLCJlcnJvciIsImZyb20iLCJzZWxlY3QiLCJlcSIsImlkIiwib3JkZXIiLCJhc2NlbmRpbmciLCJjb25zb2xlIiwiY3JlYXRlVGVtcGxhdGUiLCJlIiwicHJldmVudERlZmF1bHQiLCJpbnNlcnQiLCJ1c2VyX2lkIiwic2luZ2xlIiwicHJldiIsImRlbGV0ZVRlbXBsYXRlIiwidGVtcGxhdGVJZCIsImRlbGV0ZSIsImZpbHRlciIsInQiLCJjcmVhdGVEZWZhdWx0VGVtcGxhdGVzRm9yVXNlciIsImRlZmF1bHRDYXRlZ29yeSIsImZpbmQiLCJjIiwidHlwZSIsImluY29tZUNhdGVnb3J5IiwidGVtcGxhdGVzV2l0aENhdGVnb3JpZXMiLCJtYXAiLCJ0ZW1wbGF0ZSIsImRpdiIsImkiLCJoMyIsImJ1dHRvbiIsIm9uQ2xpY2siLCJmb3JtIiwib25TdWJtaXQiLCJpbnB1dCIsInBsYWNlaG9sZGVyIiwidmFsdWUiLCJvbkNoYW5nZSIsInRhcmdldCIsInJlcXVpcmVkIiwic3RlcCIsInBhcnNlRmxvYXQiLCJvcHRpb24iLCJjYXRlZ29yeSIsImljb24iLCJsYWJlbCIsImNoZWNrZWQiLCJzcGFuIiwicCIsInN0b3BQcm9wYWdhdGlvbiIsInN2ZyIsImZpbGwiLCJ2aWV3Qm94IiwicGF0aCIsImZpbGxSdWxlIiwiZCIsImNsaXBSdWxlIiwidG9GaXhlZCIsIm5leHRfZHVlX2RhdGUiLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TransactionTemplates.tsx\n"));

/***/ })

});