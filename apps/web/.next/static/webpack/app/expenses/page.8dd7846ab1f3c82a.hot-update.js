"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/./src/components/TransactionTemplates.tsx":
/*!*************************************************!*\
  !*** ./src/components/TransactionTemplates.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionTemplates: () => (/* binding */ TransactionTemplates)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\nconst DEFAULT_TEMPLATES = [\n    {\n        name: 'Coffee',\n        amount: 5,\n        description: 'Daily coffee',\n        transaction_type: 'expense'\n    },\n    {\n        name: 'Lunch',\n        amount: 15,\n        description: 'Lunch break',\n        transaction_type: 'expense'\n    },\n    {\n        name: 'Groceries',\n        amount: 50,\n        description: 'Weekly groceries',\n        transaction_type: 'expense'\n    },\n    {\n        name: 'Gas',\n        amount: 40,\n        description: 'Fuel expense',\n        transaction_type: 'expense'\n    },\n    {\n        name: 'Salary',\n        amount: 3000,\n        description: 'Monthly salary',\n        transaction_type: 'income'\n    }\n];\nfunction TransactionTemplates(param) {\n    let { onUseTemplate, categories: propCategories = [], className = '' } = param;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { formatCurrency } = (0,_repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore)();\n    const [templates, setTemplates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showCreateForm, setShowCreateForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newTemplate, setNewTemplate] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        amount: 0,\n        category_id: '',\n        description: '',\n        transaction_type: 'expense',\n        is_recurring: false,\n        frequency: 'monthly',\n        auto_create: false\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionTemplates.useEffect\": ()=>{\n            if (user) {\n                fetchTemplates();\n            }\n        }\n    }[\"TransactionTemplates.useEffect\"], [\n        user\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionTemplates.useEffect\": ()=>{\n            if (propCategories.length > 0) {\n                setCategories(propCategories);\n            } else if (user) {\n                fetchCategories();\n            }\n        }\n    }[\"TransactionTemplates.useEffect\"], [\n        propCategories,\n        user\n    ]);\n    const fetchTemplates = async ()=>{\n        try {\n            const { data, error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('transaction_templates').select('*').eq('user_id', user.id).order('created_at', {\n                ascending: false\n            });\n            if (error) {\n                console.error('Error fetching templates:', error);\n                return;\n            }\n            setTemplates(data || []);\n        } catch (error) {\n            console.error('Error fetching templates:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchCategories = async ()=>{\n        try {\n            const { data, error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('categories').select('*').eq('user_id', user.id).order('name');\n            if (error) {\n                console.error('Error fetching categories:', error);\n                return;\n            }\n            setCategories(data || []);\n        } catch (error) {\n            console.error('Error fetching categories:', error);\n        }\n    };\n    const createTemplate = async (e)=>{\n        e.preventDefault();\n        if (!user || !newTemplate.name || !newTemplate.category_id) return;\n        try {\n            const { data, error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('transaction_templates').insert({\n                ...newTemplate,\n                user_id: user.id\n            }).select().single();\n            if (error) {\n                console.error('Error creating template:', error);\n                return;\n            }\n            setTemplates((prev)=>[\n                    data,\n                    ...prev\n                ]);\n            setNewTemplate({\n                name: '',\n                amount: 0,\n                category_id: '',\n                description: '',\n                transaction_type: 'expense',\n                is_recurring: false,\n                frequency: 'monthly',\n                auto_create: false\n            });\n            setShowCreateForm(false);\n        } catch (error) {\n            console.error('Error creating template:', error);\n        }\n    };\n    const deleteTemplate = async (templateId)=>{\n        try {\n            const { error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('transaction_templates').delete().eq('id', templateId);\n            if (error) {\n                console.error('Error deleting template:', error);\n                return;\n            }\n            setTemplates((prev)=>prev.filter((t)=>t.id !== templateId));\n        } catch (error) {\n            console.error('Error deleting template:', error);\n        }\n    };\n    const createDefaultTemplatesForUser = async ()=>{\n        if (!user || !categories.length) return;\n        const defaultCategory = categories.find((c)=>c.type === 'expense') || categories[0];\n        const incomeCategory = categories.find((c)=>c.type === 'income') || categories[0];\n        const templatesWithCategories = DEFAULT_TEMPLATES.map((template)=>({\n                ...template,\n                category_id: template.transaction_type === 'income' ? incomeCategory.id : defaultCategory.id,\n                user_id: user.id\n            }));\n        try {\n            const { data, error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.from('transaction_templates').insert(templatesWithCategories).select();\n            if (error) {\n                console.error('Error creating default templates:', error);\n                return;\n            }\n            setTemplates((prev)=>[\n                    ...data || [],\n                    ...prev\n                ]);\n        } catch (error) {\n            console.error('Error creating default templates:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-pulse \".concat(className),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-8 bg-gray-200 dark:bg-gray-700 rounded mb-4\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                    lineNumber: 179,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n                    children: [\n                        1,\n                        2,\n                        3,\n                        4,\n                        5,\n                        6\n                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-16 bg-gray-200 dark:bg-gray-700 rounded\"\n                        }, i, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n            lineNumber: 178,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: className,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-medium text-text-primary\",\n                        children: \"Quick Templates\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 192,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setShowCreateForm(!showCreateForm),\n                        className: \"text-sm bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent hover:from-blue-600 hover:to-purple-700 font-medium\",\n                        children: showCreateForm ? 'Cancel' : 'Add Template'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            showCreateForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: createTemplate,\n                className: \"bg-surface-elevated p-4 rounded-lg mb-4 border border-border-light\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                placeholder: \"Template name\",\n                                value: newTemplate.name,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            name: e.target.value\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"number\",\n                                placeholder: \"Amount\",\n                                step: \"0.01\",\n                                value: newTemplate.amount,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            amount: parseFloat(e.target.value) || 0\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                required: true\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: newTemplate.category_id,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            category_id: e.target.value\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                required: true,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"Select category\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    categories.filter((category)=>category.type === newTemplate.transaction_type).map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: category.id,\n                                            children: [\n                                                category.icon,\n                                                \" \",\n                                                category.name\n                                            ]\n                                        }, category.id, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 17\n                                        }, this))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: newTemplate.transaction_type,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            transaction_type: e.target.value\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"expense\",\n                                        children: \"Expense\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"income\",\n                                        children: \"Income\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"checkbox\",\n                                    checked: newTemplate.is_recurring,\n                                    onChange: (e)=>setNewTemplate((prev)=>({\n                                                ...prev,\n                                                is_recurring: e.target.checked\n                                            })),\n                                    className: \"rounded border-gray-300 dark:border-gray-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                    lineNumber: 247,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm text-text-primary\",\n                                    children: \"Make this a recurring transaction\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 245,\n                        columnNumber: 11\n                    }, this),\n                    newTemplate.is_recurring && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"col-span-2 grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-surface rounded-lg border border-border-light\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                value: newTemplate.frequency,\n                                onChange: (e)=>setNewTemplate((prev)=>({\n                                            ...prev,\n                                            frequency: e.target.value\n                                        })),\n                                className: \"px-3 py-2 border border-border-light rounded-md bg-surface text-text-primary\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"weekly\",\n                                        children: \"Weekly\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"monthly\",\n                                        children: \"Monthly\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"yearly\",\n                                        children: \"Yearly\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        checked: newTemplate.auto_create,\n                                        onChange: (e)=>setNewTemplate((prev)=>({\n                                                    ...prev,\n                                                    auto_create: e.target.checked\n                                                })),\n                                        className: \"rounded border-gray-300 dark:border-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-text-primary\",\n                                        children: \"Auto-create transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                        type: \"text\",\n                        placeholder: \"Description (optional)\",\n                        value: newTemplate.description,\n                        onChange: (e)=>setNewTemplate((prev)=>({\n                                    ...prev,\n                                    description: e.target.value\n                                })),\n                        className: \"mt-2 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        type: \"submit\",\n                        className: \"mt-2 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-md hover:from-blue-600 hover:to-purple-700 text-sm font-medium transition-all\",\n                        children: \"Create Template\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                lineNumber: 202,\n                columnNumber: 9\n            }, this),\n            templates.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-text-secondary mb-4\",\n                        children: \"No templates yet\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 299,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: createDefaultTemplatesForUser,\n                        className: \"px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-md hover:from-blue-600 hover:to-purple-700 text-sm font-medium transition-all\",\n                        children: \"Create Default Templates\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                lineNumber: 298,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 md:grid-cols-3 gap-2\",\n                children: templates.map((template)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative group p-3 bg-surface border border-border-light rounded-lg hover:shadow-md hover:border-blue-300 transition-all cursor-pointer\",\n                        onClick: ()=>onUseTemplate(template),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: (e)=>{\n                                    e.stopPropagation();\n                                    deleteTemplate(template.id);\n                                },\n                                className: \"absolute top-1 right-1 opacity-0 group-hover:opacity-100 text-red-500 hover:text-red-700 transition-opacity\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4\",\n                                    fill: \"currentColor\",\n                                    viewBox: \"0 0 20 20\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        fillRule: \"evenodd\",\n                                        d: \"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\",\n                                        clipRule: \"evenodd\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm font-medium text-text-primary\",\n                                        children: template.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 328,\n                                        columnNumber: 15\n                                    }, this),\n                                    template.is_recurring && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center text-xs bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-3 h-3 mr-1\",\n                                                fill: \"currentColor\",\n                                                viewBox: \"0 0 20 20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    fillRule: \"evenodd\",\n                                                    d: \"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z\",\n                                                    clipRule: \"evenodd\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                                lineNumber: 331,\n                                                columnNumber: 19\n                                            }, this),\n                                            template.frequency\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-lg font-bold \".concat(template.transaction_type === 'income' ? 'text-green-600' : 'text-red-600'),\n                                children: [\n                                    template.transaction_type === 'income' ? '+' : '-',\n                                    formatCurrency(template.amount).replace(/^[^\\d]*/, '')\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this),\n                            template.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-text-secondary mt-1 truncate\",\n                                children: template.description\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 15\n                            }, this),\n                            template.is_recurring && template.next_due_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs text-text-secondary mt-1\",\n                                children: [\n                                    \"Next due: \",\n                                    new Date(template.next_due_date).toLocaleDateString(),\n                                    template.auto_create && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2 px-1 py-0.5 bg-gradient-to-r from-blue-100 to-purple-100 text-blue-700 rounded text-xs\",\n                                        children: \"Auto\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, template.id, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n                lineNumber: 309,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionTemplates.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n_s(TransactionTemplates, \"kx3fYjJNRN2OPDbOO6b3boSJCD8=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore\n    ];\n});\n_c = TransactionTemplates;\nvar _c;\n$RefreshReg$(_c, \"TransactionTemplates\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TransactionTemplates.tsx\n"));

/***/ })

});