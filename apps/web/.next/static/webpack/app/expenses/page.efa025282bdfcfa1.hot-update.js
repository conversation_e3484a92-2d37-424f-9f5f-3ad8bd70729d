"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/expenses/page",{

/***/ "(app-pages-browser)/./src/components/TransactionList.tsx":
/*!********************************************!*\
  !*** ./src/components/TransactionList.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TransactionListWeb: () => (/* binding */ TransactionListWeb),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(app-pages-browser)/../../packages/shared/src/index.ts\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(app-pages-browser)/../../node_modules/react-hot-toast/dist/index.mjs\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nfunction TransactionListWeb(param) {\n    let { onEditTransaction, key, ...props } = param;\n    _s();\n    const [transactions, setTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [loadingMore, setLoadingMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        searchQuery: '',\n        categoryId: '',\n        startDate: '',\n        endDate: '',\n        transactionType: 'all'\n    });\n    const [offset, setOffset] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasMoreItems, setHasMoreItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const ITEMS_PER_PAGE = 20;\n    const loadTransactions = async function() {\n        let reset = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        try {\n            const currentOffset = reset ? 0 : offset;\n            if (reset) {\n                setLoading(true);\n                setError('');\n            } else {\n                setLoadingMore(true);\n            }\n            const options = {\n                limit: ITEMS_PER_PAGE,\n                offset: currentOffset,\n                ...filters.categoryId && {\n                    categoryId: filters.categoryId\n                },\n                ...filters.startDate && {\n                    startDate: filters.startDate\n                },\n                ...filters.endDate && {\n                    endDate: filters.endDate\n                },\n                ...filters.transactionType !== 'all' && {\n                    transactionType: filters.transactionType\n                },\n                ...filters.searchQuery && {\n                    searchQuery: filters.searchQuery\n                }\n            };\n            const result = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.ExpenseService.getTransactions(options);\n            if (reset) {\n                setTransactions(result.data);\n                setOffset(ITEMS_PER_PAGE);\n            } else {\n                setTransactions((prev)=>[\n                        ...prev,\n                        ...result.data\n                    ]);\n                setOffset((prev)=>prev + ITEMS_PER_PAGE);\n            }\n            setTotalCount(result.count);\n            setHasMoreItems(result.data.length === ITEMS_PER_PAGE && currentOffset + ITEMS_PER_PAGE < result.count);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load transactions');\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to load transactions');\n        } finally{\n            setLoading(false);\n            setLoadingMore(false);\n        }\n    };\n    const loadCategories = async ()=>{\n        try {\n            const categoriesData = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.ExpenseService.getCategories();\n            setCategories(categoriesData);\n        } catch (err) {\n            console.error('Failed to load categories:', err);\n        }\n    };\n    // Initial load\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            loadCategories();\n            loadTransactions(true);\n        }\n    }[\"TransactionListWeb.useEffect\"], []);\n    // Reload when key changes (for refresh from parent)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            if (key !== undefined) {\n                loadTransactions(true);\n            }\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        key\n    ]);\n    // Reload when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            loadTransactions(true);\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        filters\n    ]);\n    const handleRefresh = ()=>{\n        loadTransactions(true);\n    };\n    const handleLoadMore = ()=>{\n        if (!loadingMore && hasMoreItems) {\n            loadTransactions(false);\n        }\n    };\n    const handleSearch = (query)=>{\n        setFilters((prev)=>({\n                ...prev,\n                searchQuery: query\n            }));\n    };\n    const handleFilterChange = (newFilters)=>{\n        setFilters((prev)=>({\n                ...prev,\n                ...newFilters\n            }));\n    };\n    const handleEdit = (transaction)=>{\n        onEditTransaction === null || onEditTransaction === void 0 ? void 0 : onEditTransaction(transaction);\n    };\n    const handleDelete = async (id)=>{\n        if (!confirm('Are you sure you want to delete this transaction?')) {\n            return;\n        }\n        try {\n            await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.ExpenseService.deleteTransaction(id);\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.success('Transaction deleted successfully');\n            loadTransactions(true);\n        } catch (err) {\n            react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.toast.error('Failed to delete transaction');\n        }\n    };\n    const formatCurrency = (amount)=>\"$\".concat(amount.toFixed(2));\n    const formatDate = (date)=>new Date(date).toLocaleDateString();\n    // Quick date filter functions\n    const [activeQuickFilter, setActiveQuickFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const setQuickDateFilter = (period)=>{\n        const today = new Date();\n        let startDate;\n        switch(period){\n            case 'week':\n                startDate = new Date(today);\n                startDate.setDate(today.getDate() - 7);\n                break;\n            case 'month':\n                startDate = new Date(today);\n                startDate.setMonth(today.getMonth() - 1);\n                break;\n            case 'year':\n                startDate = new Date(today);\n                startDate.setFullYear(today.getFullYear() - 1);\n                break;\n        }\n        setActiveQuickFilter(period);\n        setFilters((prev)=>({\n                ...prev,\n                startDate: startDate.toISOString().split('T')[0],\n                endDate: today.toISOString().split('T')[0]\n            }));\n    };\n    const clearDateFilter = ()=>{\n        setActiveQuickFilter('all');\n        setFilters((prev)=>({\n                ...prev,\n                startDate: '',\n                endDate: ''\n            }));\n    };\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [searchInput, setSearchInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const loadMoreRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Sync search input with filters\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            setSearchInput(filters.searchQuery);\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        filters.searchQuery\n    ]);\n    // Debounced search\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            const timeoutId = setTimeout({\n                \"TransactionListWeb.useEffect.timeoutId\": ()=>{\n                    if (searchInput !== filters.searchQuery) {\n                        setFilters({\n                            \"TransactionListWeb.useEffect.timeoutId\": (prev)=>({\n                                    ...prev,\n                                    searchQuery: searchInput\n                                })\n                        }[\"TransactionListWeb.useEffect.timeoutId\"]);\n                    }\n                }\n            }[\"TransactionListWeb.useEffect.timeoutId\"], 500);\n            return ({\n                \"TransactionListWeb.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"TransactionListWeb.useEffect\"];\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        searchInput,\n        filters.searchQuery\n    ]);\n    // Infinite scroll implementation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TransactionListWeb.useEffect\": ()=>{\n            const observer = new IntersectionObserver({\n                \"TransactionListWeb.useEffect\": (entries)=>{\n                    if (entries[0].isIntersecting && hasMoreItems && !loadingMore) {\n                        handleLoadMore();\n                    }\n                }\n            }[\"TransactionListWeb.useEffect\"], {\n                threshold: 0.1\n            });\n            if (loadMoreRef.current) {\n                observer.observe(loadMoreRef.current);\n            }\n            return ({\n                \"TransactionListWeb.useEffect\": ()=>observer.disconnect()\n            })[\"TransactionListWeb.useEffect\"];\n        }\n    }[\"TransactionListWeb.useEffect\"], [\n        hasMoreItems,\n        loadingMore,\n        handleLoadMore\n    ]);\n    const handleSearchSubmit = (e)=>{\n        e.preventDefault();\n        handleSearch(searchInput);\n    };\n    const clearFilters = ()=>{\n        const clearedFilters = {\n            searchQuery: '',\n            categoryId: '',\n            startDate: '',\n            endDate: '',\n            transactionType: 'all'\n        };\n        setActiveQuickFilter(null);\n        setFilters(clearedFilters);\n    };\n    const hasActiveFilters = filters.searchQuery || filters.categoryId || filters.startDate || filters.endDate || filters.transactionType !== 'all';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg border border-gray-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-6 py-5 border-b border-gray-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-gray-900\",\n                                        children: \"Transactions\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 13\n                                    }, this),\n                                    totalCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500 mt-1\",\n                                        children: [\n                                            totalCount,\n                                            \" transaction\",\n                                            totalCount === 1 ? '' : 's',\n                                            \" found\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                        onSubmit: handleSearchSubmit,\n                                        className: \"flex-1 sm:flex-initial\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    placeholder: \"Search by description or category...\",\n                                                    value: searchInput,\n                                                    onChange: (e)=>setSearchInput(e.target.value),\n                                                    className: \"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"h-5 w-5 text-gray-400\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowFilters(!showFilters),\n                                        className: \"px-4 py-2 rounded-lg font-medium transition-colors \".concat(showFilters ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"h-5 w-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 291,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"Filters\",\n                                                hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"bg-red-500 text-white rounded-full w-2 h-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 296,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleRefresh,\n                                        disabled: loading,\n                                        className: \"px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors disabled:opacity-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 \".concat(loading ? 'animate-spin' : ''),\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-4 bg-gray-50 rounded-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                        children: \"Quick Date Filters\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('week'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'week' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last 7 Days\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('month'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'month' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last Month\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setQuickDateFilter('year'),\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'year' ? 'bg-blue-600 text-white' : 'bg-blue-100 hover:bg-blue-200 text-blue-700'),\n                                                children: \"Last Year\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: clearDateFilter,\n                                                className: \"px-3 py-1 text-sm rounded-lg transition-colors \".concat(activeQuickFilter === 'all' ? 'bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-700'),\n                                                children: \"All Time\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Category\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 369,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: filters.categoryId,\n                                                onChange: (e)=>handleFilterChange({\n                                                        categoryId: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"\",\n                                                        children: \"All Categories\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: category.id,\n                                                            children: [\n                                                                category.icon,\n                                                                \" \",\n                                                                category.name\n                                                            ]\n                                                        }, category.id, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 21\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"Type\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 388,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: filters.transactionType,\n                                                onChange: (e)=>handleFilterChange({\n                                                        transactionType: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"all\",\n                                                        children: \"All Types\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"income\",\n                                                        children: \"Income\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"expense\",\n                                                        children: \"Expense\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"From Date\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: filters.startDate,\n                                                onChange: (e)=>handleFilterChange({\n                                                        startDate: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                                children: \"To Date\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 419,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"date\",\n                                                value: filters.endDate,\n                                                onChange: (e)=>handleFilterChange({\n                                                        endDate: e.target.value\n                                                    }),\n                                                className: \"w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 13\n                            }, this),\n                            hasActiveFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: clearFilters,\n                                    className: \"text-sm text-blue-600 hover:text-blue-800 font-medium\",\n                                    children: \"Clear all filters\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 433,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 bg-red-50 border-l-4 border-red-400\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"ml-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-red-700\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                    lineNumber: 449,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 448,\n                columnNumber: 9\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 460,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-gray-600\",\n                        children: \"Loading transactions...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 461,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 459,\n                columnNumber: 9\n            }, this) : transactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"py-16 px-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-12 h-12 text-gray-400\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"No transactions found\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 470,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 max-w-sm mx-auto\",\n                        children: hasActiveFilters ? 'Try adjusting your filters or search terms to find more transactions' : 'Add your first expense or income to get started!'\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                lineNumber: 464,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"divide-y divide-gray-200\",\n                        children: transactions.map((transaction)=>{\n                            var _transaction_category, _transaction_category1;\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-4 py-3 hover:bg-gray-50 transition-colors group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-between items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl flex-shrink-0\",\n                                                    children: ((_transaction_category = transaction.category) === null || _transaction_category === void 0 ? void 0 : _transaction_category.icon) || '💰'\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 min-w-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"font-medium text-gray-900 truncate\",\n                                                                    children: ((_transaction_category1 = transaction.category) === null || _transaction_category1 === void 0 ? void 0 : _transaction_category1.name) || 'Uncategorized'\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 494,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-gray-500 ml-2 flex-shrink-0\",\n                                                                    children: formatDate(transaction.transaction_date)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 497,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 493,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        transaction.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-600 truncate\",\n                                                            children: transaction.description\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 502,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 488,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3 ml-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-base font-semibold \".concat(transaction.transaction_type === 'income' ? 'text-green-600' : 'text-red-600'),\n                                                        children: [\n                                                            transaction.transaction_type === 'income' ? '+' : '-',\n                                                            formatCurrency(transaction.amount)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                        lineNumber: 511,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 510,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                    children: [\n                                                        onEditTransaction && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleEdit(transaction),\n                                                            className: \"p-1.5 text-gray-400 hover:text-blue-600 transition-colors\",\n                                                            title: \"Edit transaction\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"h-4 w-4\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 530,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                lineNumber: 529,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleDelete(transaction.id),\n                                                            className: \"p-1.5 text-gray-400 hover:text-red-600 transition-colors\",\n                                                            title: \"Delete transaction\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"h-4 w-4\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                                lineNumber: 539,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 17\n                                }, this)\n                            }, transaction.id, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 15\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 481,\n                        columnNumber: 11\n                    }, this),\n                    hasMoreItems && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: loadMoreRef,\n                        className: \"p-6 text-center border-t\",\n                        children: loadingMore ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 19\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-gray-600\",\n                                    children: \"Loading more...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                                    lineNumber: 556,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 554,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleLoadMore,\n                            className: \"text-blue-600 hover:text-blue-800 font-medium\",\n                            children: \"Load More Transactions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                            lineNumber: 559,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n                        lineNumber: 552,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/TransactionList.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s(TransactionListWeb, \"BDnN+q2ZjhU/3NQK95okzl5VjRk=\");\n_c = TransactionListWeb;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TransactionListWeb);\nvar _c;\n$RefreshReg$(_c, \"TransactionListWeb\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1RyYW5zYWN0aW9uTGlzdC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQThCO0FBQ3FCO0FBQytDO0FBQzNEO0FBb0JoQyxTQUFTTSxtQkFBbUIsS0FJVDtRQUpTLEVBQ2pDQyxpQkFBaUIsRUFDakJDLEdBQUcsRUFDSCxHQUFHQyxPQUNxQixHQUpTOztJQUtqQyxNQUFNLENBQUNDLGNBQWNDLGdCQUFnQixHQUFHViwrQ0FBUUEsQ0FBaUIsRUFBRTtJQUNuRSxNQUFNLENBQUNXLFlBQVlDLGNBQWMsR0FBR1osK0NBQVFBLENBQWMsRUFBRTtJQUM1RCxNQUFNLENBQUNhLFNBQVNDLFdBQVcsR0FBR2QsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDZSxhQUFhQyxlQUFlLEdBQUdoQiwrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNpQixPQUFPQyxTQUFTLEdBQUdsQiwrQ0FBUUEsQ0FBQztJQUNuQyxNQUFNLENBQUNtQixZQUFZQyxjQUFjLEdBQUdwQiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUNxQixTQUFTQyxXQUFXLEdBQUd0QiwrQ0FBUUEsQ0FBcUI7UUFDekR1QixhQUFhO1FBQ2JDLFlBQVk7UUFDWkMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLGlCQUFpQjtJQUNuQjtJQUNBLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHN0IsK0NBQVFBLENBQUM7SUFDckMsTUFBTSxDQUFDOEIsY0FBY0MsZ0JBQWdCLEdBQUcvQiwrQ0FBUUEsQ0FBQztJQUNqRCxNQUFNZ0MsaUJBQWlCO0lBRXZCLE1BQU1DLG1CQUFtQjtZQUFPQyx5RUFBUTtRQUN0QyxJQUFJO1lBQ0YsTUFBTUMsZ0JBQWdCRCxRQUFRLElBQUlOO1lBQ2xDLElBQUlNLE9BQU87Z0JBQ1RwQixXQUFXO2dCQUNYSSxTQUFTO1lBQ1gsT0FBTztnQkFDTEYsZUFBZTtZQUNqQjtZQUVBLE1BQU1vQixVQUFVO2dCQUNkQyxPQUFPTDtnQkFDUEosUUFBUU87Z0JBQ1IsR0FBSWQsUUFBUUcsVUFBVSxJQUFJO29CQUFFQSxZQUFZSCxRQUFRRyxVQUFVO2dCQUFDLENBQUM7Z0JBQzVELEdBQUlILFFBQVFJLFNBQVMsSUFBSTtvQkFBRUEsV0FBV0osUUFBUUksU0FBUztnQkFBQyxDQUFDO2dCQUN6RCxHQUFJSixRQUFRSyxPQUFPLElBQUk7b0JBQUVBLFNBQVNMLFFBQVFLLE9BQU87Z0JBQUMsQ0FBQztnQkFDbkQsR0FBSUwsUUFBUU0sZUFBZSxLQUFLLFNBQVM7b0JBQUVBLGlCQUFpQk4sUUFBUU0sZUFBZTtnQkFBeUIsQ0FBQztnQkFDN0csR0FBSU4sUUFBUUUsV0FBVyxJQUFJO29CQUFFQSxhQUFhRixRQUFRRSxXQUFXO2dCQUFDLENBQUM7WUFDakU7WUFFQSxNQUFNZSxTQUFTLE1BQU1uQyx3REFBY0EsQ0FBQ29DLGVBQWUsQ0FBQ0g7WUFFcEQsSUFBSUYsT0FBTztnQkFDVHhCLGdCQUFnQjRCLE9BQU9FLElBQUk7Z0JBQzNCWCxVQUFVRztZQUNaLE9BQU87Z0JBQ0x0QixnQkFBZ0IrQixDQUFBQSxPQUFROzJCQUFJQTsyQkFBU0gsT0FBT0UsSUFBSTtxQkFBQztnQkFDakRYLFVBQVVZLENBQUFBLE9BQVFBLE9BQU9UO1lBQzNCO1lBRUFaLGNBQWNrQixPQUFPSSxLQUFLO1lBQzFCWCxnQkFBZ0JPLE9BQU9FLElBQUksQ0FBQ0csTUFBTSxLQUFLWCxrQkFBa0IsZ0JBQWlCQSxpQkFBa0JNLE9BQU9JLEtBQUs7UUFDMUcsRUFBRSxPQUFPRSxLQUFLO1lBQ1oxQixTQUFTMEIsZUFBZUMsUUFBUUQsSUFBSUUsT0FBTyxHQUFHO1lBQzlDMUMsa0RBQUtBLENBQUNhLEtBQUssQ0FBQztRQUNkLFNBQVU7WUFDUkgsV0FBVztZQUNYRSxlQUFlO1FBQ2pCO0lBQ0Y7SUFFQSxNQUFNK0IsaUJBQWlCO1FBQ3JCLElBQUk7WUFDRixNQUFNQyxpQkFBaUIsTUFBTTdDLHdEQUFjQSxDQUFDOEMsYUFBYTtZQUN6RHJDLGNBQWNvQztRQUNoQixFQUFFLE9BQU9KLEtBQUs7WUFDWk0sUUFBUWpDLEtBQUssQ0FBQyw4QkFBOEIyQjtRQUM5QztJQUNGO0lBRUEsZUFBZTtJQUNmMUMsZ0RBQVNBO3dDQUFDO1lBQ1I2QztZQUNBZCxpQkFBaUI7UUFDbkI7dUNBQUcsRUFBRTtJQUVMLG9EQUFvRDtJQUNwRC9CLGdEQUFTQTt3Q0FBQztZQUNSLElBQUlLLFFBQVE0QyxXQUFXO2dCQUNyQmxCLGlCQUFpQjtZQUNuQjtRQUNGO3VDQUFHO1FBQUMxQjtLQUFJO0lBRVIsNkJBQTZCO0lBQzdCTCxnREFBU0E7d0NBQUM7WUFDUitCLGlCQUFpQjtRQUNuQjt1Q0FBRztRQUFDWjtLQUFRO0lBRVosTUFBTStCLGdCQUFnQjtRQUNwQm5CLGlCQUFpQjtJQUNuQjtJQUVBLE1BQU1vQixpQkFBaUI7UUFDckIsSUFBSSxDQUFDdEMsZUFBZWUsY0FBYztZQUNoQ0csaUJBQWlCO1FBQ25CO0lBQ0Y7SUFFQSxNQUFNcUIsZUFBZSxDQUFDQztRQUNwQmpDLFdBQVdtQixDQUFBQSxPQUFTO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUVsQixhQUFhZ0M7WUFBTTtJQUNwRDtJQUVBLE1BQU1DLHFCQUFxQixDQUFDQztRQUMxQm5DLFdBQVdtQixDQUFBQSxPQUFTO2dCQUFFLEdBQUdBLElBQUk7Z0JBQUUsR0FBR2dCLFVBQVU7WUFBQztJQUMvQztJQUVBLE1BQU1DLGFBQWEsQ0FBQ0M7UUFDbEJyRCw4QkFBQUEsd0NBQUFBLGtCQUFvQnFEO0lBQ3RCO0lBRUEsTUFBTUMsZUFBZSxPQUFPQztRQUMxQixJQUFJLENBQUNDLFFBQVEsc0RBQXNEO1lBQ2pFO1FBQ0Y7UUFFQSxJQUFJO1lBQ0YsTUFBTTNELHdEQUFjQSxDQUFDNEQsaUJBQWlCLENBQUNGO1lBQ3ZDekQsa0RBQUtBLENBQUM0RCxPQUFPLENBQUM7WUFDZC9CLGlCQUFpQjtRQUNuQixFQUFFLE9BQU9XLEtBQUs7WUFDWnhDLGtEQUFLQSxDQUFDYSxLQUFLLENBQUM7UUFDZDtJQUNGO0lBRUEsTUFBTWdELGlCQUFpQixDQUFDQyxTQUFtQixJQUFzQixPQUFsQkEsT0FBT0MsT0FBTyxDQUFDO0lBQzlELE1BQU1DLGFBQWEsQ0FBQ0MsT0FBd0IsSUFBSUMsS0FBS0QsTUFBTUUsa0JBQWtCO0lBRTdFLDhCQUE4QjtJQUM5QixNQUFNLENBQUNDLG1CQUFtQkMscUJBQXFCLEdBQUd6RSwrQ0FBUUEsQ0FBZ0I7SUFFMUUsTUFBTTBFLHFCQUFxQixDQUFDQztRQUMxQixNQUFNQyxRQUFRLElBQUlOO1FBQ2xCLElBQUk3QztRQUVKLE9BQVFrRDtZQUNOLEtBQUs7Z0JBQ0hsRCxZQUFZLElBQUk2QyxLQUFLTTtnQkFDckJuRCxVQUFVb0QsT0FBTyxDQUFDRCxNQUFNRSxPQUFPLEtBQUs7Z0JBQ3BDO1lBQ0YsS0FBSztnQkFDSHJELFlBQVksSUFBSTZDLEtBQUtNO2dCQUNyQm5ELFVBQVVzRCxRQUFRLENBQUNILE1BQU1JLFFBQVEsS0FBSztnQkFDdEM7WUFDRixLQUFLO2dCQUNIdkQsWUFBWSxJQUFJNkMsS0FBS007Z0JBQ3JCbkQsVUFBVXdELFdBQVcsQ0FBQ0wsTUFBTU0sV0FBVyxLQUFLO2dCQUM1QztRQUNKO1FBRUFULHFCQUFxQkU7UUFDckJyRCxXQUFXbUIsQ0FBQUEsT0FBUztnQkFDbEIsR0FBR0EsSUFBSTtnQkFDUGhCLFdBQVdBLFVBQVUwRCxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtnQkFDaEQxRCxTQUFTa0QsTUFBTU8sV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7WUFDNUM7SUFDRjtJQUVBLE1BQU1DLGtCQUFrQjtRQUN0QloscUJBQXFCO1FBQ3JCbkQsV0FBV21CLENBQUFBLE9BQVM7Z0JBQUUsR0FBR0EsSUFBSTtnQkFBRWhCLFdBQVc7Z0JBQUlDLFNBQVM7WUFBRztJQUM1RDtJQUVBLE1BQU0sQ0FBQzRELGFBQWFDLGVBQWUsR0FBR3ZGLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0sQ0FBQ3dGLGFBQWFDLGVBQWUsR0FBR3pGLCtDQUFRQSxDQUFDO0lBQy9DLE1BQU0wRixjQUFjekYsNkNBQU1BLENBQWlCO0lBRTNDLGlDQUFpQztJQUNqQ0MsZ0RBQVNBO3dDQUFDO1lBQ1J1RixlQUFlcEUsUUFBUUUsV0FBVztRQUNwQzt1Q0FBRztRQUFDRixRQUFRRSxXQUFXO0tBQUM7SUFFeEIsbUJBQW1CO0lBQ25CckIsZ0RBQVNBO3dDQUFDO1lBQ1IsTUFBTXlGLFlBQVlDOzBEQUFXO29CQUMzQixJQUFJSixnQkFBZ0JuRSxRQUFRRSxXQUFXLEVBQUU7d0JBQ3ZDRDtzRUFBV21CLENBQUFBLE9BQVM7b0NBQUUsR0FBR0EsSUFBSTtvQ0FBRWxCLGFBQWFpRTtnQ0FBWTs7b0JBQzFEO2dCQUNGO3lEQUFHO1lBRUg7Z0RBQU8sSUFBTUssYUFBYUY7O1FBQzVCO3VDQUFHO1FBQUNIO1FBQWFuRSxRQUFRRSxXQUFXO0tBQUM7SUFFckMsaUNBQWlDO0lBQ2pDckIsZ0RBQVNBO3dDQUFDO1lBQ1IsTUFBTTRGLFdBQVcsSUFBSUM7Z0RBQ25CLENBQUNDO29CQUNDLElBQUlBLE9BQU8sQ0FBQyxFQUFFLENBQUNDLGNBQWMsSUFBSW5FLGdCQUFnQixDQUFDZixhQUFhO3dCQUM3RHNDO29CQUNGO2dCQUNGOytDQUNBO2dCQUFFNkMsV0FBVztZQUFJO1lBR25CLElBQUlSLFlBQVlTLE9BQU8sRUFBRTtnQkFDdkJMLFNBQVNNLE9BQU8sQ0FBQ1YsWUFBWVMsT0FBTztZQUN0QztZQUVBO2dEQUFPLElBQU1MLFNBQVNPLFVBQVU7O1FBQ2xDO3VDQUFHO1FBQUN2RTtRQUFjZjtRQUFhc0M7S0FBZTtJQUU5QyxNQUFNaUQscUJBQXFCLENBQUNDO1FBQzFCQSxFQUFFQyxjQUFjO1FBQ2hCbEQsYUFBYWtDO0lBQ2Y7SUFFQSxNQUFNaUIsZUFBZTtRQUNuQixNQUFNQyxpQkFBaUI7WUFDckJuRixhQUFhO1lBQ2JDLFlBQVk7WUFDWkMsV0FBVztZQUNYQyxTQUFTO1lBQ1RDLGlCQUFpQjtRQUNuQjtRQUNBOEMscUJBQXFCO1FBQ3JCbkQsV0FBV29GO0lBQ2I7SUFFQSxNQUFNQyxtQkFBbUJ0RixRQUFRRSxXQUFXLElBQUlGLFFBQVFHLFVBQVUsSUFDaEVILFFBQVFJLFNBQVMsSUFBSUosUUFBUUssT0FBTyxJQUFJTCxRQUFRTSxlQUFlLEtBQUs7SUFFdEUscUJBQ0UsOERBQUNpRjtRQUFJQyxXQUFVOzswQkFFYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7MENBQ2IsOERBQUNEOztrREFDQyw4REFBQ0U7d0NBQUdELFdBQVU7a0RBQXNDOzs7Ozs7b0NBR25EMUYsYUFBYSxtQkFDWiw4REFBQzRGO3dDQUFFRixXQUFVOzs0Q0FDVjFGOzRDQUFXOzRDQUFjQSxlQUEwQixJQUFJLEtBQUs7NENBQUk7Ozs7Ozs7Ozs7Ozs7MENBS3ZFLDhEQUFDeUY7Z0NBQUlDLFdBQVU7O2tEQUViLDhEQUFDRzt3Q0FBS0MsVUFBVVg7d0NBQW9CTyxXQUFVO2tEQUM1Qyw0RUFBQ0Q7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDSztvREFDQ0MsTUFBSztvREFDTEMsYUFBWTtvREFDWkMsT0FBTzdCO29EQUNQOEIsVUFBVSxDQUFDZixJQUFNZCxlQUFlYyxFQUFFZ0IsTUFBTSxDQUFDRixLQUFLO29EQUM5Q1IsV0FBVTs7Ozs7OzhEQUVaLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ1c7d0RBQUlYLFdBQVU7d0RBQXdCWSxNQUFLO3dEQUFPQyxRQUFPO3dEQUFlQyxTQUFRO2tFQUMvRSw0RUFBQ0M7NERBQUtDLGVBQWM7NERBQVFDLGdCQUFlOzREQUFRQyxhQUFhOzREQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBTzdFLDhEQUFDQzt3Q0FDQ0MsU0FBUyxJQUFNM0MsZUFBZSxDQUFDRDt3Q0FDL0J1QixXQUFXLHNEQUlWLE9BSEN2QixjQUNJLDJCQUNBO2tEQUdOLDRFQUFDNkM7NENBQUt0QixXQUFVOzs4REFDZCw4REFBQ1c7b0RBQUlYLFdBQVU7b0RBQVVZLE1BQUs7b0RBQU9DLFFBQU87b0RBQWVDLFNBQVE7OERBQ2pFLDRFQUFDQzt3REFBS0MsZUFBYzt3REFBUUMsZ0JBQWU7d0RBQVFDLGFBQWE7d0RBQUdDLEdBQUU7Ozs7Ozs7Ozs7O2dEQUNqRTtnREFFTHJCLGtDQUNDLDhEQUFDd0I7b0RBQUt0QixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrREFNdEIsOERBQUNvQjt3Q0FDQ0MsU0FBUzlFO3dDQUNUZ0YsVUFBVXZIO3dDQUNWZ0csV0FBVTtrREFFViw0RUFBQ1c7NENBQUlYLFdBQVcsV0FBeUMsT0FBOUJoRyxVQUFVLGlCQUFpQjs0Q0FBTTRHLE1BQUs7NENBQU9DLFFBQU87NENBQWVDLFNBQVE7c0RBQ3BHLDRFQUFDQztnREFBS0MsZUFBYztnREFBUUMsZ0JBQWU7Z0RBQVFDLGFBQWE7Z0RBQUdDLEdBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBTzVFMUMsNkJBQ0MsOERBQUNzQjt3QkFBSUMsV0FBVTs7MENBRWIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ3dCO3dDQUFNeEIsV0FBVTtrREFBK0M7Ozs7OztrREFHaEUsOERBQUNEO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ29CO2dEQUNDQyxTQUFTLElBQU14RCxtQkFBbUI7Z0RBQ2xDbUMsV0FBVyxrREFJVixPQUhDckMsc0JBQXNCLFNBQ2xCLDJCQUNBOzBEQUVQOzs7Ozs7MERBR0QsOERBQUN5RDtnREFDQ0MsU0FBUyxJQUFNeEQsbUJBQW1CO2dEQUNsQ21DLFdBQVcsa0RBSVYsT0FIQ3JDLHNCQUFzQixVQUNsQiwyQkFDQTswREFFUDs7Ozs7OzBEQUdELDhEQUFDeUQ7Z0RBQ0NDLFNBQVMsSUFBTXhELG1CQUFtQjtnREFDbENtQyxXQUFXLGtEQUlWLE9BSENyQyxzQkFBc0IsU0FDbEIsMkJBQ0E7MERBRVA7Ozs7OzswREFHRCw4REFBQ3lEO2dEQUNDQyxTQUFTN0M7Z0RBQ1R3QixXQUFXLGtEQUlWLE9BSENyQyxzQkFBc0IsUUFDbEIsMkJBQ0E7MERBRVA7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FNTCw4REFBQ29DO2dDQUFJQyxXQUFVOztrREFFYiw4REFBQ0Q7OzBEQUNDLDhEQUFDeUI7Z0RBQU14QixXQUFVOzBEQUErQzs7Ozs7OzBEQUdoRSw4REFBQ3lCO2dEQUNDakIsT0FBT2hHLFFBQVFHLFVBQVU7Z0RBQ3pCOEYsVUFBVSxDQUFDZixJQUFNL0MsbUJBQW1CO3dEQUFFaEMsWUFBWStFLEVBQUVnQixNQUFNLENBQUNGLEtBQUs7b0RBQUM7Z0RBQ2pFUixXQUFVOztrRUFFViw4REFBQzBCO3dEQUFPbEIsT0FBTTtrRUFBRzs7Ozs7O29EQUNoQjFHLFdBQVc2SCxHQUFHLENBQUMsQ0FBQ0MseUJBQ2YsOERBQUNGOzREQUF5QmxCLE9BQU9vQixTQUFTNUUsRUFBRTs7Z0VBQ3pDNEUsU0FBU0MsSUFBSTtnRUFBQztnRUFBRUQsU0FBU0UsSUFBSTs7MkRBRG5CRixTQUFTNUUsRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBUTlCLDhEQUFDK0M7OzBEQUNDLDhEQUFDeUI7Z0RBQU14QixXQUFVOzBEQUErQzs7Ozs7OzBEQUdoRSw4REFBQ3lCO2dEQUNDakIsT0FBT2hHLFFBQVFNLGVBQWU7Z0RBQzlCMkYsVUFBVSxDQUFDZixJQUFNL0MsbUJBQW1CO3dEQUNsQzdCLGlCQUFpQjRFLEVBQUVnQixNQUFNLENBQUNGLEtBQUs7b0RBQ2pDO2dEQUNBUixXQUFVOztrRUFFViw4REFBQzBCO3dEQUFPbEIsT0FBTTtrRUFBTTs7Ozs7O2tFQUNwQiw4REFBQ2tCO3dEQUFPbEIsT0FBTTtrRUFBUzs7Ozs7O2tFQUN2Qiw4REFBQ2tCO3dEQUFPbEIsT0FBTTtrRUFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tEQUs1Qiw4REFBQ1Q7OzBEQUNDLDhEQUFDeUI7Z0RBQU14QixXQUFVOzBEQUErQzs7Ozs7OzBEQUdoRSw4REFBQ0s7Z0RBQ0NDLE1BQUs7Z0RBQ0xFLE9BQU9oRyxRQUFRSSxTQUFTO2dEQUN4QjZGLFVBQVUsQ0FBQ2YsSUFBTS9DLG1CQUFtQjt3REFBRS9CLFdBQVc4RSxFQUFFZ0IsTUFBTSxDQUFDRixLQUFLO29EQUFDO2dEQUNoRVIsV0FBVTs7Ozs7Ozs7Ozs7O2tEQUtkLDhEQUFDRDs7MERBQ0MsOERBQUN5QjtnREFBTXhCLFdBQVU7MERBQStDOzs7Ozs7MERBR2hFLDhEQUFDSztnREFDQ0MsTUFBSztnREFDTEUsT0FBT2hHLFFBQVFLLE9BQU87Z0RBQ3RCNEYsVUFBVSxDQUFDZixJQUFNL0MsbUJBQW1CO3dEQUFFOUIsU0FBUzZFLEVBQUVnQixNQUFNLENBQUNGLEtBQUs7b0RBQUM7Z0RBQzlEUixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7NEJBTWZGLGtDQUNDLDhEQUFDQztnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ29CO29DQUNDQyxTQUFTekI7b0NBQ1RJLFdBQVU7OENBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O1lBVVY1Rix1QkFDQyw4REFBQzJGO2dCQUFJQyxXQUFVOzBCQUNiLDRFQUFDRDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNFOzRCQUFFRixXQUFVO3NDQUF3QjVGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFPNUNKLHdCQUNDLDhEQUFDK0Y7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTs7Ozs7O2tDQUNmLDhEQUFDRTt3QkFBRUYsV0FBVTtrQ0FBcUI7Ozs7Ozs7Ozs7O3VCQUVsQ3BHLGFBQWFrQyxNQUFNLEtBQUssa0JBQzFCLDhEQUFDaUU7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ1c7NEJBQUlYLFdBQVU7NEJBQTBCWSxNQUFLOzRCQUFPQyxRQUFPOzRCQUFlQyxTQUFRO3NDQUNqRiw0RUFBQ0M7Z0NBQUtDLGVBQWM7Z0NBQVFDLGdCQUFlO2dDQUFRQyxhQUFhO2dDQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O2tDQUd6RSw4REFBQ1k7d0JBQUcvQixXQUFVO2tDQUEyQzs7Ozs7O2tDQUN6RCw4REFBQ0U7d0JBQUVGLFdBQVU7a0NBQ1ZGLG1CQUNHLHlFQUNBOzs7Ozs7Ozs7OztxQ0FLUjs7a0NBRUUsOERBQUNDO3dCQUFJQyxXQUFVO2tDQUNacEcsYUFBYStILEdBQUcsQ0FBQyxDQUFDN0U7Z0NBUVJBLHVCQUtJQTtpREFaYiw4REFBQ2lEO2dDQUVDQyxXQUFVOzBDQUVWLDRFQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ3NCO29EQUFLdEIsV0FBVTs4REFDYmxELEVBQUFBLHdCQUFBQSxZQUFZOEUsUUFBUSxjQUFwQjlFLDRDQUFBQSxzQkFBc0IrRSxJQUFJLEtBQUk7Ozs7Ozs4REFFakMsOERBQUM5QjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNEOzREQUFJQyxXQUFVOzs4RUFDYiw4REFBQytCO29FQUFHL0IsV0FBVTs4RUFDWGxELEVBQUFBLHlCQUFBQSxZQUFZOEUsUUFBUSxjQUFwQjlFLDZDQUFBQSx1QkFBc0JnRixJQUFJLEtBQUk7Ozs7Ozs4RUFFakMsOERBQUNSO29FQUFLdEIsV0FBVTs4RUFDYnpDLFdBQVdULFlBQVlrRixnQkFBZ0I7Ozs7Ozs7Ozs7Ozt3REFHM0NsRixZQUFZbUYsV0FBVyxrQkFDdEIsOERBQUMvQjs0REFBRUYsV0FBVTtzRUFDVmxELFlBQVltRixXQUFXOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBTWhDLDhEQUFDbEM7NENBQUlDLFdBQVU7OzhEQUNiLDhEQUFDRDtvREFBSUMsV0FBVTs4REFDYiw0RUFBQ0U7d0RBQUVGLFdBQVcsMkJBSWIsT0FIQ2xELFlBQVlvRixnQkFBZ0IsS0FBSyxXQUM3QixtQkFDQTs7NERBRUhwRixZQUFZb0YsZ0JBQWdCLEtBQUssV0FBVyxNQUFNOzREQUNsRDlFLGVBQWVOLFlBQVlPLE1BQU07Ozs7Ozs7Ozs7Ozs4REFLdEMsOERBQUMwQztvREFBSUMsV0FBVTs7d0RBQ1p2RyxtQ0FDQyw4REFBQzJIOzREQUNDQyxTQUFTLElBQU14RSxXQUFXQzs0REFDMUJrRCxXQUFVOzREQUNWbUMsT0FBTTtzRUFFTiw0RUFBQ3hCO2dFQUFJWCxXQUFVO2dFQUFVWSxNQUFLO2dFQUFPQyxRQUFPO2dFQUFlQyxTQUFROzBFQUNqRSw0RUFBQ0M7b0VBQUtDLGVBQWM7b0VBQVFDLGdCQUFlO29FQUFRQyxhQUFhO29FQUFHQyxHQUFFOzs7Ozs7Ozs7Ozs7Ozs7O3NFQUkzRSw4REFBQ0M7NERBQ0NDLFNBQVMsSUFBTXRFLGFBQWFELFlBQVlFLEVBQUU7NERBQzFDZ0QsV0FBVTs0REFDVm1DLE9BQU07c0VBRU4sNEVBQUN4QjtnRUFBSVgsV0FBVTtnRUFBVVksTUFBSztnRUFBT0MsUUFBTztnRUFBZUMsU0FBUTswRUFDakUsNEVBQUNDO29FQUFLQyxlQUFjO29FQUFRQyxnQkFBZTtvRUFBUUMsYUFBYTtvRUFBR0MsR0FBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsrQkF4RDFFckUsWUFBWUUsRUFBRTs7Ozs7Ozs7Ozs7b0JBbUV4Qi9CLDhCQUNDLDhEQUFDOEU7d0JBQUlxQyxLQUFLdkQ7d0JBQWFtQixXQUFVO2tDQUM5QjlGLDRCQUNDLDhEQUFDNkY7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRDtvQ0FBSUMsV0FBVTs7Ozs7OzhDQUNmLDhEQUFDc0I7b0NBQUt0QixXQUFVOzhDQUFxQjs7Ozs7Ozs7Ozs7aURBR3ZDLDhEQUFDb0I7NEJBQ0NDLFNBQVM3RTs0QkFDVHdELFdBQVU7c0NBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFVakI7R0FwaUJnQnhHO0tBQUFBO0FBc2lCaEIsaUVBQWVBLGtCQUFrQkEsRUFBQSIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9hcHBzL3dlYi9zcmMvY29tcG9uZW50cy9UcmFuc2FjdGlvbkxpc3QudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZVJlZiwgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBFeHBlbnNlU2VydmljZSwgdHlwZSBJVHJhbnNhY3Rpb24sIHR5cGUgSUNhdGVnb3J5LCB1c2VDdXJyZW5jeVN0b3JlIH0gZnJvbSAnQHJlcG8vc2hhcmVkJ1xuaW1wb3J0IHsgdG9hc3QgfSBmcm9tICdyZWFjdC1ob3QtdG9hc3QnXG5cbi8vIFRlbXBvcmFyeSBwbGFjZWhvbGRlciB0eXBlc1xuaW50ZXJmYWNlIFRyYW5zYWN0aW9uTGlzdFByb3BzIHtcbiAgLy8gQWRkIHByb3BzIGFzIG5lZWRlZFxufVxuXG5pbnRlcmZhY2UgVHJhbnNhY3Rpb25GaWx0ZXJzIHtcbiAgc2VhcmNoUXVlcnk6IHN0cmluZ1xuICBjYXRlZ29yeUlkOiBzdHJpbmdcbiAgc3RhcnREYXRlOiBzdHJpbmdcbiAgZW5kRGF0ZTogc3RyaW5nXG4gIHRyYW5zYWN0aW9uVHlwZTogJ2FsbCcgfCAnaW5jb21lJyB8ICdleHBlbnNlJ1xufVxuXG5pbnRlcmZhY2UgVHJhbnNhY3Rpb25MaXN0V2ViUHJvcHMgZXh0ZW5kcyBUcmFuc2FjdGlvbkxpc3RQcm9wcyB7XG4gIG9uRWRpdFRyYW5zYWN0aW9uPzogKHRyYW5zYWN0aW9uOiBJVHJhbnNhY3Rpb24pID0+IHZvaWRcbiAga2V5PzogbnVtYmVyIC8vIEZvciByZWZyZXNoIGZ1bmN0aW9uYWxpdHlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFRyYW5zYWN0aW9uTGlzdFdlYih7IFxuICBvbkVkaXRUcmFuc2FjdGlvbixcbiAga2V5LFxuICAuLi5wcm9wcyBcbn06IFRyYW5zYWN0aW9uTGlzdFdlYlByb3BzKSB7XG4gIGNvbnN0IFt0cmFuc2FjdGlvbnMsIHNldFRyYW5zYWN0aW9uc10gPSB1c2VTdGF0ZTxJVHJhbnNhY3Rpb25bXT4oW10pXG4gIGNvbnN0IFtjYXRlZ29yaWVzLCBzZXRDYXRlZ29yaWVzXSA9IHVzZVN0YXRlPElDYXRlZ29yeVtdPihbXSlcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSlcbiAgY29uc3QgW2xvYWRpbmdNb3JlLCBzZXRMb2FkaW5nTW9yZV0gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgW3RvdGFsQ291bnQsIHNldFRvdGFsQ291bnRdID0gdXNlU3RhdGUoMClcbiAgY29uc3QgW2ZpbHRlcnMsIHNldEZpbHRlcnNdID0gdXNlU3RhdGU8VHJhbnNhY3Rpb25GaWx0ZXJzPih7XG4gICAgc2VhcmNoUXVlcnk6ICcnLFxuICAgIGNhdGVnb3J5SWQ6ICcnLFxuICAgIHN0YXJ0RGF0ZTogJycsXG4gICAgZW5kRGF0ZTogJycsXG4gICAgdHJhbnNhY3Rpb25UeXBlOiAnYWxsJ1xuICB9KVxuICBjb25zdCBbb2Zmc2V0LCBzZXRPZmZzZXRdID0gdXNlU3RhdGUoMClcbiAgY29uc3QgW2hhc01vcmVJdGVtcywgc2V0SGFzTW9yZUl0ZW1zXSA9IHVzZVN0YXRlKGZhbHNlKVxuICBjb25zdCBJVEVNU19QRVJfUEFHRSA9IDIwXG5cbiAgY29uc3QgbG9hZFRyYW5zYWN0aW9ucyA9IGFzeW5jIChyZXNldCA9IGZhbHNlKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGN1cnJlbnRPZmZzZXQgPSByZXNldCA/IDAgOiBvZmZzZXRcbiAgICAgIGlmIChyZXNldCkge1xuICAgICAgICBzZXRMb2FkaW5nKHRydWUpXG4gICAgICAgIHNldEVycm9yKCcnKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0TG9hZGluZ01vcmUodHJ1ZSlcbiAgICAgIH1cblxuICAgICAgY29uc3Qgb3B0aW9ucyA9IHtcbiAgICAgICAgbGltaXQ6IElURU1TX1BFUl9QQUdFLFxuICAgICAgICBvZmZzZXQ6IGN1cnJlbnRPZmZzZXQsXG4gICAgICAgIC4uLihmaWx0ZXJzLmNhdGVnb3J5SWQgJiYgeyBjYXRlZ29yeUlkOiBmaWx0ZXJzLmNhdGVnb3J5SWQgfSksXG4gICAgICAgIC4uLihmaWx0ZXJzLnN0YXJ0RGF0ZSAmJiB7IHN0YXJ0RGF0ZTogZmlsdGVycy5zdGFydERhdGUgfSksXG4gICAgICAgIC4uLihmaWx0ZXJzLmVuZERhdGUgJiYgeyBlbmREYXRlOiBmaWx0ZXJzLmVuZERhdGUgfSksXG4gICAgICAgIC4uLihmaWx0ZXJzLnRyYW5zYWN0aW9uVHlwZSAhPT0gJ2FsbCcgJiYgeyB0cmFuc2FjdGlvblR5cGU6IGZpbHRlcnMudHJhbnNhY3Rpb25UeXBlIGFzICdpbmNvbWUnIHwgJ2V4cGVuc2UnIH0pLFxuICAgICAgICAuLi4oZmlsdGVycy5zZWFyY2hRdWVyeSAmJiB7IHNlYXJjaFF1ZXJ5OiBmaWx0ZXJzLnNlYXJjaFF1ZXJ5IH0pLFxuICAgICAgfVxuXG4gICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBFeHBlbnNlU2VydmljZS5nZXRUcmFuc2FjdGlvbnMob3B0aW9ucylcblxuICAgICAgaWYgKHJlc2V0KSB7XG4gICAgICAgIHNldFRyYW5zYWN0aW9ucyhyZXN1bHQuZGF0YSlcbiAgICAgICAgc2V0T2Zmc2V0KElURU1TX1BFUl9QQUdFKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgc2V0VHJhbnNhY3Rpb25zKHByZXYgPT4gWy4uLnByZXYsIC4uLnJlc3VsdC5kYXRhXSlcbiAgICAgICAgc2V0T2Zmc2V0KHByZXYgPT4gcHJldiArIElURU1TX1BFUl9QQUdFKVxuICAgICAgfVxuXG4gICAgICBzZXRUb3RhbENvdW50KHJlc3VsdC5jb3VudClcbiAgICAgIHNldEhhc01vcmVJdGVtcyhyZXN1bHQuZGF0YS5sZW5ndGggPT09IElURU1TX1BFUl9QQUdFICYmIChjdXJyZW50T2Zmc2V0ICsgSVRFTVNfUEVSX1BBR0UpIDwgcmVzdWx0LmNvdW50KVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgc2V0RXJyb3IoZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gbG9hZCB0cmFuc2FjdGlvbnMnKVxuICAgICAgdG9hc3QuZXJyb3IoJ0ZhaWxlZCB0byBsb2FkIHRyYW5zYWN0aW9ucycpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXG4gICAgICBzZXRMb2FkaW5nTW9yZShmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBsb2FkQ2F0ZWdvcmllcyA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgY2F0ZWdvcmllc0RhdGEgPSBhd2FpdCBFeHBlbnNlU2VydmljZS5nZXRDYXRlZ29yaWVzKClcbiAgICAgIHNldENhdGVnb3JpZXMoY2F0ZWdvcmllc0RhdGEpXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBjYXRlZ29yaWVzOicsIGVycilcbiAgICB9XG4gIH1cblxuICAvLyBJbml0aWFsIGxvYWRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBsb2FkQ2F0ZWdvcmllcygpXG4gICAgbG9hZFRyYW5zYWN0aW9ucyh0cnVlKVxuICB9LCBbXSlcblxuICAvLyBSZWxvYWQgd2hlbiBrZXkgY2hhbmdlcyAoZm9yIHJlZnJlc2ggZnJvbSBwYXJlbnQpXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKGtleSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICBsb2FkVHJhbnNhY3Rpb25zKHRydWUpXG4gICAgfVxuICB9LCBba2V5XSlcblxuICAvLyBSZWxvYWQgd2hlbiBmaWx0ZXJzIGNoYW5nZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxvYWRUcmFuc2FjdGlvbnModHJ1ZSlcbiAgfSwgW2ZpbHRlcnNdKVxuXG4gIGNvbnN0IGhhbmRsZVJlZnJlc2ggPSAoKSA9PiB7XG4gICAgbG9hZFRyYW5zYWN0aW9ucyh0cnVlKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlTG9hZE1vcmUgPSAoKSA9PiB7XG4gICAgaWYgKCFsb2FkaW5nTW9yZSAmJiBoYXNNb3JlSXRlbXMpIHtcbiAgICAgIGxvYWRUcmFuc2FjdGlvbnMoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgaGFuZGxlU2VhcmNoID0gKHF1ZXJ5OiBzdHJpbmcpID0+IHtcbiAgICBzZXRGaWx0ZXJzKHByZXYgPT4gKHsgLi4ucHJldiwgc2VhcmNoUXVlcnk6IHF1ZXJ5IH0pKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlRmlsdGVyQ2hhbmdlID0gKG5ld0ZpbHRlcnM6IFBhcnRpYWw8VHJhbnNhY3Rpb25GaWx0ZXJzPikgPT4ge1xuICAgIHNldEZpbHRlcnMocHJldiA9PiAoeyAuLi5wcmV2LCAuLi5uZXdGaWx0ZXJzIH0pKVxuICB9XG5cbiAgY29uc3QgaGFuZGxlRWRpdCA9ICh0cmFuc2FjdGlvbjogSVRyYW5zYWN0aW9uKSA9PiB7IFxuICAgIG9uRWRpdFRyYW5zYWN0aW9uPy4odHJhbnNhY3Rpb24pIFxuICB9XG5cbiAgY29uc3QgaGFuZGxlRGVsZXRlID0gYXN5bmMgKGlkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIWNvbmZpcm0oJ0FyZSB5b3Ugc3VyZSB5b3Ugd2FudCB0byBkZWxldGUgdGhpcyB0cmFuc2FjdGlvbj8nKSkge1xuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IEV4cGVuc2VTZXJ2aWNlLmRlbGV0ZVRyYW5zYWN0aW9uKGlkKVxuICAgICAgdG9hc3Quc3VjY2VzcygnVHJhbnNhY3Rpb24gZGVsZXRlZCBzdWNjZXNzZnVsbHknKVxuICAgICAgbG9hZFRyYW5zYWN0aW9ucyh0cnVlKVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgdG9hc3QuZXJyb3IoJ0ZhaWxlZCB0byBkZWxldGUgdHJhbnNhY3Rpb24nKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGZvcm1hdEN1cnJlbmN5ID0gKGFtb3VudDogbnVtYmVyKSA9PiBgJCR7YW1vdW50LnRvRml4ZWQoMil9YFxuICBjb25zdCBmb3JtYXREYXRlID0gKGRhdGU6IHN0cmluZyB8IERhdGUpID0+IG5ldyBEYXRlKGRhdGUpLnRvTG9jYWxlRGF0ZVN0cmluZygpXG5cbiAgLy8gUXVpY2sgZGF0ZSBmaWx0ZXIgZnVuY3Rpb25zXG4gIGNvbnN0IFthY3RpdmVRdWlja0ZpbHRlciwgc2V0QWN0aXZlUXVpY2tGaWx0ZXJdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbClcbiAgXG4gIGNvbnN0IHNldFF1aWNrRGF0ZUZpbHRlciA9IChwZXJpb2Q6ICd3ZWVrJyB8ICdtb250aCcgfCAneWVhcicpID0+IHtcbiAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKClcbiAgICBsZXQgc3RhcnREYXRlOiBEYXRlXG5cbiAgICBzd2l0Y2ggKHBlcmlvZCkge1xuICAgICAgY2FzZSAnd2Vlayc6XG4gICAgICAgIHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKHRvZGF5KVxuICAgICAgICBzdGFydERhdGUuc2V0RGF0ZSh0b2RheS5nZXREYXRlKCkgLSA3KVxuICAgICAgICBicmVha1xuICAgICAgY2FzZSAnbW9udGgnOlxuICAgICAgICBzdGFydERhdGUgPSBuZXcgRGF0ZSh0b2RheSlcbiAgICAgICAgc3RhcnREYXRlLnNldE1vbnRoKHRvZGF5LmdldE1vbnRoKCkgLSAxKVxuICAgICAgICBicmVha1xuICAgICAgY2FzZSAneWVhcic6XG4gICAgICAgIHN0YXJ0RGF0ZSA9IG5ldyBEYXRlKHRvZGF5KVxuICAgICAgICBzdGFydERhdGUuc2V0RnVsbFllYXIodG9kYXkuZ2V0RnVsbFllYXIoKSAtIDEpXG4gICAgICAgIGJyZWFrXG4gICAgfVxuXG4gICAgc2V0QWN0aXZlUXVpY2tGaWx0ZXIocGVyaW9kKVxuICAgIHNldEZpbHRlcnMocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIHN0YXJ0RGF0ZTogc3RhcnREYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSxcbiAgICAgIGVuZERhdGU6IHRvZGF5LnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXVxuICAgIH0pKVxuICB9XG4gIFxuICBjb25zdCBjbGVhckRhdGVGaWx0ZXIgPSAoKSA9PiB7XG4gICAgc2V0QWN0aXZlUXVpY2tGaWx0ZXIoJ2FsbCcpXG4gICAgc2V0RmlsdGVycyhwcmV2ID0+ICh7IC4uLnByZXYsIHN0YXJ0RGF0ZTogJycsIGVuZERhdGU6ICcnIH0pKVxuICB9XG5cbiAgY29uc3QgW3Nob3dGaWx0ZXJzLCBzZXRTaG93RmlsdGVyc10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3NlYXJjaElucHV0LCBzZXRTZWFyY2hJbnB1dF0gPSB1c2VTdGF0ZSgnJylcbiAgY29uc3QgbG9hZE1vcmVSZWYgPSB1c2VSZWY8SFRNTERpdkVsZW1lbnQ+KG51bGwpXG5cbiAgLy8gU3luYyBzZWFyY2ggaW5wdXQgd2l0aCBmaWx0ZXJzXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0U2VhcmNoSW5wdXQoZmlsdGVycy5zZWFyY2hRdWVyeSlcbiAgfSwgW2ZpbHRlcnMuc2VhcmNoUXVlcnldKVxuXG4gIC8vIERlYm91bmNlZCBzZWFyY2hcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIGlmIChzZWFyY2hJbnB1dCAhPT0gZmlsdGVycy5zZWFyY2hRdWVyeSkge1xuICAgICAgICBzZXRGaWx0ZXJzKHByZXYgPT4gKHsgLi4ucHJldiwgc2VhcmNoUXVlcnk6IHNlYXJjaElucHV0IH0pKVxuICAgICAgfVxuICAgIH0sIDUwMClcblxuICAgIHJldHVybiAoKSA9PiBjbGVhclRpbWVvdXQodGltZW91dElkKVxuICB9LCBbc2VhcmNoSW5wdXQsIGZpbHRlcnMuc2VhcmNoUXVlcnldKVxuXG4gIC8vIEluZmluaXRlIHNjcm9sbCBpbXBsZW1lbnRhdGlvblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IG9ic2VydmVyID0gbmV3IEludGVyc2VjdGlvbk9ic2VydmVyKFxuICAgICAgKGVudHJpZXMpID0+IHtcbiAgICAgICAgaWYgKGVudHJpZXNbMF0uaXNJbnRlcnNlY3RpbmcgJiYgaGFzTW9yZUl0ZW1zICYmICFsb2FkaW5nTW9yZSkge1xuICAgICAgICAgIGhhbmRsZUxvYWRNb3JlKClcbiAgICAgICAgfVxuICAgICAgfSxcbiAgICAgIHsgdGhyZXNob2xkOiAwLjEgfVxuICAgIClcblxuICAgIGlmIChsb2FkTW9yZVJlZi5jdXJyZW50KSB7XG4gICAgICBvYnNlcnZlci5vYnNlcnZlKGxvYWRNb3JlUmVmLmN1cnJlbnQpXG4gICAgfVxuXG4gICAgcmV0dXJuICgpID0+IG9ic2VydmVyLmRpc2Nvbm5lY3QoKVxuICB9LCBbaGFzTW9yZUl0ZW1zLCBsb2FkaW5nTW9yZSwgaGFuZGxlTG9hZE1vcmVdKVxuXG4gIGNvbnN0IGhhbmRsZVNlYXJjaFN1Ym1pdCA9IChlOiBSZWFjdC5Gb3JtRXZlbnQpID0+IHtcbiAgICBlLnByZXZlbnREZWZhdWx0KClcbiAgICBoYW5kbGVTZWFyY2goc2VhcmNoSW5wdXQpXG4gIH1cblxuICBjb25zdCBjbGVhckZpbHRlcnMgPSAoKSA9PiB7XG4gICAgY29uc3QgY2xlYXJlZEZpbHRlcnMgPSB7XG4gICAgICBzZWFyY2hRdWVyeTogJycsXG4gICAgICBjYXRlZ29yeUlkOiAnJyxcbiAgICAgIHN0YXJ0RGF0ZTogJycsXG4gICAgICBlbmREYXRlOiAnJyxcbiAgICAgIHRyYW5zYWN0aW9uVHlwZTogJ2FsbCcgYXMgY29uc3RcbiAgICB9XG4gICAgc2V0QWN0aXZlUXVpY2tGaWx0ZXIobnVsbClcbiAgICBzZXRGaWx0ZXJzKGNsZWFyZWRGaWx0ZXJzKVxuICB9XG5cbiAgY29uc3QgaGFzQWN0aXZlRmlsdGVycyA9IGZpbHRlcnMuc2VhcmNoUXVlcnkgfHwgZmlsdGVycy5jYXRlZ29yeUlkIHx8IFxuICAgIGZpbHRlcnMuc3RhcnREYXRlIHx8IGZpbHRlcnMuZW5kRGF0ZSB8fCBmaWx0ZXJzLnRyYW5zYWN0aW9uVHlwZSAhPT0gJ2FsbCdcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctd2hpdGUgcm91bmRlZC14bCBzaGFkb3ctbGcgYm9yZGVyIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgey8qIEhlYWRlciB3aXRoIFNlYXJjaCBhbmQgRmlsdGVycyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHgtNiBweS01IGJvcmRlci1iIGJvcmRlci1ncmF5LTIwMFwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgc206ZmxleC1yb3cgc206aXRlbXMtY2VudGVyIHNtOmp1c3RpZnktYmV0d2VlbiBnYXAtNFwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIHRleHQtZ3JheS05MDBcIj5cbiAgICAgICAgICAgICAgVHJhbnNhY3Rpb25zXG4gICAgICAgICAgICA8L2gyPlxuICAgICAgICAgICAge3RvdGFsQ291bnQgPiAwICYmIChcbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwIG10LTFcIj5cbiAgICAgICAgICAgICAgICB7dG90YWxDb3VudH0gdHJhbnNhY3Rpb257KHRvdGFsQ291bnQgYXMgbnVtYmVyKSA9PT0gMSA/ICcnIDogJ3MnfSBmb3VuZFxuICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTNcIj5cbiAgICAgICAgICAgIHsvKiBTZWFyY2ggRm9ybSAqL31cbiAgICAgICAgICAgIDxmb3JtIG9uU3VibWl0PXtoYW5kbGVTZWFyY2hTdWJtaXR9IGNsYXNzTmFtZT1cImZsZXgtMSBzbTpmbGV4LWluaXRpYWxcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJTZWFyY2ggYnkgZGVzY3JpcHRpb24gb3IgY2F0ZWdvcnkuLi5cIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e3NlYXJjaElucHV0fVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRTZWFyY2hJbnB1dChlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgc206dy02NCBwbC0xMCBwci00IHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLWxnIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LXktMCBsZWZ0LTAgcGwtMyBmbGV4IGl0ZW1zLWNlbnRlciBwb2ludGVyLWV2ZW50cy1ub25lXCI+XG4gICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1ncmF5LTQwMFwiIGZpbGw9XCJub25lXCIgc3Ryb2tlPVwiY3VycmVudENvbG9yXCIgdmlld0JveD1cIjAgMCAyNCAyNFwiPlxuICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMjEgMjFsLTYtNm0yLTVhNyA3IDAgMTEtMTQgMCA3IDcgMCAwMTE0IDB6XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZm9ybT5cblxuICAgICAgICAgICAgey8qIEZpbHRlciBUb2dnbGUgKi99XG4gICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dGaWx0ZXJzKCFzaG93RmlsdGVycyl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTQgcHktMiByb3VuZGVkLWxnIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgc2hvd0ZpbHRlcnNcbiAgICAgICAgICAgICAgICAgID8gJ2JnLWJsdWUtNjAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICA6ICdiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNzAwIGhvdmVyOmJnLWdyYXktMjAwJ1xuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImgtNSB3LTVcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA2VjRtMCAyYTIgMiAwIDEwMCA0bTAtNGEyIDIgMCAxMTAgNG0tNiA4YTIgMiAwIDEwMC00bTAgNGEyIDIgMCAxMDAgNG0wLTR2Mm0wLTZWNG02IDZ2MTBtNi0yYTIgMiAwIDEwMC00bTAgNGEyIDIgMCAxMDAgNG0wLTR2Mm0wLTZWNFwiIC8+XG4gICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgRmlsdGVyc1xuICAgICAgICAgICAgICAgIHtoYXNBY3RpdmVGaWx0ZXJzICYmIChcbiAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImJnLXJlZC01MDAgdGV4dC13aGl0ZSByb3VuZGVkLWZ1bGwgdy0yIGgtMlwiPjwvc3Bhbj5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICA8L2J1dHRvbj5cblxuICAgICAgICAgICAgey8qIFJlZnJlc2ggQnV0dG9uICovfVxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBvbkNsaWNrPXtoYW5kbGVSZWZyZXNofVxuICAgICAgICAgICAgICBkaXNhYmxlZD17bG9hZGluZ31cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwicHgtNCBweS0yIGJnLWdyYXktMTAwIGhvdmVyOmJnLWdyYXktMjAwIHRleHQtZ3JheS03MDAgcm91bmRlZC1sZyBmb250LW1lZGl1bSB0cmFuc2l0aW9uLWNvbG9ycyBkaXNhYmxlZDpvcGFjaXR5LTUwXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9e2BoLTUgdy01ICR7bG9hZGluZyA/ICdhbmltYXRlLXNwaW4nIDogJyd9YH0gZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgPHBhdGggc3Ryb2tlTGluZWNhcD1cInJvdW5kXCIgc3Ryb2tlTGluZWpvaW49XCJyb3VuZFwiIHN0cm9rZVdpZHRoPXsyfSBkPVwiTTQgNHY1aC41ODJtMTUuMzU2IDJBOC4wMDEgOC4wMDEgMCAwMDQuNTgyIDltMCAwSDltMTEgMTF2LTVoLS41ODFtMCAwYTguMDAzIDguMDAzIDAgMDEtMTUuMzU3LTJtMTUuMzU3IDJIMTVcIiAvPlxuICAgICAgICAgICAgICA8L3N2Zz5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogRmlsdGVyIFBhbmVsICovfVxuICAgICAgICB7c2hvd0ZpbHRlcnMgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNCBwLTQgYmctZ3JheS01MCByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICB7LyogUXVpY2sgRGF0ZSBGaWx0ZXJzICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi02XCI+XG4gICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMlwiPlxuICAgICAgICAgICAgICAgIFF1aWNrIERhdGUgRmlsdGVyc1xuICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0UXVpY2tEYXRlRmlsdGVyKCd3ZWVrJyl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0zIHB5LTEgdGV4dC1zbSByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZVF1aWNrRmlsdGVyID09PSAnd2VlaydcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWJsdWUtMTAwIGhvdmVyOmJnLWJsdWUtMjAwIHRleHQtYmx1ZS03MDAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBMYXN0IDcgRGF5c1xuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFF1aWNrRGF0ZUZpbHRlcignbW9udGgnKX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHB4LTMgcHktMSB0ZXh0LXNtIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgJHtcbiAgICAgICAgICAgICAgICAgICAgYWN0aXZlUXVpY2tGaWx0ZXIgPT09ICdtb250aCdcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWJsdWUtMTAwIGhvdmVyOmJnLWJsdWUtMjAwIHRleHQtYmx1ZS03MDAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBMYXN0IE1vbnRoXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0UXVpY2tEYXRlRmlsdGVyKCd5ZWFyJyl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0zIHB5LTEgdGV4dC1zbSByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZVF1aWNrRmlsdGVyID09PSAneWVhcidcbiAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1ibHVlLTYwMCB0ZXh0LXdoaXRlJ1xuICAgICAgICAgICAgICAgICAgICAgIDogJ2JnLWJsdWUtMTAwIGhvdmVyOmJnLWJsdWUtMjAwIHRleHQtYmx1ZS03MDAnXG4gICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBMYXN0IFllYXJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXtjbGVhckRhdGVGaWx0ZXJ9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BweC0zIHB5LTEgdGV4dC1zbSByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzICR7XG4gICAgICAgICAgICAgICAgICAgIGFjdGl2ZVF1aWNrRmlsdGVyID09PSAnYWxsJ1xuICAgICAgICAgICAgICAgICAgICAgID8gJ2JnLWdyYXktNjAwIHRleHQtd2hpdGUnXG4gICAgICAgICAgICAgICAgICAgICAgOiAnYmctZ3JheS0xMDAgaG92ZXI6YmctZ3JheS0yMDAgdGV4dC1ncmF5LTcwMCdcbiAgICAgICAgICAgICAgICAgIH1gfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIEFsbCBUaW1lXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBzbTpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNFwiPlxuICAgICAgICAgICAgICB7LyogQ2F0ZWdvcnkgRmlsdGVyICovfVxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgQ2F0ZWdvcnlcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxzZWxlY3RcbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtmaWx0ZXJzLmNhdGVnb3J5SWR9XG4gICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUZpbHRlckNoYW5nZSh7IGNhdGVnb3J5SWQ6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBweC0zIHB5LTIgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8b3B0aW9uIHZhbHVlPVwiXCI+QWxsIENhdGVnb3JpZXM8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIHtjYXRlZ29yaWVzLm1hcCgoY2F0ZWdvcnkpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPG9wdGlvbiBrZXk9e2NhdGVnb3J5LmlkfSB2YWx1ZT17Y2F0ZWdvcnkuaWR9PlxuICAgICAgICAgICAgICAgICAgICAgIHtjYXRlZ29yeS5pY29ufSB7Y2F0ZWdvcnkubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgPC9vcHRpb24+XG4gICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgey8qIFRyYW5zYWN0aW9uIFR5cGUgRmlsdGVyICovfVxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgVHlwZVxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2ZpbHRlcnMudHJhbnNhY3Rpb25UeXBlfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVGaWx0ZXJDaGFuZ2UoeyBcbiAgICAgICAgICAgICAgICAgICAgdHJhbnNhY3Rpb25UeXBlOiBlLnRhcmdldC52YWx1ZSBhcyAnYWxsJyB8ICdpbmNvbWUnIHwgJ2V4cGVuc2UnIFxuICAgICAgICAgICAgICAgICAgfSl9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ3LWZ1bGwgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIHB4LTMgcHktMiBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMCBmb2N1czpib3JkZXItdHJhbnNwYXJlbnRcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJhbGxcIj5BbGwgVHlwZXM8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJpbmNvbWVcIj5JbmNvbWU8L29wdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCJleHBlbnNlXCI+RXhwZW5zZTwvb3B0aW9uPlxuICAgICAgICAgICAgICAgIDwvc2VsZWN0PlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogU3RhcnQgRGF0ZSBGaWx0ZXIgKi99XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMCBtYi0xXCI+XG4gICAgICAgICAgICAgICAgICBGcm9tIERhdGVcbiAgICAgICAgICAgICAgICA8L2xhYmVsPlxuICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgdHlwZT1cImRhdGVcIlxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2ZpbHRlcnMuc3RhcnREYXRlfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVGaWx0ZXJDaGFuZ2UoeyBzdGFydERhdGU6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBweC0zIHB5LTIgZm9jdXM6cmluZy0yIGZvY3VzOnJpbmctYmx1ZS01MDAgZm9jdXM6Ym9yZGVyLXRyYW5zcGFyZW50XCJcbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICB7LyogRW5kIERhdGUgRmlsdGVyICovfVxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJibG9jayB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtZ3JheS03MDAgbWItMVwiPlxuICAgICAgICAgICAgICAgICAgVG8gRGF0ZVxuICAgICAgICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICB0eXBlPVwiZGF0ZVwiXG4gICAgICAgICAgICAgICAgICB2YWx1ZT17ZmlsdGVycy5lbmREYXRlfVxuICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVGaWx0ZXJDaGFuZ2UoeyBlbmREYXRlOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIHJvdW5kZWQtbWQgcHgtMyBweS0yIGZvY3VzOnJpbmctMiBmb2N1czpyaW5nLWJsdWUtNTAwIGZvY3VzOmJvcmRlci10cmFuc3BhcmVudFwiXG4gICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgey8qIENsZWFyIEZpbHRlcnMgKi99XG4gICAgICAgICAgICB7aGFzQWN0aXZlRmlsdGVycyAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtNFwiPlxuICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9e2NsZWFyRmlsdGVyc31cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1ibHVlLTYwMCBob3Zlcjp0ZXh0LWJsdWUtODAwIGZvbnQtbWVkaXVtXCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICBDbGVhciBhbGwgZmlsdGVyc1xuICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIEVycm9yIE1lc3NhZ2UgKi99XG4gICAgICB7ZXJyb3IgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBiZy1yZWQtNTAgYm9yZGVyLWwtNCBib3JkZXItcmVkLTQwMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleFwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtbC0zXCI+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNzAwXCI+e2Vycm9yfTwvcD5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBMb2FkaW5nIFN0YXRlICovfVxuICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicC04IHRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItYmx1ZS02MDAgbXgtYXV0b1wiPjwvZGl2PlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTIgdGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcgdHJhbnNhY3Rpb25zLi4uPC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICkgOiB0cmFuc2FjdGlvbnMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB5LTE2IHB4LTggdGV4dC1jZW50ZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm14LWF1dG8gdy0yNCBoLTI0IGJnLWdyYXktMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBtYi02XCI+XG4gICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cInctMTIgaC0xMiB0ZXh0LWdyYXktNDAwXCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xMiA4Yy0xLjY1NyAwLTMgLjg5NS0zIDJzMS4zNDMgMiAzIDIgMyAuODk1IDMgMi0xLjM0MyAyLTMgMm0wLThjMS4xMSAwIDIuMDguNDAyIDIuNTk5IDFNMTIgOFY3bTAgMXY4bTAgMHYxbTAtMWMtMS4xMSAwLTIuMDgtLjQwMi0yLjU5OS0xXCIgLz5cbiAgICAgICAgICAgIDwvc3ZnPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMCBtYi0yXCI+Tm8gdHJhbnNhY3Rpb25zIGZvdW5kPC9oMz5cbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNjAwIG1heC13LXNtIG14LWF1dG9cIj5cbiAgICAgICAgICAgIHtoYXNBY3RpdmVGaWx0ZXJzIFxuICAgICAgICAgICAgICA/ICdUcnkgYWRqdXN0aW5nIHlvdXIgZmlsdGVycyBvciBzZWFyY2ggdGVybXMgdG8gZmluZCBtb3JlIHRyYW5zYWN0aW9ucydcbiAgICAgICAgICAgICAgOiAnQWRkIHlvdXIgZmlyc3QgZXhwZW5zZSBvciBpbmNvbWUgdG8gZ2V0IHN0YXJ0ZWQhJ1xuICAgICAgICAgICAgfVxuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICApIDogKFxuICAgICAgICA8PlxuICAgICAgICAgIHsvKiBUcmFuc2FjdGlvbiBMaXN0ICovfVxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZGl2aWRlLXkgZGl2aWRlLWdyYXktMjAwXCI+XG4gICAgICAgICAgICB7dHJhbnNhY3Rpb25zLm1hcCgodHJhbnNhY3Rpb24pID0+IChcbiAgICAgICAgICAgICAgPGRpdiBcbiAgICAgICAgICAgICAgICBrZXk9e3RyYW5zYWN0aW9uLmlkfSBcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC00IHB5LTMgaG92ZXI6YmctZ3JheS01MCB0cmFuc2l0aW9uLWNvbG9ycyBncm91cFwiXG4gICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14bCBmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAge3RyYW5zYWN0aW9uLmNhdGVnb3J5Py5pY29uIHx8ICfwn5KwJ31cbiAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy0wXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWdyYXktOTAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt0cmFuc2FjdGlvbi5jYXRlZ29yeT8ubmFtZSB8fCAnVW5jYXRlZ29yaXplZCd9XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwIG1sLTIgZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7Zm9ybWF0RGF0ZSh0cmFuc2FjdGlvbi50cmFuc2FjdGlvbl9kYXRlKX1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICB7dHJhbnNhY3Rpb24uZGVzY3JpcHRpb24gJiYgKFxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIHRydW5jYXRlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHt0cmFuc2FjdGlvbi5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMyBtbC00XCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT17YHRleHQtYmFzZSBmb250LXNlbWlib2xkICR7XG4gICAgICAgICAgICAgICAgICAgICAgICB0cmFuc2FjdGlvbi50cmFuc2FjdGlvbl90eXBlID09PSAnaW5jb21lJyBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPyAndGV4dC1ncmVlbi02MDAnIFxuICAgICAgICAgICAgICAgICAgICAgICAgICA6ICd0ZXh0LXJlZC02MDAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9PlxuICAgICAgICAgICAgICAgICAgICAgICAge3RyYW5zYWN0aW9uLnRyYW5zYWN0aW9uX3R5cGUgPT09ICdpbmNvbWUnID8gJysnIDogJy0nfVxuICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdEN1cnJlbmN5KHRyYW5zYWN0aW9uLmFtb3VudCl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICB7LyogQWN0aW9uIEJ1dHRvbnMgKi99XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBzcGFjZS14LTEgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHtvbkVkaXRUcmFuc2FjdGlvbiAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZUVkaXQodHJhbnNhY3Rpb24pfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTEuNSB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtYmx1ZS02MDAgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkVkaXQgdHJhbnNhY3Rpb25cIlxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3ZnIGNsYXNzTmFtZT1cImgtNCB3LTRcIiBmaWxsPVwibm9uZVwiIHN0cm9rZT1cImN1cnJlbnRDb2xvclwiIHZpZXdCb3g9XCIwIDAgMjQgMjRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cGF0aCBzdHJva2VMaW5lY2FwPVwicm91bmRcIiBzdHJva2VMaW5lam9pbj1cInJvdW5kXCIgc3Ryb2tlV2lkdGg9ezJ9IGQ9XCJNMTEgNUg2YTIgMiAwIDAwLTIgMnYxMWEyIDIgMCAwMDIgMmgxMWEyIDIgMCAwMDItMnYtNW0tMS40MTQtOS40MTRhMiAyIDAgMTEyLjgyOCAyLjgyOEwxMS44MjggMTVIOXYtMi44MjhsOC41ODYtOC41ODZ6XCIgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZURlbGV0ZSh0cmFuc2FjdGlvbi5pZCl9XG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTEuNSB0ZXh0LWdyYXktNDAwIGhvdmVyOnRleHQtcmVkLTYwMCB0cmFuc2l0aW9uLWNvbG9yc1wiXG4gICAgICAgICAgICAgICAgICAgICAgICB0aXRsZT1cIkRlbGV0ZSB0cmFuc2FjdGlvblwiXG4gICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHN2ZyBjbGFzc05hbWU9XCJoLTQgdy00XCIgZmlsbD1cIm5vbmVcIiBzdHJva2U9XCJjdXJyZW50Q29sb3JcIiB2aWV3Qm94PVwiMCAwIDI0IDI0XCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIDxwYXRoIHN0cm9rZUxpbmVjYXA9XCJyb3VuZFwiIHN0cm9rZUxpbmVqb2luPVwicm91bmRcIiBzdHJva2VXaWR0aD17Mn0gZD1cIk0xOSA3bC0uODY3IDEyLjE0MkEyIDIgMCAwMTE2LjEzOCAyMUg3Ljg2MmEyIDIgMCAwMS0xLjk5NS0xLjg1OEw1IDdtNSA0djZtNC02djZtMS0xMFY0YTEgMSAwIDAwLTEtMWgtNGExIDEgMCAwMC0xIDF2M000IDdoMTZcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9zdmc+XG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKSl9XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICB7LyogTG9hZCBNb3JlICovfVxuICAgICAgICAgIHtoYXNNb3JlSXRlbXMgJiYgKFxuICAgICAgICAgICAgPGRpdiByZWY9e2xvYWRNb3JlUmVmfSBjbGFzc05hbWU9XCJwLTYgdGV4dC1jZW50ZXIgYm9yZGVyLXRcIj5cbiAgICAgICAgICAgICAge2xvYWRpbmdNb3JlID8gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYW5pbWF0ZS1zcGluIHJvdW5kZWQtZnVsbCBoLTYgdy02IGJvcmRlci1iLTIgYm9yZGVyLWJsdWUtNjAwXCI+PC9kaXY+XG4gICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yIHRleHQtZ3JheS02MDBcIj5Mb2FkaW5nIG1vcmUuLi48L3NwYW4+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlTG9hZE1vcmV9XG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJsdWUtNjAwIGhvdmVyOnRleHQtYmx1ZS04MDAgZm9udC1tZWRpdW1cIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIExvYWQgTW9yZSBUcmFuc2FjdGlvbnNcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgIDwvPlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKVxufVxuXG5leHBvcnQgZGVmYXVsdCBUcmFuc2FjdGlvbkxpc3RXZWIiXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZVJlZiIsInVzZUVmZmVjdCIsIkV4cGVuc2VTZXJ2aWNlIiwidG9hc3QiLCJUcmFuc2FjdGlvbkxpc3RXZWIiLCJvbkVkaXRUcmFuc2FjdGlvbiIsImtleSIsInByb3BzIiwidHJhbnNhY3Rpb25zIiwic2V0VHJhbnNhY3Rpb25zIiwiY2F0ZWdvcmllcyIsInNldENhdGVnb3JpZXMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImxvYWRpbmdNb3JlIiwic2V0TG9hZGluZ01vcmUiLCJlcnJvciIsInNldEVycm9yIiwidG90YWxDb3VudCIsInNldFRvdGFsQ291bnQiLCJmaWx0ZXJzIiwic2V0RmlsdGVycyIsInNlYXJjaFF1ZXJ5IiwiY2F0ZWdvcnlJZCIsInN0YXJ0RGF0ZSIsImVuZERhdGUiLCJ0cmFuc2FjdGlvblR5cGUiLCJvZmZzZXQiLCJzZXRPZmZzZXQiLCJoYXNNb3JlSXRlbXMiLCJzZXRIYXNNb3JlSXRlbXMiLCJJVEVNU19QRVJfUEFHRSIsImxvYWRUcmFuc2FjdGlvbnMiLCJyZXNldCIsImN1cnJlbnRPZmZzZXQiLCJvcHRpb25zIiwibGltaXQiLCJyZXN1bHQiLCJnZXRUcmFuc2FjdGlvbnMiLCJkYXRhIiwicHJldiIsImNvdW50IiwibGVuZ3RoIiwiZXJyIiwiRXJyb3IiLCJtZXNzYWdlIiwibG9hZENhdGVnb3JpZXMiLCJjYXRlZ29yaWVzRGF0YSIsImdldENhdGVnb3JpZXMiLCJjb25zb2xlIiwidW5kZWZpbmVkIiwiaGFuZGxlUmVmcmVzaCIsImhhbmRsZUxvYWRNb3JlIiwiaGFuZGxlU2VhcmNoIiwicXVlcnkiLCJoYW5kbGVGaWx0ZXJDaGFuZ2UiLCJuZXdGaWx0ZXJzIiwiaGFuZGxlRWRpdCIsInRyYW5zYWN0aW9uIiwiaGFuZGxlRGVsZXRlIiwiaWQiLCJjb25maXJtIiwiZGVsZXRlVHJhbnNhY3Rpb24iLCJzdWNjZXNzIiwiZm9ybWF0Q3VycmVuY3kiLCJhbW91bnQiLCJ0b0ZpeGVkIiwiZm9ybWF0RGF0ZSIsImRhdGUiLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwiYWN0aXZlUXVpY2tGaWx0ZXIiLCJzZXRBY3RpdmVRdWlja0ZpbHRlciIsInNldFF1aWNrRGF0ZUZpbHRlciIsInBlcmlvZCIsInRvZGF5Iiwic2V0RGF0ZSIsImdldERhdGUiLCJzZXRNb250aCIsImdldE1vbnRoIiwic2V0RnVsbFllYXIiLCJnZXRGdWxsWWVhciIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJjbGVhckRhdGVGaWx0ZXIiLCJzaG93RmlsdGVycyIsInNldFNob3dGaWx0ZXJzIiwic2VhcmNoSW5wdXQiLCJzZXRTZWFyY2hJbnB1dCIsImxvYWRNb3JlUmVmIiwidGltZW91dElkIiwic2V0VGltZW91dCIsImNsZWFyVGltZW91dCIsIm9ic2VydmVyIiwiSW50ZXJzZWN0aW9uT2JzZXJ2ZXIiLCJlbnRyaWVzIiwiaXNJbnRlcnNlY3RpbmciLCJ0aHJlc2hvbGQiLCJjdXJyZW50Iiwib2JzZXJ2ZSIsImRpc2Nvbm5lY3QiLCJoYW5kbGVTZWFyY2hTdWJtaXQiLCJlIiwicHJldmVudERlZmF1bHQiLCJjbGVhckZpbHRlcnMiLCJjbGVhcmVkRmlsdGVycyIsImhhc0FjdGl2ZUZpbHRlcnMiLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsInAiLCJmb3JtIiwib25TdWJtaXQiLCJpbnB1dCIsInR5cGUiLCJwbGFjZWhvbGRlciIsInZhbHVlIiwib25DaGFuZ2UiLCJ0YXJnZXQiLCJzdmciLCJmaWxsIiwic3Ryb2tlIiwidmlld0JveCIsInBhdGgiLCJzdHJva2VMaW5lY2FwIiwic3Ryb2tlTGluZWpvaW4iLCJzdHJva2VXaWR0aCIsImQiLCJidXR0b24iLCJvbkNsaWNrIiwic3BhbiIsImRpc2FibGVkIiwibGFiZWwiLCJzZWxlY3QiLCJvcHRpb24iLCJtYXAiLCJjYXRlZ29yeSIsImljb24iLCJuYW1lIiwiaDMiLCJ0cmFuc2FjdGlvbl9kYXRlIiwiZGVzY3JpcHRpb24iLCJ0cmFuc2FjdGlvbl90eXBlIiwidGl0bGUiLCJyZWYiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TransactionList.tsx\n"));

/***/ })

});