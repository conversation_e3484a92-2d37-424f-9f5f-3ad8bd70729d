"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/budgets/page",{

/***/ "(app-pages-browser)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AccountDropdown__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AccountDropdown */ \"(app-pages-browser)/./src/components/AccountDropdown.tsx\");\n/* harmony import */ var _ThemeToggleButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ThemeToggleButton */ \"(app-pages-browser)/./src/components/ThemeToggleButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Navbar(param) {\n    let { currentPage } = param;\n    const navItems = [\n        {\n            href: '/dashboard',\n            label: 'Dashboard',\n            key: 'dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            href: '/expenses',\n            label: 'Expenses',\n            key: 'expenses',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            href: '/budgets',\n            label: 'Budgets',\n            key: 'budgets',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 002 2z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-surface-elevated border-b border-border-light shadow-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/dashboard\",\n                            className: \"flex items-center gap-3 text-xl font-semibold text-text-primary hover:text-text-secondary transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-white\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 114 0 2 2 0 01-4 0zm8-2a2 2 0 100 4 2 2 0 000-4z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this),\n                                \"Portfolio Tracker\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-1\",\n                        children: navItems.map((item)=>{\n                            const isActive = item.key === currentPage;\n                            if (isActive) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-2 rounded-lg text-white text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        item.icon,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.key, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 19\n                                }, this);\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                className: \"flex items-center gap-2 px-4 py-2 rounded-lg text-text-secondary hover:text-text-primary hover:bg-surface transition-all\",\n                                children: [\n                                    item.icon,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, item.key, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggleButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AccountDropdown__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n_c = Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Navbar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ThemeToggleButton.tsx":
/*!**********************************************!*\
  !*** ./src/components/ThemeToggleButton.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeToggleButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(app-pages-browser)/./src/contexts/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ThemeToggleButton() {\n    _s();\n    const { theme, resolvedTheme, setTheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    const handleToggle = ()=>{\n        if (resolvedTheme === 'light') {\n            setTheme('dark');\n        } else {\n            setTheme('light');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleToggle,\n        className: \"relative w-10 h-10 rounded-lg bg-surface hover:bg-surface-elevated border border-border-light hover:border-border transition-all duration-200 flex items-center justify-center group\",\n        \"aria-label\": \"Switch to \".concat(resolvedTheme === 'light' ? 'dark' : 'light', \" mode\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"absolute w-5 h-5 text-warning-orange transition-all duration-300 \".concat(resolvedTheme === 'light' ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 rotate-90 scale-75'),\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z\",\n                    clipRule: \"evenodd\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ThemeToggleButton.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ThemeToggleButton.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"absolute w-5 h-5 text-primary-purple transition-all duration-300 \".concat(resolvedTheme === 'dark' ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 -rotate-90 scale-75'),\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ThemeToggleButton.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ThemeToggleButton.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 \".concat(resolvedTheme === 'light' ? 'bg-warning-orange/5' : 'bg-primary-purple/5')\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ThemeToggleButton.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ThemeToggleButton.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n_s(ThemeToggleButton, \"6XqfAubXb2IiL1uQRqn5jKk2Erw=\", false, function() {\n    return [\n        _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__.useTheme\n    ];\n});\n_c = ThemeToggleButton;\nvar _c;\n$RefreshReg$(_c, \"ThemeToggleButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ThemeToggleButton.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider(param) {\n    let { children } = param;\n    _s();\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    // Load theme from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const savedTheme = localStorage.getItem('theme');\n            if (savedTheme && [\n                'light',\n                'dark',\n                'system'\n            ].includes(savedTheme)) {\n                setThemeState(savedTheme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Update resolved theme based on theme preference and system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const updateResolvedTheme = {\n                \"ThemeProvider.useEffect.updateResolvedTheme\": ()=>{\n                    if (theme === 'system') {\n                        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n                        setResolvedTheme(systemPrefersDark ? 'dark' : 'light');\n                    } else {\n                        setResolvedTheme(theme);\n                    }\n                }\n            }[\"ThemeProvider.useEffect.updateResolvedTheme\"];\n            updateResolvedTheme();\n            // Listen for system theme changes\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": ()=>{\n                    if (theme === 'system') {\n                        updateResolvedTheme();\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener('change', handleChange)\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme\n    ]);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const root = document.documentElement;\n            if (resolvedTheme === 'dark') {\n                root.classList.add('dark');\n                root.setAttribute('data-theme', 'dark');\n            } else {\n                root.classList.remove('dark');\n                root.setAttribute('data-theme', 'light');\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        resolvedTheme\n    ]);\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        localStorage.setItem('theme', newTheme);\n    };\n    const toggleTheme = ()=>{\n        if (theme === 'light') {\n            setTheme('dark');\n        } else if (theme === 'dark') {\n            setTheme('system');\n        } else {\n            setTheme('light');\n        }\n    };\n    const value = {\n        theme,\n        resolvedTheme,\n        setTheme,\n        toggleTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/contexts/ThemeContext.tsx\",\n        lineNumber: 89,\n        columnNumber: 10\n    }, this);\n}\n_s(ThemeProvider, \"Q0EfSnp/c4d6atTvTyxcBLa3aOQ=\");\n_c = ThemeProvider;\nfunction useTheme() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n_s1(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ThemeContext.tsx\n"));

/***/ })

});