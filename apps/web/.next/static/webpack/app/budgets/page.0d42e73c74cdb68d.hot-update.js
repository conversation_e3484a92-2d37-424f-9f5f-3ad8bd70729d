"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/budgets/page",{

/***/ "(app-pages-browser)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider(param) {\n    let { children } = param;\n    _s();\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    // Load theme from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const savedTheme = localStorage.getItem('theme');\n            if (savedTheme && [\n                'light',\n                'dark',\n                'system'\n            ].includes(savedTheme)) {\n                setThemeState(savedTheme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Update resolved theme based on theme preference and system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const updateResolvedTheme = {\n                \"ThemeProvider.useEffect.updateResolvedTheme\": ()=>{\n                    if (theme === 'system') {\n                        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n                        setResolvedTheme(systemPrefersDark ? 'dark' : 'light');\n                    } else {\n                        setResolvedTheme(theme);\n                    }\n                }\n            }[\"ThemeProvider.useEffect.updateResolvedTheme\"];\n            updateResolvedTheme();\n            // Listen for system theme changes\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": ()=>{\n                    if (theme === 'system') {\n                        updateResolvedTheme();\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener('change', handleChange)\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme\n    ]);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const root = document.documentElement;\n            if (resolvedTheme === 'dark') {\n                root.classList.add('dark');\n                root.setAttribute('data-theme', 'dark');\n            } else {\n                root.classList.remove('dark');\n                root.setAttribute('data-theme', 'light');\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        resolvedTheme\n    ]);\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        localStorage.setItem('theme', newTheme);\n    };\n    const toggleTheme = ()=>{\n        if (theme === 'light') {\n            setTheme('dark');\n        } else if (theme === 'dark') {\n            setTheme('system');\n        } else {\n            setTheme('light');\n        }\n    };\n    const value = {\n        theme,\n        resolvedTheme,\n        setTheme,\n        toggleTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/contexts/ThemeContext.tsx\",\n        lineNumber: 89,\n        columnNumber: 10\n    }, this);\n}\n_s(ThemeProvider, \"Q0EfSnp/c4d6atTvTyxcBLa3aOQ=\");\n_c = ThemeProvider;\nfunction useTheme() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n_s1(useTheme, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb250ZXh0cy9UaGVtZUNvbnRleHQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBNkU7QUFXN0UsTUFBTUssNkJBQWVKLG9EQUFhQSxDQUErQks7QUFNMUQsU0FBU0MsY0FBYyxLQUFnQztRQUFoQyxFQUFFQyxRQUFRLEVBQXNCLEdBQWhDOztJQUM1QixNQUFNLENBQUNDLE9BQU9DLGNBQWMsR0FBR04sK0NBQVFBLENBQVE7SUFDL0MsTUFBTSxDQUFDTyxlQUFlQyxpQkFBaUIsR0FBR1IsK0NBQVFBLENBQW1CO0lBRXJFLHdDQUF3QztJQUN4Q0QsZ0RBQVNBO21DQUFDO1lBQ1IsTUFBTVUsYUFBYUMsYUFBYUMsT0FBTyxDQUFDO1lBQ3hDLElBQUlGLGNBQWM7Z0JBQUM7Z0JBQVM7Z0JBQVE7YUFBUyxDQUFDRyxRQUFRLENBQUNILGFBQWE7Z0JBQ2xFSCxjQUFjRztZQUNoQjtRQUNGO2tDQUFHLEVBQUU7SUFFTCx3RUFBd0U7SUFDeEVWLGdEQUFTQTttQ0FBQztZQUNSLE1BQU1jOytEQUFzQjtvQkFDMUIsSUFBSVIsVUFBVSxVQUFVO3dCQUN0QixNQUFNUyxvQkFBb0JDLE9BQU9DLFVBQVUsQ0FBQyxnQ0FBZ0NDLE9BQU87d0JBQ25GVCxpQkFBaUJNLG9CQUFvQixTQUFTO29CQUNoRCxPQUFPO3dCQUNMTixpQkFBaUJIO29CQUNuQjtnQkFDRjs7WUFFQVE7WUFFQSxrQ0FBa0M7WUFDbEMsTUFBTUssYUFBYUgsT0FBT0MsVUFBVSxDQUFDO1lBQ3JDLE1BQU1HO3dEQUFlO29CQUNuQixJQUFJZCxVQUFVLFVBQVU7d0JBQ3RCUTtvQkFDRjtnQkFDRjs7WUFFQUssV0FBV0UsZ0JBQWdCLENBQUMsVUFBVUQ7WUFDdEM7MkNBQU8sSUFBTUQsV0FBV0csbUJBQW1CLENBQUMsVUFBVUY7O1FBQ3hEO2tDQUFHO1FBQUNkO0tBQU07SUFFViwwQkFBMEI7SUFDMUJOLGdEQUFTQTttQ0FBQztZQUNSLE1BQU11QixPQUFPQyxTQUFTQyxlQUFlO1lBQ3JDLElBQUlqQixrQkFBa0IsUUFBUTtnQkFDNUJlLEtBQUtHLFNBQVMsQ0FBQ0MsR0FBRyxDQUFDO2dCQUNuQkosS0FBS0ssWUFBWSxDQUFDLGNBQWM7WUFDbEMsT0FBTztnQkFDTEwsS0FBS0csU0FBUyxDQUFDRyxNQUFNLENBQUM7Z0JBQ3RCTixLQUFLSyxZQUFZLENBQUMsY0FBYztZQUNsQztRQUNGO2tDQUFHO1FBQUNwQjtLQUFjO0lBRWxCLE1BQU1zQixXQUFXLENBQUNDO1FBQ2hCeEIsY0FBY3dCO1FBQ2RwQixhQUFhcUIsT0FBTyxDQUFDLFNBQVNEO0lBQ2hDO0lBRUEsTUFBTUUsY0FBYztRQUNsQixJQUFJM0IsVUFBVSxTQUFTO1lBQ3JCd0IsU0FBUztRQUNYLE9BQU8sSUFBSXhCLFVBQVUsUUFBUTtZQUMzQndCLFNBQVM7UUFDWCxPQUFPO1lBQ0xBLFNBQVM7UUFDWDtJQUNGO0lBRUEsTUFBTUksUUFBMEI7UUFDOUI1QjtRQUNBRTtRQUNBc0I7UUFDQUc7SUFDRjtJQUVBLHFCQUFPLDhEQUFDL0IsYUFBYWlDLFFBQVE7UUFBQ0QsT0FBT0E7a0JBQVE3Qjs7Ozs7O0FBQy9DO0dBeEVnQkQ7S0FBQUE7QUEwRVQsU0FBU2dDOztJQUNkLE1BQU1DLFVBQVV0QyxpREFBVUEsQ0FBQ0c7SUFDM0IsSUFBSW1DLFlBQVlsQyxXQUFXO1FBQ3pCLE1BQU0sSUFBSW1DLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNUO0lBTmdCRCIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9hcHBzL3dlYi9zcmMvY29udGV4dHMvVGhlbWVDb250ZXh0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUmVhY3QsIHsgY3JlYXRlQ29udGV4dCwgdXNlQ29udGV4dCwgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuXG5leHBvcnQgdHlwZSBUaGVtZSA9ICdsaWdodCcgfCAnZGFyaycgfCAnc3lzdGVtJ1xuXG5pbnRlcmZhY2UgVGhlbWVDb250ZXh0VHlwZSB7XG4gIHRoZW1lOiBUaGVtZVxuICByZXNvbHZlZFRoZW1lOiAnbGlnaHQnIHwgJ2RhcmsnXG4gIHNldFRoZW1lOiAodGhlbWU6IFRoZW1lKSA9PiB2b2lkXG4gIHRvZ2dsZVRoZW1lOiAoKSA9PiB2b2lkXG59XG5cbmNvbnN0IFRoZW1lQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8VGhlbWVDb250ZXh0VHlwZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKVxuXG5leHBvcnQgaW50ZXJmYWNlIFRoZW1lUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgY29uc3QgW3RoZW1lLCBzZXRUaGVtZVN0YXRlXSA9IHVzZVN0YXRlPFRoZW1lPignc3lzdGVtJylcbiAgY29uc3QgW3Jlc29sdmVkVGhlbWUsIHNldFJlc29sdmVkVGhlbWVdID0gdXNlU3RhdGU8J2xpZ2h0JyB8ICdkYXJrJz4oJ2xpZ2h0JylcblxuICAvLyBMb2FkIHRoZW1lIGZyb20gbG9jYWxTdG9yYWdlIG9uIG1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3Qgc2F2ZWRUaGVtZSA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0aGVtZScpIGFzIFRoZW1lXG4gICAgaWYgKHNhdmVkVGhlbWUgJiYgWydsaWdodCcsICdkYXJrJywgJ3N5c3RlbSddLmluY2x1ZGVzKHNhdmVkVGhlbWUpKSB7XG4gICAgICBzZXRUaGVtZVN0YXRlKHNhdmVkVGhlbWUpXG4gICAgfVxuICB9LCBbXSlcblxuICAvLyBVcGRhdGUgcmVzb2x2ZWQgdGhlbWUgYmFzZWQgb24gdGhlbWUgcHJlZmVyZW5jZSBhbmQgc3lzdGVtIHByZWZlcmVuY2VcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB1cGRhdGVSZXNvbHZlZFRoZW1lID0gKCkgPT4ge1xuICAgICAgaWYgKHRoZW1lID09PSAnc3lzdGVtJykge1xuICAgICAgICBjb25zdCBzeXN0ZW1QcmVmZXJzRGFyayA9IHdpbmRvdy5tYXRjaE1lZGlhKCcocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspJykubWF0Y2hlc1xuICAgICAgICBzZXRSZXNvbHZlZFRoZW1lKHN5c3RlbVByZWZlcnNEYXJrID8gJ2RhcmsnIDogJ2xpZ2h0JylcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHNldFJlc29sdmVkVGhlbWUodGhlbWUpXG4gICAgICB9XG4gICAgfVxuXG4gICAgdXBkYXRlUmVzb2x2ZWRUaGVtZSgpXG5cbiAgICAvLyBMaXN0ZW4gZm9yIHN5c3RlbSB0aGVtZSBjaGFuZ2VzXG4gICAgY29uc3QgbWVkaWFRdWVyeSA9IHdpbmRvdy5tYXRjaE1lZGlhKCcocHJlZmVycy1jb2xvci1zY2hlbWU6IGRhcmspJylcbiAgICBjb25zdCBoYW5kbGVDaGFuZ2UgPSAoKSA9PiB7XG4gICAgICBpZiAodGhlbWUgPT09ICdzeXN0ZW0nKSB7XG4gICAgICAgIHVwZGF0ZVJlc29sdmVkVGhlbWUoKVxuICAgICAgfVxuICAgIH1cblxuICAgIG1lZGlhUXVlcnkuYWRkRXZlbnRMaXN0ZW5lcignY2hhbmdlJywgaGFuZGxlQ2hhbmdlKVxuICAgIHJldHVybiAoKSA9PiBtZWRpYVF1ZXJ5LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2NoYW5nZScsIGhhbmRsZUNoYW5nZSlcbiAgfSwgW3RoZW1lXSlcblxuICAvLyBBcHBseSB0aGVtZSB0byBkb2N1bWVudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHJvb3QgPSBkb2N1bWVudC5kb2N1bWVudEVsZW1lbnRcbiAgICBpZiAocmVzb2x2ZWRUaGVtZSA9PT0gJ2RhcmsnKSB7XG4gICAgICByb290LmNsYXNzTGlzdC5hZGQoJ2RhcmsnKVxuICAgICAgcm9vdC5zZXRBdHRyaWJ1dGUoJ2RhdGEtdGhlbWUnLCAnZGFyaycpXG4gICAgfSBlbHNlIHtcbiAgICAgIHJvb3QuY2xhc3NMaXN0LnJlbW92ZSgnZGFyaycpXG4gICAgICByb290LnNldEF0dHJpYnV0ZSgnZGF0YS10aGVtZScsICdsaWdodCcpXG4gICAgfVxuICB9LCBbcmVzb2x2ZWRUaGVtZV0pXG5cbiAgY29uc3Qgc2V0VGhlbWUgPSAobmV3VGhlbWU6IFRoZW1lKSA9PiB7XG4gICAgc2V0VGhlbWVTdGF0ZShuZXdUaGVtZSlcbiAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgndGhlbWUnLCBuZXdUaGVtZSlcbiAgfVxuXG4gIGNvbnN0IHRvZ2dsZVRoZW1lID0gKCkgPT4ge1xuICAgIGlmICh0aGVtZSA9PT0gJ2xpZ2h0Jykge1xuICAgICAgc2V0VGhlbWUoJ2RhcmsnKVxuICAgIH0gZWxzZSBpZiAodGhlbWUgPT09ICdkYXJrJykge1xuICAgICAgc2V0VGhlbWUoJ3N5c3RlbScpXG4gICAgfSBlbHNlIHtcbiAgICAgIHNldFRoZW1lKCdsaWdodCcpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgdmFsdWU6IFRoZW1lQ29udGV4dFR5cGUgPSB7XG4gICAgdGhlbWUsXG4gICAgcmVzb2x2ZWRUaGVtZSxcbiAgICBzZXRUaGVtZSxcbiAgICB0b2dnbGVUaGVtZSxcbiAgfVxuXG4gIHJldHVybiA8VGhlbWVDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+e2NoaWxkcmVufTwvVGhlbWVDb250ZXh0LlByb3ZpZGVyPlxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlVGhlbWUoKSB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KFRoZW1lQ29udGV4dClcbiAgaWYgKGNvbnRleHQgPT09IHVuZGVmaW5lZCkge1xuICAgIHRocm93IG5ldyBFcnJvcigndXNlVGhlbWUgbXVzdCBiZSB1c2VkIHdpdGhpbiBhIFRoZW1lUHJvdmlkZXInKVxuICB9XG4gIHJldHVybiBjb250ZXh0XG59Il0sIm5hbWVzIjpbIlJlYWN0IiwiY3JlYXRlQ29udGV4dCIsInVzZUNvbnRleHQiLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIlRoZW1lQ29udGV4dCIsInVuZGVmaW5lZCIsIlRoZW1lUHJvdmlkZXIiLCJjaGlsZHJlbiIsInRoZW1lIiwic2V0VGhlbWVTdGF0ZSIsInJlc29sdmVkVGhlbWUiLCJzZXRSZXNvbHZlZFRoZW1lIiwic2F2ZWRUaGVtZSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJpbmNsdWRlcyIsInVwZGF0ZVJlc29sdmVkVGhlbWUiLCJzeXN0ZW1QcmVmZXJzRGFyayIsIndpbmRvdyIsIm1hdGNoTWVkaWEiLCJtYXRjaGVzIiwibWVkaWFRdWVyeSIsImhhbmRsZUNoYW5nZSIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwicm9vdCIsImRvY3VtZW50IiwiZG9jdW1lbnRFbGVtZW50IiwiY2xhc3NMaXN0IiwiYWRkIiwic2V0QXR0cmlidXRlIiwicmVtb3ZlIiwic2V0VGhlbWUiLCJuZXdUaGVtZSIsInNldEl0ZW0iLCJ0b2dnbGVUaGVtZSIsInZhbHVlIiwiUHJvdmlkZXIiLCJ1c2VUaGVtZSIsImNvbnRleHQiLCJFcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/ThemeContext.tsx\n"));

/***/ })

});