/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/budgets/page";
exports.ids = ["app/budgets/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fbudgets%2Fpage&page=%2Fbudgets%2Fpage&appPaths=%2Fbudgets%2Fpage&pagePath=private-next-app-dir%2Fbudgets%2Fpage.tsx&appDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fbudgets%2Fpage&page=%2Fbudgets%2Fpage&appPaths=%2Fbudgets%2Fpage&pagePath=private-next-app-dir%2Fbudgets%2Fpage.tsx&appDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/budgets/page.tsx */ \"(rsc)/./src/app/budgets/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'budgets',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/budgets/page\",\n        pathname: \"/budgets\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fbudgets%2Fpage&page=%2Fbudgets%2Fpage&appPaths=%2Fbudgets%2Fpage&pagePath=private-next-app-dir%2Fbudgets%2Fpage.tsx&appDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Fbudgets%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Fbudgets%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/budgets/page.tsx */ \"(rsc)/./src/app/budgets/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJhdmludGglMkZ3b3Jrc3BhY2VzJTJGc3RhcnR1cCUyRnBvcnRmb2xpb190cmFja2VyJTJGYXBwcyUyRndlYiUyRnNyYyUyRmFwcCUyRmJ1ZGdldHMlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQTJIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL2FwcHMvd2ViL3NyYy9hcHAvYnVkZ2V0cy9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Fbudgets%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJhdmludGglMkZ3b3Jrc3BhY2VzJTJGc3RhcnR1cCUyRnBvcnRmb2xpb190cmFja2VyJTJGYXBwcyUyRndlYiUyRnNyYyUyRmFwcCUyRmxheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFxSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9hcHBzL3dlYi9zcmMvYXBwL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hcmF2aW50aC93b3Jrc3BhY2VzL3N0YXJ0dXAvcG9ydGZvbGlvX3RyYWNrZXIvYXBwcy93ZWIvc3JjL2FwcC9mYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/budgets/page.tsx":
/*!**********************************!*\
  !*** ./src/app/budgets/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!****************************************************************!*\
  !*** ../../node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \****************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/../../packages/shared/src/database.types.ts":
/*!***************************************************!*\
  !*** ../../packages/shared/src/database.types.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Constants: () => (/* binding */ Constants)\n/* harmony export */ });\nconst Constants = {\n    public: {\n        Enums: {}\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/database.types.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/index.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/index.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsService: () => (/* reexport safe */ _lib_analytics__WEBPACK_IMPORTED_MODULE_7__.AnalyticsService),\n/* harmony export */   BiometricService: () => (/* reexport safe */ _lib_biometric_web__WEBPACK_IMPORTED_MODULE_9__.BiometricService),\n/* harmony export */   BudgetService: () => (/* reexport safe */ _lib_budget__WEBPACK_IMPORTED_MODULE_6__.BudgetService),\n/* harmony export */   Constants: () => (/* reexport safe */ _database_types__WEBPACK_IMPORTED_MODULE_3__.Constants),\n/* harmony export */   ExpenseService: () => (/* reexport safe */ _lib_expenses__WEBPACK_IMPORTED_MODULE_5__.ExpenseService),\n/* harmony export */   RecurringTransactionService: () => (/* reexport safe */ _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_8__.RecurringTransactionService),\n/* harmony export */   budgetFormInputSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_12__.budgetFormInputSchema),\n/* harmony export */   budgetFormSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_12__.budgetFormSchema),\n/* harmony export */   budgetSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_12__.budgetSchema),\n/* harmony export */   calculateBudgetProgress: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.calculateBudgetProgress),\n/* harmony export */   categorySchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.categorySchema),\n/* harmony export */   expenseFormInputSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_11__.expenseFormInputSchema),\n/* harmony export */   expenseFormSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_11__.expenseFormSchema),\n/* harmony export */   expenseSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_11__.expenseSchema),\n/* harmony export */   formatCurrency: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatCurrency),\n/* harmony export */   formatDate: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatDate),\n/* harmony export */   resetPasswordSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_10__.resetPasswordSchema),\n/* harmony export */   signInSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_10__.signInSchema),\n/* harmony export */   signUpSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_10__.signUpSchema),\n/* harmony export */   supabase: () => (/* reexport safe */ _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase),\n/* harmony export */   supabaseMobile: () => (/* reexport safe */ _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_14__.supabase),\n/* harmony export */   transactionSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.transactionSchema),\n/* harmony export */   useCurrencyStore: () => (/* reexport safe */ _stores_currencyStore__WEBPACK_IMPORTED_MODULE_13__.useCurrencyStore)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(ssr)/../../packages/shared/src/types.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/../../packages/shared/src/utils.ts\");\n/* harmony import */ var _validators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./validators */ \"(ssr)/../../packages/shared/src/validators.ts\");\n/* harmony import */ var _database_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./database.types */ \"(ssr)/../../packages/shared/src/database.types.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/supabase */ \"(ssr)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _lib_expenses__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/expenses */ \"(ssr)/../../packages/shared/src/lib/expenses.ts\");\n/* harmony import */ var _lib_budget__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/budget */ \"(ssr)/../../packages/shared/src/lib/budget.ts\");\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/analytics */ \"(ssr)/../../packages/shared/src/lib/analytics.ts\");\n/* harmony import */ var _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/recurring-transactions */ \"(ssr)/../../packages/shared/src/lib/recurring-transactions.ts\");\n/* harmony import */ var _lib_biometric_web__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/biometric.web */ \"(ssr)/../../packages/shared/src/lib/biometric.web.ts\");\n/* harmony import */ var _schemas_auth__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./schemas/auth */ \"(ssr)/../../packages/shared/src/schemas/auth.ts\");\n/* harmony import */ var _schemas_expense__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./schemas/expense */ \"(ssr)/../../packages/shared/src/schemas/expense.ts\");\n/* harmony import */ var _schemas_budget__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./schemas/budget */ \"(ssr)/../../packages/shared/src/schemas/budget.ts\");\n/* harmony import */ var _stores_currencyStore__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./stores/currencyStore */ \"(ssr)/../../packages/shared/src/stores/currencyStore.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./lib/supabase.mobile */ \"(ssr)/../../packages/shared/src/lib/supabase.mobile.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_14__);\n// Shared business logic and utilities\n\n\n\n\n\n\n\n\n\n// Platform-specific biometric exports\n\n\n\n\n\n// Platform-specific exports (explicit re-export to avoid naming conflicts)\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvc2hhcmVkL3NyYy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxzQ0FBc0M7QUFDZDtBQUNBO0FBQ0s7QUFDSTtBQUNGO0FBQ0E7QUFDRjtBQUNHO0FBQ2E7QUFDN0Msc0NBQXNDO0FBQ0Y7QUFDTDtBQUNHO0FBQ0Q7QUFDTTtBQUV2QywyRUFBMkU7QUFDUiIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9wYWNrYWdlcy9zaGFyZWQvc3JjL2luZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNoYXJlZCBidXNpbmVzcyBsb2dpYyBhbmQgdXRpbGl0aWVzXG5leHBvcnQgKiBmcm9tICcuL3R5cGVzJztcbmV4cG9ydCAqIGZyb20gJy4vdXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi92YWxpZGF0b3JzJztcbmV4cG9ydCAqIGZyb20gJy4vZGF0YWJhc2UudHlwZXMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvc3VwYWJhc2UnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvZXhwZW5zZXMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvYnVkZ2V0JztcbmV4cG9ydCAqIGZyb20gJy4vbGliL2FuYWx5dGljcyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9yZWN1cnJpbmctdHJhbnNhY3Rpb25zJztcbi8vIFBsYXRmb3JtLXNwZWNpZmljIGJpb21ldHJpYyBleHBvcnRzXG5leHBvcnQgKiBmcm9tICcuL2xpYi9iaW9tZXRyaWMud2ViJztcbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hcy9hdXRoJztcbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hcy9leHBlbnNlJztcbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hcy9idWRnZXQnO1xuZXhwb3J0ICogZnJvbSAnLi9zdG9yZXMvY3VycmVuY3lTdG9yZSc7XG5cbi8vIFBsYXRmb3JtLXNwZWNpZmljIGV4cG9ydHMgKGV4cGxpY2l0IHJlLWV4cG9ydCB0byBhdm9pZCBuYW1pbmcgY29uZmxpY3RzKVxuZXhwb3J0IHsgc3VwYWJhc2UgYXMgc3VwYWJhc2VNb2JpbGUgfSBmcm9tICcuL2xpYi9zdXBhYmFzZS5tb2JpbGUnOyJdLCJuYW1lcyI6WyJzdXBhYmFzZSIsInN1cGFiYXNlTW9iaWxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/lib/analytics.ts":
/*!**************************************************!*\
  !*** ../../packages/shared/src/lib/analytics.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsService: () => (/* binding */ AnalyticsService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/../../packages/shared/src/lib/supabase.ts\");\n\nclass AnalyticsService {\n    static async getAnalyticsData(dateRange) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Default to last 6 months if no range provided\n        const endDate = dateRange?.endDate || new Date().toISOString().split('T')[0];\n        const startDate = dateRange?.startDate || new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(`\n        *,\n        category:categories(*)\n      `).eq('user_id', user.id).gte('transaction_date', startDate).lte('transaction_date', endDate).order('transaction_date', {\n            ascending: true\n        });\n        if (error) {\n            throw new Error(`Failed to fetch analytics data: ${error.message}`);\n        }\n        const transactions = data;\n        return this.processAnalyticsData(transactions);\n    }\n    static processAnalyticsData(transactions) {\n        // Calculate totals\n        const totalIncome = transactions.filter((t)=>t.transaction_type === 'income').reduce((sum, t)=>sum + t.amount, 0);\n        const totalExpenses = transactions.filter((t)=>t.transaction_type === 'expense').reduce((sum, t)=>sum + t.amount, 0);\n        const netIncome = totalIncome - totalExpenses;\n        // Category breakdown\n        const categoryMap = {};\n        transactions.forEach((transaction)=>{\n            if (transaction.category) {\n                const categoryId = transaction.category.id;\n                if (!categoryMap[categoryId]) {\n                    categoryMap[categoryId] = {\n                        category: transaction.category,\n                        total: 0,\n                        count: 0\n                    };\n                }\n                categoryMap[categoryId].total += transaction.amount;\n                categoryMap[categoryId].count += 1;\n            }\n        });\n        const categoryBreakdown = Object.values(categoryMap).map((item)=>({\n                category: item.category,\n                total: item.total,\n                percentage: totalExpenses > 0 ? item.total / totalExpenses * 100 : 0\n            })).sort((a, b)=>b.total - a.total);\n        // Monthly trends\n        const monthlyMap = {};\n        transactions.forEach((transaction)=>{\n            const monthKey = transaction.transaction_date.substring(0, 7) // YYYY-MM\n            ;\n            if (!monthlyMap[monthKey]) {\n                monthlyMap[monthKey] = {\n                    income: 0,\n                    expenses: 0\n                };\n            }\n            if (transaction.transaction_type === 'income') {\n                monthlyMap[monthKey].income += transaction.amount;\n            } else {\n                monthlyMap[monthKey].expenses += transaction.amount;\n            }\n        });\n        const monthlyTrends = Object.entries(monthlyMap).map(([month, data])=>({\n                month,\n                income: data.income,\n                expenses: data.expenses,\n                net: data.income - data.expenses\n            })).sort((a, b)=>a.month.localeCompare(b.month));\n        // Top categories\n        const topCategories = Object.values(categoryMap).map((item)=>({\n                category: item.category,\n                total: item.total,\n                transactionCount: item.count\n            })).sort((a, b)=>b.total - a.total).slice(0, 5);\n        return {\n            totalIncome,\n            totalExpenses,\n            netIncome,\n            categoryBreakdown,\n            monthlyTrends,\n            topCategories\n        };\n    }\n    static async getSpendingTrends(months = 12) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const endDate = new Date();\n        const startDate = new Date();\n        startDate.setMonth(startDate.getMonth() - months);\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('amount, transaction_type, transaction_date').eq('user_id', user.id).gte('transaction_date', startDate.toISOString().split('T')[0]).lte('transaction_date', endDate.toISOString().split('T')[0]).order('transaction_date', {\n            ascending: true\n        });\n        if (error) {\n            throw new Error(`Failed to fetch spending trends: ${error.message}`);\n        }\n        const transactions = data;\n        const monthlyData = {};\n        transactions.forEach((transaction)=>{\n            const monthKey = transaction.transaction_date.substring(0, 7);\n            if (!monthlyData[monthKey]) {\n                monthlyData[monthKey] = {\n                    income: 0,\n                    expenses: 0\n                };\n            }\n            if (transaction.transaction_type === 'income') {\n                monthlyData[monthKey].income += transaction.amount;\n            } else {\n                monthlyData[monthKey].expenses += transaction.amount;\n            }\n        });\n        return Object.entries(monthlyData).map(([month, data])=>({\n                month,\n                expenses: data.expenses,\n                income: data.income\n            })).sort((a, b)=>a.month.localeCompare(b.month));\n    }\n    static formatMonth(monthString) {\n        const date = new Date(monthString + '-01');\n        return date.toLocaleDateString('en-US', {\n            month: 'short',\n            year: 'numeric'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/lib/analytics.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/lib/biometric.web.ts":
/*!******************************************************!*\
  !*** ../../packages/shared/src/lib/biometric.web.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BiometricService: () => (/* binding */ BiometricService)\n/* harmony export */ });\nclass BiometricService {\n    /**\n   * Check if biometric authentication is available on the device\n   * Web implementation - always returns unavailable\n   */ static async getCapabilities() {\n        return {\n            isAvailable: false,\n            biometricType: [],\n            hasHardware: false,\n            isEnrolled: false\n        };\n    }\n    /**\n   * Authenticate user with biometrics\n   * Web implementation - always returns unavailable\n   */ static async authenticate(reason = 'Please authenticate to continue') {\n        return {\n            success: false,\n            error: 'Biometric authentication is not available on web'\n        };\n    }\n    /**\n   * Check if biometric login is enabled by the user\n   * Web implementation - always returns false\n   */ static async isBiometricEnabled() {\n        return false;\n    }\n    /**\n   * Enable or disable biometric login\n   * Web implementation - no-op\n   */ static async setBiometricEnabled(enabled) {\n    // No-op for web\n    }\n    /**\n   * Store encrypted credentials for biometric login\n   * Web implementation - no-op\n   */ static async storeCredentials(email, hashedPassword) {\n    // No-op for web\n    }\n    /**\n   * Retrieve stored credentials after successful biometric authentication\n   * Web implementation - always returns null\n   */ static async getStoredCredentials() {\n        return null;\n    }\n    /**\n   * Clear all biometric data\n   * Web implementation - no-op\n   */ static async clearBiometricData() {\n    // No-op for web\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/lib/biometric.web.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/lib/budget.ts":
/*!***********************************************!*\
  !*** ../../packages/shared/src/lib/budget.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BudgetService: () => (/* binding */ BudgetService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/../../packages/shared/src/lib/supabase.ts\");\n\nclass BudgetService {\n    static async createBudget(data) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const budgetData = {\n            name: data.name,\n            amount: data.amount,\n            period: data.period,\n            category_id: data.category_id || null,\n            start_date: data.start_date.toISOString().split('T')[0],\n            end_date: data.end_date ? data.end_date.toISOString().split('T')[0] : null,\n            user_id: user.id\n        };\n        const { data: budget, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('budgets').insert(budgetData).select(`\n        *,\n        category:categories(*)\n      `).single();\n        if (error) {\n            throw new Error(`Failed to create budget: ${error.message}`);\n        }\n        return budget;\n    }\n    static async getBudgets() {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('budgets').select(`\n        *,\n        category:categories(*)\n      `).eq('user_id', user.id).order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            throw new Error(`Failed to fetch budgets: ${error.message}`);\n        }\n        return data;\n    }\n    static async getBudgetWithProgress(budgetId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Fetch budget\n        const { data: budget, error: budgetError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('budgets').select(`\n        *,\n        category:categories(*)\n      `).eq('id', budgetId).eq('user_id', user.id).single();\n        if (budgetError) {\n            throw new Error(`Failed to fetch budget: ${budgetError.message}`);\n        }\n        // Calculate progress\n        const progress = await this.calculateBudgetProgress(budget);\n        return progress;\n    }\n    static async getBudgetsWithProgress() {\n        const budgets = await this.getBudgets();\n        const budgetsWithProgress = await Promise.all(budgets.map((budget)=>this.calculateBudgetProgress(budget)));\n        return budgetsWithProgress;\n    }\n    static async updateBudget(id, data) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const updateData = {};\n        if (data.name !== undefined) updateData.name = data.name;\n        if (data.amount !== undefined) updateData.amount = data.amount;\n        if (data.period !== undefined) updateData.period = data.period;\n        if (data.category_id !== undefined) updateData.category_id = data.category_id || null;\n        if (data.start_date !== undefined) {\n            updateData.start_date = data.start_date.toISOString().split('T')[0];\n        }\n        if (data.end_date !== undefined) {\n            updateData.end_date = data.end_date ? data.end_date.toISOString().split('T')[0] : null;\n        }\n        updateData.updated_at = new Date().toISOString();\n        const { data: budget, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('budgets').update(updateData).eq('id', id).eq('user_id', user.id).select(`\n        *,\n        category:categories(*)\n      `).single();\n        if (error) {\n            throw new Error(`Failed to update budget: ${error.message}`);\n        }\n        return budget;\n    }\n    static async deleteBudget(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('budgets').delete().eq('id', id).eq('user_id', user.id);\n        if (error) {\n            throw new Error(`Failed to delete budget: ${error.message}`);\n        }\n    }\n    static async calculateBudgetProgress(budget) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Calculate date range for budget period\n        const { startDate, endDate } = this.getBudgetDateRange(budget);\n        // Build query for transactions\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(`\n        *,\n        category:categories(*)\n      `).eq('user_id', user.id).eq('transaction_type', 'expense').gte('transaction_date', startDate).lte('transaction_date', endDate);\n        // Filter by category if budget is category-specific\n        if (budget.category_id) {\n            query = query.eq('category_id', budget.category_id);\n        }\n        const { data: transactions, error } = await query;\n        if (error) {\n            throw new Error(`Failed to fetch transactions for budget: ${error.message}`);\n        }\n        // Calculate spending\n        const spent = transactions?.reduce((sum, transaction)=>sum + transaction.amount, 0) || 0;\n        const remaining = Math.max(0, budget.amount - spent);\n        const progress = Math.min(100, spent / budget.amount * 100);\n        const isOverBudget = spent > budget.amount;\n        return {\n            ...budget,\n            spent,\n            remaining,\n            progress,\n            isOverBudget,\n            transactions: transactions\n        };\n    }\n    static getBudgetDateRange(budget) {\n        const budgetStartDate = new Date(budget.start_date);\n        const now = new Date();\n        // If budget has specific end date, use it\n        if (budget.end_date) {\n            return {\n                startDate: budget.start_date,\n                endDate: budget.end_date\n            };\n        }\n        // Calculate end date based on period\n        let endDate;\n        switch(budget.period){\n            case 'weekly':\n                endDate = new Date(budgetStartDate);\n                endDate.setDate(endDate.getDate() + 7);\n                break;\n            case 'monthly':\n                endDate = new Date(budgetStartDate);\n                endDate.setMonth(endDate.getMonth() + 1);\n                break;\n            case 'yearly':\n                endDate = new Date(budgetStartDate);\n                endDate.setFullYear(endDate.getFullYear() + 1);\n                break;\n            default:\n                endDate = new Date(budgetStartDate);\n                endDate.setMonth(endDate.getMonth() + 1);\n        }\n        // For recurring budgets, find the current period\n        while(endDate < now){\n            switch(budget.period){\n                case 'weekly':\n                    budgetStartDate.setDate(budgetStartDate.getDate() + 7);\n                    endDate.setDate(endDate.getDate() + 7);\n                    break;\n                case 'monthly':\n                    budgetStartDate.setMonth(budgetStartDate.getMonth() + 1);\n                    endDate.setMonth(endDate.getMonth() + 1);\n                    break;\n                case 'yearly':\n                    budgetStartDate.setFullYear(budgetStartDate.getFullYear() + 1);\n                    endDate.setFullYear(endDate.getFullYear() + 1);\n                    break;\n            }\n        }\n        return {\n            startDate: budgetStartDate.toISOString().split('T')[0],\n            endDate: endDate.toISOString().split('T')[0]\n        };\n    }\n    static async checkBudgetAlerts() {\n        const budgetsWithProgress = await this.getBudgetsWithProgress();\n        // Return budgets that are at 80% or over their limit\n        return budgetsWithProgress.filter((budget)=>budget.progress >= 80);\n    }\n    static async createRecurringBudgets() {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Get all budgets that don't have an end_date (recurring)\n        const { data: recurringBudgets, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('budgets').select('*').eq('user_id', user.id).is('end_date', null);\n        if (error) {\n            throw new Error(`Failed to fetch recurring budgets: ${error.message}`);\n        }\n        const now = new Date();\n        const newBudgets = [];\n        for (const budget of recurringBudgets || []){\n            const { endDate } = this.getBudgetDateRange(budget);\n            // If current period has ended, create new budget for next period\n            if (new Date(endDate) < now) {\n                const newStartDate = new Date(endDate);\n                newStartDate.setDate(newStartDate.getDate() + 1);\n                const newBudgetData = {\n                    name: budget.name,\n                    amount: budget.amount,\n                    period: budget.period,\n                    category_id: budget.category_id,\n                    start_date: newStartDate.toISOString().split('T')[0],\n                    end_date: null,\n                    user_id: user.id\n                };\n                const { data: newBudget, error: createError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('budgets').insert(newBudgetData).select(`\n            *,\n            category:categories(*)\n          `).single();\n                if (createError) {\n                    console.error(`Failed to create recurring budget: ${createError.message}`);\n                    continue;\n                }\n                newBudgets.push(newBudget);\n            }\n        }\n        return newBudgets;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/lib/budget.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/lib/expenses.ts":
/*!*************************************************!*\
  !*** ../../packages/shared/src/lib/expenses.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExpenseService: () => (/* binding */ ExpenseService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/../../packages/shared/src/lib/supabase.ts\");\n\nclass ExpenseService {\n    static async createTransaction(data) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const transactionData = {\n            amount: data.amount,\n            description: data.description || null,\n            category_id: data.category_id,\n            transaction_type: data.transaction_type,\n            transaction_date: data.transaction_date.toISOString().split('T')[0],\n            user_id: user.id\n        };\n        const { data: transaction, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(`\n        *,\n        category:categories(*)\n      `).single();\n        if (error) {\n            throw new Error(`Failed to create transaction: ${error.message}`);\n        }\n        return transaction;\n    }\n    static async getTransactions(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(`\n        *,\n        category:categories(*)\n      `, {\n            count: 'exact'\n        }).eq('user_id', user.id).order('transaction_date', {\n            ascending: false\n        }).order('created_at', {\n            ascending: false\n        });\n        // Apply filters\n        if (options?.categoryId) {\n            query = query.eq('category_id', options.categoryId);\n        }\n        if (options?.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options?.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options?.transactionType) {\n            query = query.eq('transaction_type', options.transactionType);\n        }\n        if (options?.searchQuery) {\n            // Search in both description and category name\n            query = query.or(`description.ilike.%${options.searchQuery}%,` + `category.name.ilike.%${options.searchQuery}%`);\n        }\n        // Apply pagination\n        if (options?.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options?.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n        }\n        const { data, error, count } = await query;\n        if (error) {\n            throw new Error(`Failed to fetch transactions: ${error.message}`);\n        }\n        return {\n            data: data,\n            count: count || 0\n        };\n    }\n    static async updateTransaction(id, data) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const updateData = {};\n        if (data.amount !== undefined) updateData.amount = data.amount;\n        if (data.description !== undefined) updateData.description = data.description;\n        if (data.category_id !== undefined) updateData.category_id = data.category_id;\n        if (data.transaction_type !== undefined) updateData.transaction_type = data.transaction_type;\n        if (data.transaction_date !== undefined) {\n            updateData.transaction_date = data.transaction_date.toISOString().split('T')[0];\n        }\n        updateData.updated_at = new Date().toISOString();\n        const { data: transaction, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').update(updateData).eq('id', id).eq('user_id', user.id).select(`\n        *,\n        category:categories(*)\n      `).single();\n        if (error) {\n            throw new Error(`Failed to update transaction: ${error.message}`);\n        }\n        return transaction;\n    }\n    static async deleteTransaction(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', id).eq('user_id', user.id);\n        if (error) {\n            throw new Error(`Failed to delete transaction: ${error.message}`);\n        }\n    }\n    static async getCategories() {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('*').or(`user_id.eq.${user.id},and(is_default.eq.true,user_id.is.null)`).order('name');\n        if (error) {\n            throw new Error(`Failed to fetch categories: ${error.message}`);\n        }\n        // If no categories exist for the user, create default categories\n        if (!data || data.length === 0) {\n            await this.createDefaultCategories();\n            // Fetch categories again after creating defaults\n            const { data: newData, error: newError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('*').or(`user_id.eq.${user.id},and(is_default.eq.true,user_id.is.null)`).order('name');\n            if (newError) {\n                throw new Error(`Failed to fetch categories after creating defaults: ${newError.message}`);\n            }\n            return newData;\n        }\n        return data;\n    }\n    static async createDefaultCategories() {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if user already has categories to prevent duplicates\n        const { data: existingCategories, error: checkError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('user_id', user.id).limit(1);\n        if (checkError) {\n            throw new Error(`Failed to check existing categories: ${checkError.message}`);\n        }\n        // If user already has categories, don't create defaults\n        if (existingCategories && existingCategories.length > 0) {\n            return;\n        }\n        const defaultCategories = [\n            // Expense categories\n            {\n                name: 'Food & Dining',\n                icon: '🍽️',\n                color: '#FF6B6B',\n                type: 'expense'\n            },\n            {\n                name: 'Transportation',\n                icon: '🚗',\n                color: '#4ECDC4',\n                type: 'expense'\n            },\n            {\n                name: 'Shopping',\n                icon: '🛍️',\n                color: '#45B7D1',\n                type: 'expense'\n            },\n            {\n                name: 'Entertainment',\n                icon: '🎬',\n                color: '#96CEB4',\n                type: 'expense'\n            },\n            {\n                name: 'Bills & Utilities',\n                icon: '💡',\n                color: '#FFEAA7',\n                type: 'expense'\n            },\n            {\n                name: 'Healthcare',\n                icon: '🏥',\n                color: '#DDA0DD',\n                type: 'expense'\n            },\n            {\n                name: 'Education',\n                icon: '📚',\n                color: '#98D8C8',\n                type: 'expense'\n            },\n            {\n                name: 'Travel',\n                icon: '✈️',\n                color: '#F7DC6F',\n                type: 'expense'\n            },\n            {\n                name: 'Groceries',\n                icon: '🛒',\n                color: '#82E0AA',\n                type: 'expense'\n            },\n            {\n                name: 'Other',\n                icon: '📦',\n                color: '#BDC3C7',\n                type: 'expense'\n            },\n            // Income categories\n            {\n                name: 'Salary',\n                icon: '💰',\n                color: '#27AE60',\n                type: 'income'\n            },\n            {\n                name: 'Freelance',\n                icon: '💻',\n                color: '#2ECC71',\n                type: 'income'\n            },\n            {\n                name: 'Business',\n                icon: '🏢',\n                color: '#58D68D',\n                type: 'income'\n            },\n            {\n                name: 'Investment',\n                icon: '📈',\n                color: '#85C1E9',\n                type: 'income'\n            },\n            {\n                name: 'Gift',\n                icon: '🎁',\n                color: '#F8C471',\n                type: 'income'\n            },\n            {\n                name: 'Other Income',\n                icon: '💎',\n                color: '#D5DBDB',\n                type: 'income'\n            }\n        ];\n        const categoriesToInsert = defaultCategories.map((category)=>({\n                ...category,\n                user_id: user.id,\n                is_default: false\n            }));\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').insert(categoriesToInsert);\n        if (error) {\n            throw new Error(`Failed to create default categories: ${error.message}`);\n        }\n    }\n    static async createCategory(categoryData) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').insert({\n            ...categoryData,\n            user_id: user.id,\n            is_default: false\n        }).select('*').single();\n        if (error) {\n            throw new Error(`Failed to create category: ${error.message}`);\n        }\n        return data;\n    }\n    static async getMonthlySpending(year, month) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const startDate = new Date(year, month - 1, 1).toISOString().split('T')[0];\n        const endDate = new Date(year, month, 0).toISOString().split('T')[0];\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(`\n        *,\n        category:categories(*)\n      `).eq('user_id', user.id).gte('transaction_date', startDate).lte('transaction_date', endDate);\n        if (error) {\n            throw new Error(`Failed to fetch monthly spending: ${error.message}`);\n        }\n        const transactions = data;\n        const totalIncome = transactions.filter((t)=>t.transaction_type === 'income').reduce((sum, t)=>sum + t.amount, 0);\n        const totalExpenses = transactions.filter((t)=>t.transaction_type === 'expense').reduce((sum, t)=>sum + t.amount, 0);\n        // Group by category\n        const categoryMap = {};\n        transactions.forEach((transaction)=>{\n            if (transaction.category) {\n                const categoryId = transaction.category.id;\n                if (!categoryMap[categoryId]) {\n                    categoryMap[categoryId] = {\n                        category: transaction.category,\n                        total: 0\n                    };\n                }\n                categoryMap[categoryId].total += transaction.amount;\n            }\n        });\n        const categoryBreakdown = Object.values(categoryMap);\n        return {\n            totalIncome,\n            totalExpenses,\n            categoryBreakdown\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/lib/expenses.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/lib/recurring-transactions.ts":
/*!***************************************************************!*\
  !*** ../../packages/shared/src/lib/recurring-transactions.ts ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecurringTransactionService: () => (/* binding */ RecurringTransactionService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/../../packages/shared/src/lib/supabase.ts\");\n\nclass RecurringTransactionService {\n    /**\n   * Get all recurring templates for the current user\n   */ static async getRecurringTemplates() {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transaction_templates').select('*').eq('user_id', user.id).eq('is_recurring', true).order('name');\n        if (error) {\n            throw new Error(`Failed to fetch recurring templates: ${error.message}`);\n        }\n        return data || [];\n    }\n    /**\n   * Get all due recurring transactions for the current user\n   */ static async getDueRecurringTransactions() {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transaction_templates').select(`\n        *,\n        category:categories(*)\n      `).eq('user_id', user.id).eq('is_recurring', true).not('next_due_date', 'is', null).lte('next_due_date', new Date().toISOString().split('T')[0]);\n        if (error) {\n            throw new Error(`Failed to fetch due recurring transactions: ${error.message}`);\n        }\n        const templates = data || [];\n        const today = new Date();\n        return templates.map((template)=>{\n            const dueDate = new Date(template.next_due_date);\n            const daysOverdue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));\n            return {\n                template: template,\n                daysOverdue: Math.max(0, daysOverdue),\n                nextDueDate: dueDate,\n                category: template.category\n            };\n        });\n    }\n    /**\n   * Calculate the next due date based on frequency\n   */ static calculateNextDueDate(currentDate, frequency) {\n        const nextDate = new Date(currentDate);\n        switch(frequency){\n            case 'weekly':\n                nextDate.setDate(nextDate.getDate() + 7);\n                break;\n            case 'monthly':\n                // Handle month-end edge cases\n                const originalDay = nextDate.getDate();\n                nextDate.setMonth(nextDate.getMonth() + 1);\n                // If the day doesn't exist in the next month (e.g., Jan 31 -> Feb 28)\n                if (nextDate.getDate() !== originalDay) {\n                    nextDate.setDate(0) // Set to last day of previous month\n                    ;\n                }\n                break;\n            case 'yearly':\n                nextDate.setFullYear(nextDate.getFullYear() + 1);\n                // Handle leap year edge case (Feb 29 -> Feb 28)\n                if (nextDate.getMonth() === 1 && nextDate.getDate() === 29) {\n                    // Check if next year is not a leap year\n                    const nextYear = nextDate.getFullYear();\n                    if (!this.isLeapYear(nextYear)) {\n                        nextDate.setDate(28);\n                    }\n                }\n                break;\n            default:\n                throw new Error(`Invalid frequency: ${frequency}`);\n        }\n        return nextDate;\n    }\n    /**\n   * Check if a year is a leap year\n   */ static isLeapYear(year) {\n        return year % 4 === 0 && year % 100 !== 0 || year % 400 === 0;\n    }\n    /**\n   * Create a transaction from a recurring template\n   */ static async createTransactionFromTemplate(template, transactionDate) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const date = transactionDate || new Date();\n        // Create the transaction\n        const transactionData = {\n            amount: template.amount,\n            category_id: template.category_id,\n            description: template.description,\n            transaction_type: template.transaction_type,\n            transaction_date: date.toISOString().split('T')[0],\n            user_id: user.id\n        };\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(`\n        *,\n        category:categories(*)\n      `).single();\n        if (transactionError) {\n            throw new Error(`Failed to create transaction: ${transactionError.message}`);\n        }\n        // Update the template with the next due date and last created date\n        if (template.frequency) {\n            const nextDueDate = this.calculateNextDueDate(date, template.frequency);\n            const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transaction_templates').update({\n                next_due_date: nextDueDate.toISOString().split('T')[0],\n                last_created_date: date.toISOString().split('T')[0]\n            }).eq('id', template.id);\n            if (updateError) {\n                console.error('Failed to update template next due date:', updateError);\n            // Don't throw error as transaction was created successfully\n            }\n        }\n        return transaction;\n    }\n    /**\n   * Create transactions from multiple templates\n   */ static async createTransactionsFromTemplates(templates, transactionDate) {\n        const success = [];\n        const errors = [];\n        for (const template of templates){\n            try {\n                const transaction = await this.createTransactionFromTemplate(template, transactionDate);\n                success.push(transaction);\n            } catch (error) {\n                errors.push({\n                    template,\n                    error: error instanceof Error ? error.message : 'Unknown error'\n                });\n            }\n        }\n        return {\n            success,\n            errors\n        };\n    }\n    /**\n   * Update a recurring template\n   */ static async updateRecurringTemplate(templateId, updates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // If frequency changed, recalculate next due date\n        if (updates.frequency && updates.last_created_date) {\n            const lastCreated = new Date(updates.last_created_date);\n            updates.next_due_date = this.calculateNextDueDate(lastCreated, updates.frequency).toISOString().split('T')[0];\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transaction_templates').update(updates).eq('id', templateId).eq('user_id', user.id).select().single();\n        if (error) {\n            throw new Error(`Failed to update recurring template: ${error.message}`);\n        }\n        return data;\n    }\n    /**\n   * Create a new recurring template\n   */ static async createRecurringTemplate(templateData) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Calculate initial next due date if recurring\n        let nextDueDate = null;\n        if (templateData.is_recurring && templateData.frequency) {\n            const startDate = new Date();\n            nextDueDate = this.calculateNextDueDate(startDate, templateData.frequency).toISOString().split('T')[0];\n        }\n        const insertData = {\n            ...templateData,\n            user_id: user.id,\n            next_due_date: nextDueDate\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transaction_templates').insert(insertData).select().single();\n        if (error) {\n            throw new Error(`Failed to create recurring template: ${error.message}`);\n        }\n        return data;\n    }\n    /**\n   * Disable recurring for a template\n   */ static async disableRecurring(templateId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transaction_templates').update({\n            is_recurring: false,\n            frequency: null,\n            next_due_date: null,\n            auto_create: false\n        }).eq('id', templateId).eq('user_id', user.id);\n        if (error) {\n            throw new Error(`Failed to disable recurring: ${error.message}`);\n        }\n    }\n    /**\n   * Process all due recurring transactions for auto-create templates\n   * This should be called on app startup/login\n   */ static async processAutoCreateRecurringTransactions() {\n        try {\n            const dueTransactions = await this.getDueRecurringTransactions();\n            const autoCreateTemplates = dueTransactions.filter(({ template })=>template.auto_create).map(({ template })=>template);\n            if (autoCreateTemplates.length === 0) {\n                return {\n                    created: 0,\n                    errors: []\n                };\n            }\n            const result = await this.createTransactionsFromTemplates(autoCreateTemplates);\n            return {\n                created: result.success.length,\n                errors: result.errors\n            };\n        } catch (error) {\n            console.error('Error processing auto-create recurring transactions:', error);\n            return {\n                created: 0,\n                errors: [\n                    {\n                        template: {},\n                        error: error instanceof Error ? error.message : 'Unknown error'\n                    }\n                ]\n            };\n        }\n    }\n    /**\n   * Get upcoming recurring transactions (next 30 days)\n   */ static async getUpcomingRecurringTransactions(days = 30) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const today = new Date();\n        const futureDate = new Date();\n        futureDate.setDate(today.getDate() + days);\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transaction_templates').select(`\n        *,\n        category:categories(*)\n      `).eq('user_id', user.id).eq('is_recurring', true).not('next_due_date', 'is', null).gte('next_due_date', today.toISOString().split('T')[0]).lte('next_due_date', futureDate.toISOString().split('T')[0]);\n        if (error) {\n            throw new Error(`Failed to fetch upcoming recurring transactions: ${error.message}`);\n        }\n        const templates = data || [];\n        return templates.map((template)=>{\n            const dueDate = new Date(template.next_due_date);\n            const daysUntilDue = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            return {\n                template: template,\n                dueDate,\n                daysUntilDue,\n                category: template.category\n            };\n        }).sort((a, b)=>a.daysUntilDue - b.daysUntilDue);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/lib/recurring-transactions.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/lib/supabase.mobile.ts":
/*!********************************************************!*\
  !*** ../../packages/shared/src/lib/supabase.mobile.ts ***!
  \********************************************************/
/***/ (() => {

eval("//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiIoc3NyKS8uLi8uLi9wYWNrYWdlcy9zaGFyZWQvc3JjL2xpYi9zdXBhYmFzZS5tb2JpbGUudHMiLCJzb3VyY2VSb290IjoiIiwiaWdub3JlTGlzdCI6W119\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/lib/supabase.mobile.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/lib/supabase.ts":
/*!*************************************************!*\
  !*** ../../packages/shared/src/lib/supabase.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://iconspmonvnujpnfmyqx.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imljb25zcG1vbnZudWpwbmZteXF4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyMzQwMDEsImV4cCI6MjA2NTgxMDAwMX0.xKtYUC7fBGZXlrxrI_DJxMvzBL1th7h60H2Qn-x6-So\" || 0;\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables');\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/schemas/auth.ts":
/*!*************************************************!*\
  !*** ../../packages/shared/src/schemas/auth.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resetPasswordSchema: () => (/* binding */ resetPasswordSchema),\n/* harmony export */   signInSchema: () => (/* binding */ signInSchema),\n/* harmony export */   signUpSchema: () => (/* binding */ signUpSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/zod/dist/esm/index.js\");\n\nconst signInSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email('Invalid email address'),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(6, 'Password must be at least 6 characters')\n});\nconst signUpSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(2, 'Name must be at least 2 characters'),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email('Invalid email address'),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(6, 'Password must be at least 6 characters'),\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n}).refine((data)=>data.password === data.confirmPassword, {\n    message: \"Passwords don't match\",\n    path: [\n        \"confirmPassword\"\n    ]\n});\nconst resetPasswordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email('Invalid email address')\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/schemas/auth.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/schemas/budget.ts":
/*!***************************************************!*\
  !*** ../../packages/shared/src/schemas/budget.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   budgetFormInputSchema: () => (/* binding */ budgetFormInputSchema),\n/* harmony export */   budgetFormSchema: () => (/* binding */ budgetFormSchema),\n/* harmony export */   budgetSchema: () => (/* binding */ budgetSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/zod/dist/esm/index.js\");\n\nconst budgetSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Budget name is required').max(100, 'Budget name must be under 100 characters'),\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Budget amount must be greater than 0'),\n    period: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'weekly',\n        'monthly',\n        'yearly'\n    ]),\n    category_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid('Please select a category').optional(),\n    start_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.date(),\n    end_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.date().optional()\n});\n// Base form schema without transform for form state\nconst budgetFormInputSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Budget name is required').max(100, 'Budget name must be under 100 characters'),\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Budget amount is required'),\n    period: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'weekly',\n        'monthly',\n        'yearly'\n    ]),\n    category_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    start_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.date(),\n    end_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.date(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z[\"null\"](),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.undefined()\n    ]).optional()\n});\n// Schema with transform for final validation\nconst budgetFormSchema = budgetFormInputSchema.transform((data)=>({\n        ...data,\n        amount: (()=>{\n            const num = parseFloat(data.amount.replace(/[^\\d.-]/g, ''));\n            if (isNaN(num) || num <= 0) {\n                throw new Error('Budget amount must be a positive number');\n            }\n            return num;\n        })(),\n        category_id: data.category_id || undefined,\n        end_date: data.end_date || undefined\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/schemas/budget.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/schemas/expense.ts":
/*!****************************************************!*\
  !*** ../../packages/shared/src/schemas/expense.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   expenseFormInputSchema: () => (/* binding */ expenseFormInputSchema),\n/* harmony export */   expenseFormSchema: () => (/* binding */ expenseFormSchema),\n/* harmony export */   expenseSchema: () => (/* binding */ expenseSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/zod/dist/esm/index.js\");\n\nconst expenseSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Amount must be greater than 0'),\n    category_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid('Please select a category'),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    transaction_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.date(),\n    transaction_type: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'income',\n        'expense'\n    ]),\n    attachments: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.any()).optional()\n});\n// Base form schema without transform for form state\nconst expenseFormInputSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Amount is required'),\n    category_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Please select a category'),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    transaction_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.date(),\n    transaction_type: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'income',\n        'expense'\n    ])\n});\n// Schema with transform for final validation\nconst expenseFormSchema = expenseFormInputSchema.transform((data)=>({\n        ...data,\n        amount: (()=>{\n            const num = parseFloat(data.amount.replace(/[^\\d.-]/g, ''));\n            if (isNaN(num) || num <= 0) {\n                throw new Error('Amount must be a positive number');\n            }\n            return num;\n        })()\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/schemas/expense.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/stores/currencyStore.ts":
/*!*********************************************************!*\
  !*** ../../packages/shared/src/stores/currencyStore.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCurrencyStore: () => (/* binding */ useCurrencyStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/../../node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/../../node_modules/zustand/esm/middleware.mjs\");\n\n\n// Valid currency codes supported by the app\nconst VALID_CURRENCIES = [\n    'USD',\n    'EUR',\n    'GBP',\n    'INR',\n    'JPY',\n    'CAD',\n    'AUD',\n    'CHF',\n    'SEK',\n    'NOK',\n    'DKK',\n    'SGD',\n    'HKD',\n    'CNY',\n    'KRW',\n    'BRL',\n    'MXN',\n    'ARS',\n    'ZAR',\n    'RUB',\n    'AED',\n    'SAR',\n    'TRY',\n    'ILS',\n    'EGP',\n    'NGN',\n    'KES',\n    'THB',\n    'MYR',\n    'IDR',\n    'PHP',\n    'VND',\n    'BDT',\n    'PKR',\n    'LKR',\n    'NZD'\n];\nconst useCurrencyStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        currency: 'USD',\n        setCurrency: (currency)=>{\n            // Validate currency code before setting\n            const validCurrency = VALID_CURRENCIES.includes(currency) ? currency : 'USD';\n            set({\n                currency: validCurrency\n            });\n        },\n        formatCurrency: (amount)=>{\n            const { currency } = get();\n            try {\n                // Ensure amount is a valid number\n                const validAmount = typeof amount === 'number' && !isNaN(amount) ? amount : 0;\n                return new Intl.NumberFormat('en-US', {\n                    style: 'currency',\n                    currency: currency,\n                    minimumFractionDigits: 2,\n                    maximumFractionDigits: 2\n                }).format(validAmount);\n            } catch (error) {\n                console.warn('Currency formatting error:', error);\n                // Fallback if currency is invalid\n                try {\n                    return new Intl.NumberFormat('en-US', {\n                        style: 'currency',\n                        currency: 'USD',\n                        minimumFractionDigits: 2,\n                        maximumFractionDigits: 2\n                    }).format(typeof amount === 'number' && !isNaN(amount) ? amount : 0);\n                } catch (fallbackError) {\n                    console.error('Fallback currency formatting failed:', fallbackError);\n                    return `$${(typeof amount === 'number' && !isNaN(amount) ? amount : 0).toFixed(2)}`;\n                }\n            }\n        }\n    }), {\n    name: 'currency-store'\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/stores/currencyStore.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/types.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/types.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n// Core types for the application\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/types.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/utils.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/utils.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateBudgetProgress: () => (/* binding */ calculateBudgetProgress),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate)\n/* harmony export */ });\n// Shared utility functions\nconst formatCurrency = (amount, currency = 'USD')=>{\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency\n    }).format(amount);\n};\nconst formatDate = (date)=>{\n    const d = typeof date === 'string' ? new Date(date) : date;\n    return d.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\nconst calculateBudgetProgress = (spent, budget)=>{\n    return Math.min(spent / budget * 100, 100);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvc2hhcmVkL3NyYy91dGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSwyQkFBMkI7QUFDcEIsTUFBTUEsaUJBQWlCLENBQzVCQyxRQUNBQyxXQUFtQixLQUFLO0lBRXhCLE9BQU8sSUFBSUMsS0FBS0MsWUFBWSxDQUFDLFNBQVM7UUFDcENDLE9BQU87UUFDUEg7SUFDRixHQUFHSSxNQUFNLENBQUNMO0FBQ1osRUFBRTtBQUVLLE1BQU1NLGFBQWEsQ0FBQ0M7SUFDekIsTUFBTUMsSUFBSSxPQUFPRCxTQUFTLFdBQVcsSUFBSUUsS0FBS0YsUUFBUUE7SUFDdEQsT0FBT0MsRUFBRUUsa0JBQWtCLENBQUMsU0FBUztRQUNuQ0MsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLEtBQUs7SUFDUDtBQUNGLEVBQUU7QUFFSyxNQUFNQywwQkFBMEIsQ0FDckNDLE9BQ0FDO0lBRUEsT0FBT0MsS0FBS0MsR0FBRyxDQUFDLFFBQVNGLFNBQVUsS0FBSztBQUMxQyxFQUFFIiwic291cmNlcyI6WyIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL3BhY2thZ2VzL3NoYXJlZC9zcmMvdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gU2hhcmVkIHV0aWxpdHkgZnVuY3Rpb25zXG5leHBvcnQgY29uc3QgZm9ybWF0Q3VycmVuY3kgPSAoXG4gIGFtb3VudDogbnVtYmVyLFxuICBjdXJyZW5jeTogc3RyaW5nID0gJ1VTRCdcbik6IHN0cmluZyA9PiB7XG4gIHJldHVybiBuZXcgSW50bC5OdW1iZXJGb3JtYXQoJ2VuLVVTJywge1xuICAgIHN0eWxlOiAnY3VycmVuY3knLFxuICAgIGN1cnJlbmN5LFxuICB9KS5mb3JtYXQoYW1vdW50KTtcbn07XG5cbmV4cG9ydCBjb25zdCBmb3JtYXREYXRlID0gKGRhdGU6IHN0cmluZyB8IERhdGUpOiBzdHJpbmcgPT4ge1xuICBjb25zdCBkID0gdHlwZW9mIGRhdGUgPT09ICdzdHJpbmcnID8gbmV3IERhdGUoZGF0ZSkgOiBkYXRlO1xuICByZXR1cm4gZC50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLVVTJywge1xuICAgIHllYXI6ICdudW1lcmljJyxcbiAgICBtb250aDogJ3Nob3J0JyxcbiAgICBkYXk6ICdudW1lcmljJyxcbiAgfSk7XG59O1xuXG5leHBvcnQgY29uc3QgY2FsY3VsYXRlQnVkZ2V0UHJvZ3Jlc3MgPSAoXG4gIHNwZW50OiBudW1iZXIsXG4gIGJ1ZGdldDogbnVtYmVyXG4pOiBudW1iZXIgPT4ge1xuICByZXR1cm4gTWF0aC5taW4oKHNwZW50IC8gYnVkZ2V0KSAqIDEwMCwgMTAwKTtcbn07Il0sIm5hbWVzIjpbImZvcm1hdEN1cnJlbmN5IiwiYW1vdW50IiwiY3VycmVuY3kiLCJJbnRsIiwiTnVtYmVyRm9ybWF0Iiwic3R5bGUiLCJmb3JtYXQiLCJmb3JtYXREYXRlIiwiZGF0ZSIsImQiLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwieWVhciIsIm1vbnRoIiwiZGF5IiwiY2FsY3VsYXRlQnVkZ2V0UHJvZ3Jlc3MiLCJzcGVudCIsImJ1ZGdldCIsIk1hdGgiLCJtaW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/utils.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/validators.ts":
/*!***********************************************!*\
  !*** ../../packages/shared/src/validators.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categorySchema: () => (/* binding */ categorySchema),\n/* harmony export */   transactionSchema: () => (/* binding */ transactionSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/zod/dist/esm/index.js\");\n\n// Validation schemas using Zod\nconst transactionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Amount must be positive'),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    category_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid('Invalid category ID'),\n    transaction_type: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'income',\n        'expense'\n    ]),\n    transaction_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date())\n});\nconst categorySchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Category name is required'),\n    icon: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Icon is required'),\n    color: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'income',\n        'expense'\n    ])\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/validators.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Fbudgets%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Fbudgets%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/budgets/page.tsx */ \"(ssr)/./src/app/budgets/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJhdmludGglMkZ3b3Jrc3BhY2VzJTJGc3RhcnR1cCUyRnBvcnRmb2xpb190cmFja2VyJTJGYXBwcyUyRndlYiUyRnNyYyUyRmFwcCUyRmJ1ZGdldHMlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0tBQTJIIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL2FwcHMvd2ViL3NyYy9hcHAvYnVkZ2V0cy9wYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Fbudgets%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJhdmludGglMkZ3b3Jrc3BhY2VzJTJGc3RhcnR1cCUyRnBvcnRmb2xpb190cmFja2VyJTJGYXBwcyUyRndlYiUyRnNyYyUyRmFwcCUyRmxheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFxSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9hcHBzL3dlYi9zcmMvYXBwL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/budgets/page.tsx":
/*!**********************************!*\
  !*** ./src/app/budgets/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BudgetsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_BudgetDashboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/BudgetDashboard */ \"(ssr)/./src/components/BudgetDashboard.tsx\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(ssr)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _components_Navbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../components/Navbar */ \"(ssr)/./src/components/Navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction BudgetsPage() {\n    const [showForm, setShowForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-background\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    currentPage: \"budgets\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-4xl font-bold text-text-primary\",\n                                                children: \"Budgets\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\",\n                                                lineNumber: 21,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-text-secondary text-lg mt-2\",\n                                                children: \"Set and track your spending limits by category\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\",\n                                                lineNumber: 22,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\",\n                                        lineNumber: 20,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowForm(true),\n                                            className: \"bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 flex items-center gap-2 font-semibold\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M12 4v16m8-8H4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\",\n                                                        lineNumber: 30,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\",\n                                                    lineNumber: 29,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Create Budget\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BudgetDashboard__WEBPACK_IMPORTED_MODULE_2__.BudgetDashboard, {\n                                showForm: showForm,\n                                setShowForm: setShowForm\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/budgets/page.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/budgets/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0c0f88d439f9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL2FwcHMvd2ViL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwYzBmODhkNDM5ZjlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(ssr)/./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/../../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default().variable)} font-sans antialiased bg-background text-text-primary`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: 'var(--surface-elevated)',\n                                    color: 'var(--text-primary)',\n                                    border: '1px solid var(--border)',\n                                    borderRadius: 'var(--radius-lg)',\n                                    boxShadow: 'var(--shadow-lg)'\n                                },\n                                success: {\n                                    iconTheme: {\n                                        primary: 'var(--success-green)',\n                                        secondary: 'white'\n                                    }\n                                },\n                                error: {\n                                    iconTheme: {\n                                        primary: 'var(--error-red)',\n                                        secondary: 'white'\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/AccountDropdown.tsx":
/*!********************************************!*\
  !*** ./src/components/AccountDropdown.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AccountDropdown)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction AccountDropdown() {\n    const { user, signOut } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const [isOpen, setIsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleSignOut = async ()=>{\n        try {\n            await signOut();\n            setIsOpen(false);\n        } catch (error) {\n            console.error('Error signing out:', error);\n        }\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AccountDropdown.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"AccountDropdown.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsOpen(false);\n                    }\n                }\n            }[\"AccountDropdown.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"AccountDropdown.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"AccountDropdown.useEffect\"];\n        }\n    }[\"AccountDropdown.useEffect\"], []);\n    // Get user initials for avatar\n    const getUserInitials = ()=>{\n        const name = user?.user_metadata?.name || user?.email || '';\n        return name.split(' ').map((word)=>word[0]).join('').toUpperCase().slice(0, 2);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative\",\n        ref: dropdownRef,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: ()=>setIsOpen(!isOpen),\n                className: \"flex items-center gap-3 px-3 py-2 rounded-lg text-text-primary hover:bg-surface transition-all duration-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold\",\n                        children: getUserInitials()\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-sm font-medium hidden sm:block truncate max-w-32\",\n                        children: user?.user_metadata?.name || user?.email\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: `w-4 h-4 text-text-secondary transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`,\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M19 9l-7 7-7-7\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute right-0 mt-2 w-64 bg-surface-elevated border border-border-light rounded-xl shadow-lg z-50 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-4 py-3 border-b border-border-light bg-surface\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold\",\n                                    children: getUserInitials()\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        user?.user_metadata?.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-semibold text-text-primary truncate\",\n                                            children: user.user_metadata.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-text-secondary truncate\",\n                                            children: user?.email\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                href: \"/profile\",\n                                onClick: ()=>setIsOpen(false),\n                                className: \"flex items-center gap-3 px-4 py-2 text-sm text-text-primary hover:bg-surface transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Profile Settings\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleSignOut,\n                                className: \"w-full flex items-center gap-3 px-4 py-2 text-sm text-error-red hover:bg-error-red/10 transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Sign Out\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/AccountDropdown.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/AccountDropdown.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/BudgetDashboard.tsx":
/*!********************************************!*\
  !*** ./src/components/BudgetDashboard.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BudgetDashboard: () => (/* binding */ BudgetDashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _BudgetForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BudgetForm */ \"(ssr)/./src/components/BudgetForm.tsx\");\n/* harmony import */ var _BudgetList__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./BudgetList */ \"(ssr)/./src/components/BudgetList.tsx\");\n\n\n\n\nconst BudgetDashboard = ({ showForm, setShowForm })=>{\n    const [editingBudget, setEditingBudget] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [refreshTrigger, setRefreshTrigger] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const handleCreateNew = ()=>{\n        setEditingBudget(null);\n        setShowForm(true);\n    };\n    const handleEdit = (budget)=>{\n        setEditingBudget(budget);\n        setShowForm(true);\n    };\n    const handleFormSuccess = ()=>{\n        setShowForm(false);\n        setEditingBudget(null);\n        setRefreshTrigger((prev)=>prev + 1);\n    };\n    const handleFormCancel = ()=>{\n        setShowForm(false);\n        setEditingBudget(null);\n    };\n    const handleDelete = ()=>{\n        setRefreshTrigger((prev)=>prev + 1);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            showForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BudgetForm__WEBPACK_IMPORTED_MODULE_2__.BudgetForm, {\n                    budget: editingBudget || undefined,\n                    onSuccess: handleFormSuccess,\n                    onCancel: handleFormCancel\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetDashboard.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetDashboard.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, undefined),\n            !showForm && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BudgetList__WEBPACK_IMPORTED_MODULE_3__.BudgetList, {\n                onEdit: handleEdit,\n                onDelete: handleDelete,\n                refreshTrigger: refreshTrigger\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetDashboard.tsx\",\n                lineNumber: 53,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetDashboard.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/BudgetDashboard.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/BudgetForm.tsx":
/*!***************************************!*\
  !*** ./src/components/BudgetForm.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BudgetForm: () => (/* binding */ BudgetForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/../../node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/../../node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @repo/shared */ \"(ssr)/../../packages/shared/src/index.ts\");\n\n\n\n\n\nconst BudgetForm = ({ budget, onSuccess, onCancel })=>{\n    const [categories, setCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { register, handleSubmit, formState: { errors }, reset, watch, setValue } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_4__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_2__.zodResolver)(_repo_shared__WEBPACK_IMPORTED_MODULE_3__.budgetFormInputSchema),\n        defaultValues: {\n            name: budget?.name || '',\n            amount: budget?.amount?.toString() || '',\n            period: budget?.period || 'monthly',\n            category_id: budget?.category_id || '',\n            start_date: budget?.start_date ? new Date(budget.start_date) : new Date(),\n            end_date: budget?.end_date ? new Date(budget.end_date) : null\n        }\n    });\n    const selectedPeriod = watch('period');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BudgetForm.useEffect\": ()=>{\n            loadCategories();\n        }\n    }[\"BudgetForm.useEffect\"], []);\n    const loadCategories = async ()=>{\n        try {\n            const fetchedCategories = await _repo_shared__WEBPACK_IMPORTED_MODULE_3__.ExpenseService.getCategories();\n            setCategories(fetchedCategories.filter((cat)=>cat.type === 'expense'));\n        } catch (err) {\n            console.error('Failed to load categories:', err);\n        }\n    };\n    const onSubmit = async (data)=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Transform the data\n            const transformedData = {\n                name: data.name,\n                amount: parseFloat(data.amount.replace(/[^\\d.-]/g, '')),\n                period: data.period,\n                category_id: data.category_id || undefined,\n                start_date: data.start_date,\n                end_date: data.end_date\n            };\n            let savedBudget;\n            if (budget) {\n                savedBudget = await _repo_shared__WEBPACK_IMPORTED_MODULE_3__.BudgetService.updateBudget(budget.id, transformedData);\n            } else {\n                savedBudget = await _repo_shared__WEBPACK_IMPORTED_MODULE_3__.BudgetService.createBudget(transformedData);\n            }\n            reset();\n            onSuccess?.(savedBudget);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to save budget');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCancel = ()=>{\n        reset();\n        onCancel?.();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-xl border border-gray-100 p-6 shadow-sm hover:shadow-md transition-all duration-200\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-xl font-semibold mb-6 text-gray-900\",\n                children: budget ? 'Edit Budget' : 'Create New Budget'\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, undefined),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 p-4 bg-red-50 border border-red-200 text-red-700 rounded-lg\",\n                children: error\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                onSubmit: handleSubmit(onSubmit),\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"name\",\n                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                children: \"Budget Name\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"name\",\n                                ...register('name'),\n                                className: \"w-full bg-gray-50 border-2 border-gray-200 rounded-lg px-4 py-3 text-base transition-all focus:border-blue-600 focus:outline-none focus:ring-4 focus:ring-blue-600/10 focus:bg-white\",\n                                placeholder: \"e.g., Monthly Groceries\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, undefined),\n                            errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.name.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"amount\",\n                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                children: \"Budget Amount\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"amount\",\n                                ...register('amount'),\n                                className: \"w-full bg-gray-50 border-2 border-gray-200 rounded-lg px-4 py-3 text-base transition-all focus:border-blue-600 focus:outline-none focus:ring-4 focus:ring-blue-600/10 focus:bg-white\",\n                                placeholder: \"0.00\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, undefined),\n                            errors.amount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.amount.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"period\",\n                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                children: \"Budget Period\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"period\",\n                                ...register('period'),\n                                className: \"w-full bg-gray-50 border-2 border-gray-200 rounded-lg px-4 py-3 appearance-none cursor-pointer transition-all focus:border-blue-600 focus:outline-none focus:ring-4 focus:ring-blue-600/10 focus:bg-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"weekly\",\n                                        children: \"Weekly\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"monthly\",\n                                        children: \"Monthly\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"yearly\",\n                                        children: \"Yearly\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, undefined),\n                            errors.period && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.period.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"category_id\",\n                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                children: \"Category (Optional)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                id: \"category_id\",\n                                ...register('category_id'),\n                                className: \"w-full bg-gray-50 border-2 border-gray-200 rounded-lg px-4 py-3 appearance-none cursor-pointer transition-all focus:border-blue-600 focus:outline-none focus:ring-4 focus:ring-blue-600/10 focus:bg-white\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: \"\",\n                                        children: \"All Categories\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: category.id,\n                                            children: [\n                                                category.icon,\n                                                \" \",\n                                                category.name\n                                            ]\n                                        }, category.id, true, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 15\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 11\n                            }, undefined),\n                            errors.category_id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.category_id.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"start_date\",\n                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                children: \"Start Date\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                id: \"start_date\",\n                                ...register('start_date', {\n                                    valueAsDate: true,\n                                    setValueAs: (value)=>value === '' ? new Date() : new Date(value)\n                                }),\n                                className: \"w-full bg-gray-50 border-2 border-gray-200 rounded-lg px-4 py-3 text-base transition-all focus:border-blue-600 focus:outline-none focus:ring-4 focus:ring-blue-600/10 focus:bg-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, undefined),\n                            errors.start_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.start_date.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"end_date\",\n                                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                                children: \"End Date (Optional)\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"date\",\n                                id: \"end_date\",\n                                ...register('end_date', {\n                                    setValueAs: (value)=>{\n                                        if (value === '' || !value) return undefined;\n                                        const date = new Date(value);\n                                        return isNaN(date.getTime()) ? undefined : date;\n                                    }\n                                }),\n                                className: \"w-full bg-gray-50 border-2 border-gray-200 rounded-lg px-4 py-3 text-base transition-all focus:border-blue-600 focus:outline-none focus:ring-4 focus:ring-blue-600/10 focus:bg-white\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-gray-500\",\n                                children: [\n                                    \"Leave empty for recurring \",\n                                    selectedPeriod,\n                                    \" budget\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 210,\n                                columnNumber: 11\n                            }, undefined),\n                            errors.end_date && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-1 text-sm text-red-600\",\n                                children: errors.end_date.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end space-x-3 pt-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleCancel,\n                                className: \"bg-gray-50 text-gray-700 border border-gray-200 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"submit\",\n                                disabled: loading,\n                                className: \"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold shadow-md hover:shadow-lg transition-all duration-200 hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\",\n                                children: loading ? 'Saving...' : budget ? 'Update Budget' : 'Create Budget'\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetForm.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/BudgetForm.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/BudgetList.tsx":
/*!***************************************!*\
  !*** ./src/components/BudgetList.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BudgetList: () => (/* binding */ BudgetList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(ssr)/../../packages/shared/src/index.ts\");\n\n\n\nconst BudgetList = ({ onEdit, onDelete, refreshTrigger })=>{\n    const [budgets, setBudgets] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [deletingId, setDeletingId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { formatCurrency } = (0,_repo_shared__WEBPACK_IMPORTED_MODULE_2__.useCurrencyStore)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"BudgetList.useEffect\": ()=>{\n            loadBudgets();\n        }\n    }[\"BudgetList.useEffect\"], [\n        refreshTrigger\n    ]);\n    const loadBudgets = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const budgetsWithProgress = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.BudgetService.getBudgetsWithProgress();\n            setBudgets(budgetsWithProgress);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to load budgets');\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleDelete = async (budgetId)=>{\n        if (!confirm('Are you sure you want to delete this budget?')) {\n            return;\n        }\n        try {\n            setDeletingId(budgetId);\n            await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.BudgetService.deleteBudget(budgetId);\n            setBudgets(budgets.filter((b)=>b.id !== budgetId));\n            onDelete?.(budgetId);\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'Failed to delete budget');\n        } finally{\n            setDeletingId(null);\n        }\n    };\n    const formatPeriod = (period)=>{\n        return period.charAt(0).toUpperCase() + period.slice(1);\n    };\n    const getProgressColor = (progress, isOverBudget)=>{\n        if (isOverBudget) return 'bg-red-500';\n        if (progress >= 80) return 'bg-yellow-500';\n        return 'bg-green-500';\n    };\n    const getProgressTextColor = (progress, isOverBudget)=>{\n        if (isOverBudget) return 'text-red-600';\n        if (progress >= 80) return 'text-yellow-600';\n        return 'text-green-600';\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex justify-center items-center py-12\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                lineNumber: 75,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-4 bg-red-50 border border-red-200 text-red-700 rounded-lg\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"font-medium\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: loadBudgets,\n                    className: \"mt-3 text-sm text-blue-600 font-medium hover:underline\",\n                    children: \"Try again\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n            lineNumber: 82,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (budgets.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-400 mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"mx-auto h-16 w-16\",\n                        fill: \"none\",\n                        viewBox: \"0 0 24 24\",\n                        stroke: \"currentColor\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 1.5,\n                            d: \"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-semibold text-gray-900 mb-2\",\n                    children: \"No budgets yet\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"Create your first budget to start tracking your spending.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n            lineNumber: 96,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: budgets.map((budget)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-xl border border-gray-100 p-6 shadow-sm hover:shadow-md transition-all duration-200\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-start mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-1\",\n                                        children: budget.name\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 text-sm text-gray-600\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: formatPeriod(budget.period)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            budget.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"flex items-center bg-gray-100 px-2 py-1 rounded-full\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-1\",\n                                                        children: budget.category.icon\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: budget.category.name\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>onEdit?.(budget),\n                                        className: \"text-blue-600 hover:text-blue-700 text-sm font-semibold px-4 py-2 border border-blue-200 hover:border-blue-300 bg-blue-50 hover:bg-blue-100 rounded-lg shadow-sm hover:shadow-md transition-all duration-200\",\n                                        children: \"Edit\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleDelete(budget.id),\n                                        disabled: deletingId === budget.id,\n                                        className: \"text-red-600 hover:text-red-700 text-sm font-semibold px-4 py-2 border border-red-200 hover:border-red-300 bg-red-50 hover:bg-red-100 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                        children: deletingId === budget.id ? 'Deleting...' : 'Delete'\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between text-sm text-gray-600 mb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Spent: \",\n                                            formatCurrency(budget.spent)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: [\n                                            \"Budget: \",\n                                            formatCurrency(budget.amount)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full h-2 bg-gray-100 rounded-full overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `h-full rounded-full transition-all duration-300 ${getProgressColor(budget.progress, budget.isOverBudget)}`,\n                                    style: {\n                                        width: `${Math.min(100, budget.progress)}%`\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `text-sm font-medium ${getProgressTextColor(budget.progress, budget.isOverBudget)}`,\n                                children: budget.isOverBudget ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        \"Over budget by \",\n                                        formatCurrency(budget.spent - budget.amount)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: [\n                                        formatCurrency(budget.remaining),\n                                        \" remaining\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                lineNumber: 159,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-600\",\n                                children: [\n                                    budget.progress.toFixed(1),\n                                    \"% used\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, undefined),\n                    budget.isOverBudget && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-red-50 border border-red-200 rounded-lg text-sm text-red-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold\",\n                                children: \"⚠️ Over Budget:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                lineNumber: 173,\n                                columnNumber: 15\n                            }, undefined),\n                            \" You've exceeded your budget for this period\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                        lineNumber: 172,\n                        columnNumber: 13\n                    }, undefined),\n                    !budget.isOverBudget && budget.progress >= 80 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg text-sm text-yellow-700\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-semibold\",\n                                children: \"⚠️ Warning:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 15\n                            }, undefined),\n                            \" You're approaching your budget limit\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, budget.id, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n                lineNumber: 111,\n                columnNumber: 9\n            }, undefined))\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/BudgetList.tsx\",\n        lineNumber: 109,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/BudgetList.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AccountDropdown__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AccountDropdown */ \"(ssr)/./src/components/AccountDropdown.tsx\");\n/* harmony import */ var _ThemeToggleButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ThemeToggleButton */ \"(ssr)/./src/components/ThemeToggleButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction Navbar({ currentPage }) {\n    const navItems = [\n        {\n            href: '/dashboard',\n            label: 'Dashboard',\n            key: 'dashboard',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M8 5a2 2 0 012-2h2a2 2 0 012 2v2H8V5z\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            href: '/expenses',\n            label: 'Expenses',\n            key: 'expenses',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                lineNumber: 29,\n                columnNumber: 9\n            }, this)\n        },\n        {\n            href: '/budgets',\n            label: 'Budgets',\n            key: 'budgets',\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"w-4 h-4\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 002 2z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-surface-elevated border-b border-border-light shadow-sm\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between h-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: \"/dashboard\",\n                            className: \"flex items-center gap-3 text-xl font-semibold text-text-primary hover:text-text-secondary transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-white\",\n                                        fill: \"currentColor\",\n                                        viewBox: \"0 0 20 20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            fillRule: \"evenodd\",\n                                            d: \"M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm2 6a2 2 0 114 0 2 2 0 01-4 0zm8-2a2 2 0 100 4 2 2 0 000-4z\",\n                                            clipRule: \"evenodd\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                            lineNumber: 55,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 15\n                                }, this),\n                                \"Portfolio Tracker\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:flex items-center space-x-1\",\n                        children: navItems.map((item)=>{\n                            const isActive = item.key === currentPage;\n                            if (isActive) {\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-2 rounded-lg text-white text-sm font-medium flex items-center gap-2\",\n                                    children: [\n                                        item.icon,\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: item.label\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, item.key, true, {\n                                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 19\n                                }, this);\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                href: item.href,\n                                className: \"flex items-center gap-2 px-4 py-2 rounded-lg text-text-secondary hover:text-text-primary hover:bg-surface transition-all\",\n                                children: [\n                                    item.icon,\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: item.label\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, item.key, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 17\n                            }, this);\n                        })\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ThemeToggleButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AccountDropdown__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/Navbar.tsx\",\n        lineNumber: 47,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ProtectedRoute.tsx":
/*!*******************************************!*\
  !*** ./src/components/ProtectedRoute.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProtectedRoute)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction ProtectedRoute({ children }) {\n    const { user, loading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProtectedRoute.useEffect\": ()=>{\n            if (!loading && !user) {\n                router.push('/auth/signin');\n            }\n        }\n    }[\"ProtectedRoute.useEffect\"], [\n        user,\n        loading,\n        router\n    ]);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-indigo-600\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ProtectedRoute.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ProtectedRoute.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this);\n    }\n    if (!user) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ProtectedRoute.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ThemeToggleButton.tsx":
/*!**********************************************!*\
  !*** ./src/components/ThemeToggleButton.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ThemeToggleButton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(ssr)/./src/contexts/ThemeContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ThemeToggleButton() {\n    const { theme, resolvedTheme, setTheme } = (0,_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    const handleToggle = ()=>{\n        if (resolvedTheme === 'light') {\n            setTheme('dark');\n        } else {\n            setTheme('light');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: handleToggle,\n        className: \"relative w-10 h-10 rounded-lg bg-surface hover:bg-surface-elevated border border-border-light hover:border-border transition-all duration-200 flex items-center justify-center group\",\n        \"aria-label\": `Switch to ${resolvedTheme === 'light' ? 'dark' : 'light'} mode`,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: `absolute w-5 h-5 text-warning-orange transition-all duration-300 ${resolvedTheme === 'light' ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 rotate-90 scale-75'}`,\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    fillRule: \"evenodd\",\n                    d: \"M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z\",\n                    clipRule: \"evenodd\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ThemeToggleButton.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ThemeToggleButton.tsx\",\n                lineNumber: 23,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: `absolute w-5 h-5 text-primary-purple transition-all duration-300 ${resolvedTheme === 'dark' ? 'opacity-100 rotate-0 scale-100' : 'opacity-0 -rotate-90 scale-75'}`,\n                fill: \"currentColor\",\n                viewBox: \"0 0 20 20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ThemeToggleButton.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ThemeToggleButton.tsx\",\n                lineNumber: 40,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute inset-0 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${resolvedTheme === 'light' ? 'bg-warning-orange/5' : 'bg-primary-purple/5'}`\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ThemeToggleButton.tsx\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/components/ThemeToggleButton.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ThemeToggleButton.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(ssr)/../../packages/shared/src/index.ts\");\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession().then({\n                \"AuthProvider.useEffect\": ({ data: { session } })=>{\n                    setSession(session);\n                    setUser(session?.user ? {\n                        id: session.user.id,\n                        email: session.user.email,\n                        user_metadata: session.user.user_metadata\n                    } : null);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Listen for auth changes\n            const { data: { subscription } } = _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    setSession(session);\n                    setUser(session?.user ? {\n                        id: session.user.id,\n                        email: session.user.email,\n                        user_metadata: session.user.user_metadata\n                    } : null);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const signIn = async (email, password)=>{\n        const { error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            error\n        };\n    };\n    const signUp = async (email, password, name)=>{\n        const { error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    name\n                }\n            }\n        });\n        return {\n            error\n        };\n    };\n    const signOut = async ()=>{\n        const { error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n        return {\n            error\n        };\n    };\n    const resetPassword = async (email)=>{\n        const { error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/auth/reset-password`\n        });\n        return {\n            error\n        };\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        resetPassword\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/contexts/AuthContext.tsx\",\n        lineNumber: 103,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    // Load theme from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const savedTheme = localStorage.getItem('theme');\n            if (savedTheme && [\n                'light',\n                'dark',\n                'system'\n            ].includes(savedTheme)) {\n                setThemeState(savedTheme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Update resolved theme based on theme preference and system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const updateResolvedTheme = {\n                \"ThemeProvider.useEffect.updateResolvedTheme\": ()=>{\n                    if (theme === 'system') {\n                        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n                        setResolvedTheme(systemPrefersDark ? 'dark' : 'light');\n                    } else {\n                        setResolvedTheme(theme);\n                    }\n                }\n            }[\"ThemeProvider.useEffect.updateResolvedTheme\"];\n            updateResolvedTheme();\n            // Listen for system theme changes\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": ()=>{\n                    if (theme === 'system') {\n                        updateResolvedTheme();\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener('change', handleChange)\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme\n    ]);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const root = document.documentElement;\n            if (resolvedTheme === 'dark') {\n                root.classList.add('dark');\n                root.setAttribute('data-theme', 'dark');\n            } else {\n                root.classList.remove('dark');\n                root.setAttribute('data-theme', 'light');\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        resolvedTheme\n    ]);\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        localStorage.setItem('theme', newTheme);\n    };\n    const toggleTheme = ()=>{\n        if (theme === 'light') {\n            setTheme('dark');\n        } else if (theme === 'dark') {\n            setTheme('system');\n        } else {\n            setTheme('light');\n        }\n    };\n    const value = {\n        theme,\n        resolvedTheme,\n        setTheme,\n        toggleTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/contexts/ThemeContext.tsx\",\n        lineNumber: 89,\n        columnNumber: 10\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvVGhlbWVDb250ZXh0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTZFO0FBVzdFLE1BQU1LLDZCQUFlSixvREFBYUEsQ0FBK0JLO0FBTTFELFNBQVNDLGNBQWMsRUFBRUMsUUFBUSxFQUFzQjtJQUM1RCxNQUFNLENBQUNDLE9BQU9DLGNBQWMsR0FBR04sK0NBQVFBLENBQVE7SUFDL0MsTUFBTSxDQUFDTyxlQUFlQyxpQkFBaUIsR0FBR1IsK0NBQVFBLENBQW1CO0lBRXJFLHdDQUF3QztJQUN4Q0QsZ0RBQVNBO21DQUFDO1lBQ1IsTUFBTVUsYUFBYUMsYUFBYUMsT0FBTyxDQUFDO1lBQ3hDLElBQUlGLGNBQWM7Z0JBQUM7Z0JBQVM7Z0JBQVE7YUFBUyxDQUFDRyxRQUFRLENBQUNILGFBQWE7Z0JBQ2xFSCxjQUFjRztZQUNoQjtRQUNGO2tDQUFHLEVBQUU7SUFFTCx3RUFBd0U7SUFDeEVWLGdEQUFTQTttQ0FBQztZQUNSLE1BQU1jOytEQUFzQjtvQkFDMUIsSUFBSVIsVUFBVSxVQUFVO3dCQUN0QixNQUFNUyxvQkFBb0JDLE9BQU9DLFVBQVUsQ0FBQyxnQ0FBZ0NDLE9BQU87d0JBQ25GVCxpQkFBaUJNLG9CQUFvQixTQUFTO29CQUNoRCxPQUFPO3dCQUNMTixpQkFBaUJIO29CQUNuQjtnQkFDRjs7WUFFQVE7WUFFQSxrQ0FBa0M7WUFDbEMsTUFBTUssYUFBYUgsT0FBT0MsVUFBVSxDQUFDO1lBQ3JDLE1BQU1HO3dEQUFlO29CQUNuQixJQUFJZCxVQUFVLFVBQVU7d0JBQ3RCUTtvQkFDRjtnQkFDRjs7WUFFQUssV0FBV0UsZ0JBQWdCLENBQUMsVUFBVUQ7WUFDdEM7MkNBQU8sSUFBTUQsV0FBV0csbUJBQW1CLENBQUMsVUFBVUY7O1FBQ3hEO2tDQUFHO1FBQUNkO0tBQU07SUFFViwwQkFBMEI7SUFDMUJOLGdEQUFTQTttQ0FBQztZQUNSLE1BQU11QixPQUFPQyxTQUFTQyxlQUFlO1lBQ3JDLElBQUlqQixrQkFBa0IsUUFBUTtnQkFDNUJlLEtBQUtHLFNBQVMsQ0FBQ0MsR0FBRyxDQUFDO2dCQUNuQkosS0FBS0ssWUFBWSxDQUFDLGNBQWM7WUFDbEMsT0FBTztnQkFDTEwsS0FBS0csU0FBUyxDQUFDRyxNQUFNLENBQUM7Z0JBQ3RCTixLQUFLSyxZQUFZLENBQUMsY0FBYztZQUNsQztRQUNGO2tDQUFHO1FBQUNwQjtLQUFjO0lBRWxCLE1BQU1zQixXQUFXLENBQUNDO1FBQ2hCeEIsY0FBY3dCO1FBQ2RwQixhQUFhcUIsT0FBTyxDQUFDLFNBQVNEO0lBQ2hDO0lBRUEsTUFBTUUsY0FBYztRQUNsQixJQUFJM0IsVUFBVSxTQUFTO1lBQ3JCd0IsU0FBUztRQUNYLE9BQU8sSUFBSXhCLFVBQVUsUUFBUTtZQUMzQndCLFNBQVM7UUFDWCxPQUFPO1lBQ0xBLFNBQVM7UUFDWDtJQUNGO0lBRUEsTUFBTUksUUFBMEI7UUFDOUI1QjtRQUNBRTtRQUNBc0I7UUFDQUc7SUFDRjtJQUVBLHFCQUFPLDhEQUFDL0IsYUFBYWlDLFFBQVE7UUFBQ0QsT0FBT0E7a0JBQVE3Qjs7Ozs7O0FBQy9DO0FBRU8sU0FBUytCO0lBQ2QsTUFBTUMsVUFBVXRDLGlEQUFVQSxDQUFDRztJQUMzQixJQUFJbUMsWUFBWWxDLFdBQVc7UUFDekIsTUFBTSxJQUFJbUMsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hcmF2aW50aC93b3Jrc3BhY2VzL3N0YXJ0dXAvcG9ydGZvbGlvX3RyYWNrZXIvYXBwcy93ZWIvc3JjL2NvbnRleHRzL1RoZW1lQ29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcblxuZXhwb3J0IHR5cGUgVGhlbWUgPSAnbGlnaHQnIHwgJ2RhcmsnIHwgJ3N5c3RlbSdcblxuaW50ZXJmYWNlIFRoZW1lQ29udGV4dFR5cGUge1xuICB0aGVtZTogVGhlbWVcbiAgcmVzb2x2ZWRUaGVtZTogJ2xpZ2h0JyB8ICdkYXJrJ1xuICBzZXRUaGVtZTogKHRoZW1lOiBUaGVtZSkgPT4gdm9pZFxuICB0b2dnbGVUaGVtZTogKCkgPT4gdm9pZFxufVxuXG5jb25zdCBUaGVtZUNvbnRleHQgPSBjcmVhdGVDb250ZXh0PFRoZW1lQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZClcblxuZXhwb3J0IGludGVyZmFjZSBUaGVtZVByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4gfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIGNvbnN0IFt0aGVtZSwgc2V0VGhlbWVTdGF0ZV0gPSB1c2VTdGF0ZTxUaGVtZT4oJ3N5c3RlbScpXG4gIGNvbnN0IFtyZXNvbHZlZFRoZW1lLCBzZXRSZXNvbHZlZFRoZW1lXSA9IHVzZVN0YXRlPCdsaWdodCcgfCAnZGFyayc+KCdsaWdodCcpXG5cbiAgLy8gTG9hZCB0aGVtZSBmcm9tIGxvY2FsU3RvcmFnZSBvbiBtb3VudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHNhdmVkVGhlbWUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndGhlbWUnKSBhcyBUaGVtZVxuICAgIGlmIChzYXZlZFRoZW1lICYmIFsnbGlnaHQnLCAnZGFyaycsICdzeXN0ZW0nXS5pbmNsdWRlcyhzYXZlZFRoZW1lKSkge1xuICAgICAgc2V0VGhlbWVTdGF0ZShzYXZlZFRoZW1lKVxuICAgIH1cbiAgfSwgW10pXG5cbiAgLy8gVXBkYXRlIHJlc29sdmVkIHRoZW1lIGJhc2VkIG9uIHRoZW1lIHByZWZlcmVuY2UgYW5kIHN5c3RlbSBwcmVmZXJlbmNlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlUmVzb2x2ZWRUaGVtZSA9ICgpID0+IHtcbiAgICAgIGlmICh0aGVtZSA9PT0gJ3N5c3RlbScpIHtcbiAgICAgICAgY29uc3Qgc3lzdGVtUHJlZmVyc0RhcmsgPSB3aW5kb3cubWF0Y2hNZWRpYSgnKHByZWZlcnMtY29sb3Itc2NoZW1lOiBkYXJrKScpLm1hdGNoZXNcbiAgICAgICAgc2V0UmVzb2x2ZWRUaGVtZShzeXN0ZW1QcmVmZXJzRGFyayA/ICdkYXJrJyA6ICdsaWdodCcpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRSZXNvbHZlZFRoZW1lKHRoZW1lKVxuICAgICAgfVxuICAgIH1cblxuICAgIHVwZGF0ZVJlc29sdmVkVGhlbWUoKVxuXG4gICAgLy8gTGlzdGVuIGZvciBzeXN0ZW0gdGhlbWUgY2hhbmdlc1xuICAgIGNvbnN0IG1lZGlhUXVlcnkgPSB3aW5kb3cubWF0Y2hNZWRpYSgnKHByZWZlcnMtY29sb3Itc2NoZW1lOiBkYXJrKScpXG4gICAgY29uc3QgaGFuZGxlQ2hhbmdlID0gKCkgPT4ge1xuICAgICAgaWYgKHRoZW1lID09PSAnc3lzdGVtJykge1xuICAgICAgICB1cGRhdGVSZXNvbHZlZFRoZW1lKClcbiAgICAgIH1cbiAgICB9XG5cbiAgICBtZWRpYVF1ZXJ5LmFkZEV2ZW50TGlzdGVuZXIoJ2NoYW5nZScsIGhhbmRsZUNoYW5nZSlcbiAgICByZXR1cm4gKCkgPT4gbWVkaWFRdWVyeS5yZW1vdmVFdmVudExpc3RlbmVyKCdjaGFuZ2UnLCBoYW5kbGVDaGFuZ2UpXG4gIH0sIFt0aGVtZV0pXG5cbiAgLy8gQXBwbHkgdGhlbWUgdG8gZG9jdW1lbnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCByb290ID0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50XG4gICAgaWYgKHJlc29sdmVkVGhlbWUgPT09ICdkYXJrJykge1xuICAgICAgcm9vdC5jbGFzc0xpc3QuYWRkKCdkYXJrJylcbiAgICAgIHJvb3Quc2V0QXR0cmlidXRlKCdkYXRhLXRoZW1lJywgJ2RhcmsnKVxuICAgIH0gZWxzZSB7XG4gICAgICByb290LmNsYXNzTGlzdC5yZW1vdmUoJ2RhcmsnKVxuICAgICAgcm9vdC5zZXRBdHRyaWJ1dGUoJ2RhdGEtdGhlbWUnLCAnbGlnaHQnKVxuICAgIH1cbiAgfSwgW3Jlc29sdmVkVGhlbWVdKVxuXG4gIGNvbnN0IHNldFRoZW1lID0gKG5ld1RoZW1lOiBUaGVtZSkgPT4ge1xuICAgIHNldFRoZW1lU3RhdGUobmV3VGhlbWUpXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3RoZW1lJywgbmV3VGhlbWUpXG4gIH1cblxuICBjb25zdCB0b2dnbGVUaGVtZSA9ICgpID0+IHtcbiAgICBpZiAodGhlbWUgPT09ICdsaWdodCcpIHtcbiAgICAgIHNldFRoZW1lKCdkYXJrJylcbiAgICB9IGVsc2UgaWYgKHRoZW1lID09PSAnZGFyaycpIHtcbiAgICAgIHNldFRoZW1lKCdzeXN0ZW0nKVxuICAgIH0gZWxzZSB7XG4gICAgICBzZXRUaGVtZSgnbGlnaHQnKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHZhbHVlOiBUaGVtZUNvbnRleHRUeXBlID0ge1xuICAgIHRoZW1lLFxuICAgIHJlc29sdmVkVGhlbWUsXG4gICAgc2V0VGhlbWUsXG4gICAgdG9nZ2xlVGhlbWUsXG4gIH1cblxuICByZXR1cm4gPFRoZW1lQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17dmFsdWV9PntjaGlsZHJlbn08L1RoZW1lQ29udGV4dC5Qcm92aWRlcj5cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZVRoZW1lKCkge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChUaGVtZUNvbnRleHQpXG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZVRoZW1lIG11c3QgYmUgdXNlZCB3aXRoaW4gYSBUaGVtZVByb3ZpZGVyJylcbiAgfVxuICByZXR1cm4gY29udGV4dFxufSJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJUaGVtZUNvbnRleHQiLCJ1bmRlZmluZWQiLCJUaGVtZVByb3ZpZGVyIiwiY2hpbGRyZW4iLCJ0aGVtZSIsInNldFRoZW1lU3RhdGUiLCJyZXNvbHZlZFRoZW1lIiwic2V0UmVzb2x2ZWRUaGVtZSIsInNhdmVkVGhlbWUiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiaW5jbHVkZXMiLCJ1cGRhdGVSZXNvbHZlZFRoZW1lIiwic3lzdGVtUHJlZmVyc0RhcmsiLCJ3aW5kb3ciLCJtYXRjaE1lZGlhIiwibWF0Y2hlcyIsIm1lZGlhUXVlcnkiLCJoYW5kbGVDaGFuZ2UiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInJvb3QiLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsImNsYXNzTGlzdCIsImFkZCIsInNldEF0dHJpYnV0ZSIsInJlbW92ZSIsInNldFRoZW1lIiwibmV3VGhlbWUiLCJzZXRJdGVtIiwidG9nZ2xlVGhlbWUiLCJ2YWx1ZSIsIlByb3ZpZGVyIiwidXNlVGhlbWUiLCJjb250ZXh0IiwiRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?3713":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?8e41":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/zustand","vendor-chunks/react-hot-toast","vendor-chunks/webidl-conversions","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/react-hook-form","vendor-chunks/@hookform"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fbudgets%2Fpage&page=%2Fbudgets%2Fpage&appPaths=%2Fbudgets%2Fpage&pagePath=private-next-app-dir%2Fbudgets%2Fpage.tsx&appDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();