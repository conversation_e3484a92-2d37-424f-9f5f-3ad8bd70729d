/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/auth/forgot-password/page";
exports.ids = ["app/auth/forgot-password/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fforgot-password%2Fpage&page=%2Fauth%2Fforgot-password%2Fpage&appPaths=%2Fauth%2Fforgot-password%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fforgot-password%2Fpage.tsx&appDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fforgot-password%2Fpage&page=%2Fauth%2Fforgot-password%2Fpage&appPaths=%2Fauth%2Fforgot-password%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fforgot-password%2Fpage.tsx&appDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/layout.tsx */ \"(rsc)/./src/app/auth/layout.tsx\"));\nconst page5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/forgot-password/page.tsx */ \"(rsc)/./src/app/auth/forgot-password/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'auth',\n        {\n        children: [\n        'forgot-password',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page5, \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module4, \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/layout.tsx\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/auth/forgot-password/page\",\n        pathname: \"/auth/forgot-password\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fforgot-password%2Fpage&page=%2Fauth%2Fforgot-password%2Fpage&appPaths=%2Fauth%2Fforgot-password%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fforgot-password%2Fpage.tsx&appDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Fauth%2Fforgot-password%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Fauth%2Fforgot-password%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/forgot-password/page.tsx */ \"(rsc)/./src/app/auth/forgot-password/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJhdmludGglMkZ3b3Jrc3BhY2VzJTJGc3RhcnR1cCUyRnBvcnRmb2xpb190cmFja2VyJTJGYXBwcyUyRndlYiUyRnNyYyUyRmFwcCUyRmF1dGglMkZmb3Jnb3QtcGFzc3dvcmQlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMExBQXdJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL2FwcHMvd2ViL3NyYy9hcHAvYXV0aC9mb3Jnb3QtcGFzc3dvcmQvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Fauth%2Fforgot-password%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJhdmludGglMkZ3b3Jrc3BhY2VzJTJGc3RhcnR1cCUyRnBvcnRmb2xpb190cmFja2VyJTJGYXBwcyUyRndlYiUyRnNyYyUyRmFwcCUyRmxheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFxSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9hcHBzL3dlYi9zcmMvYXBwL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hcmF2aW50aC93b3Jrc3BhY2VzL3N0YXJ0dXAvcG9ydGZvbGlvX3RyYWNrZXIvYXBwcy93ZWIvc3JjL2FwcC9mYXZpY29uLmljbz9fX25leHRfbWV0YWRhdGFfXyJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCBhc3luYyAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgYXdhaXQgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/auth/forgot-password/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/auth/forgot-password/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/auth/layout.tsx":
/*!*********************************!*\
  !*** ./src/app/auth/layout.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction AuthLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-background flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-br from-primary-blue/5 via-primary-purple/5 to-surface opacity-50\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/layout.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0\",\n                style: {\n                    backgroundImage: `radial-gradient(circle at 1px 1px, var(--border-light) 1px, transparent 0)`,\n                    backgroundSize: '20px 20px'\n                }\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/layout.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 sm:mx-auto sm:w-full sm:max-w-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-surface-elevated rounded-2xl border border-border-light p-8 shadow-xl backdrop-blur-sm\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/layout.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/layout.tsx\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/layout.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2F1dGgvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQWUsU0FBU0EsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUViLDhEQUFDRDtnQkFBSUMsV0FBVTs7Ozs7OzBCQUNmLDhEQUFDRDtnQkFBSUMsV0FBVTtnQkFBbUJDLE9BQU87b0JBQ3ZDQyxpQkFBaUIsQ0FBQywwRUFBMEUsQ0FBQztvQkFDN0ZDLGdCQUFnQjtnQkFDbEI7Ozs7OzswQkFFQSw4REFBQ0o7Z0JBQUlDLFdBQVU7MEJBQ2IsNEVBQUNEO29CQUFJQyxXQUFVOzhCQUNaRjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLWCIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9hcHBzL3dlYi9zcmMvYXBwL2F1dGgvbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBdXRoTGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGVcbn0pIHtcbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBiZy1iYWNrZ3JvdW5kIGZsZXggZmxleC1jb2wganVzdGlmeS1jZW50ZXIgcHktMTIgcHgtNCBzbTpweC02IGxnOnB4LThcIj5cbiAgICAgIHsvKiBCYWNrZ3JvdW5kIFBhdHRlcm4gKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYnIgZnJvbS1wcmltYXJ5LWJsdWUvNSB2aWEtcHJpbWFyeS1wdXJwbGUvNSB0by1zdXJmYWNlIG9wYWNpdHktNTBcIj48L2Rpdj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMFwiIHN0eWxlPXt7XG4gICAgICAgIGJhY2tncm91bmRJbWFnZTogYHJhZGlhbC1ncmFkaWVudChjaXJjbGUgYXQgMXB4IDFweCwgdmFyKC0tYm9yZGVyLWxpZ2h0KSAxcHgsIHRyYW5zcGFyZW50IDApYCxcbiAgICAgICAgYmFja2dyb3VuZFNpemU6ICcyMHB4IDIwcHgnXG4gICAgICB9fT48L2Rpdj5cbiAgICAgIFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSB6LTEwIHNtOm14LWF1dG8gc206dy1mdWxsIHNtOm1heC13LW1kXCI+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmctc3VyZmFjZS1lbGV2YXRlZCByb3VuZGVkLTJ4bCBib3JkZXIgYm9yZGVyLWJvcmRlci1saWdodCBwLTggc2hhZG93LXhsIGJhY2tkcm9wLWJsdXItc21cIj5cbiAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn0iXSwibmFtZXMiOlsiQXV0aExheW91dCIsImNoaWxkcmVuIiwiZGl2IiwiY2xhc3NOYW1lIiwic3R5bGUiLCJiYWNrZ3JvdW5kSW1hZ2UiLCJiYWNrZ3JvdW5kU2l6ZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/auth/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!****************************************************************!*\
  !*** ../../node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \****************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/../../node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/../../packages/shared/src/database.types.ts":
/*!***************************************************!*\
  !*** ../../packages/shared/src/database.types.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Constants: () => (/* binding */ Constants)\n/* harmony export */ });\nconst Constants = {\n    public: {\n        Enums: {}\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/database.types.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/index.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/index.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsService: () => (/* reexport safe */ _lib_analytics__WEBPACK_IMPORTED_MODULE_7__.AnalyticsService),\n/* harmony export */   BiometricService: () => (/* reexport safe */ _lib_biometric_web__WEBPACK_IMPORTED_MODULE_9__.BiometricService),\n/* harmony export */   BudgetService: () => (/* reexport safe */ _lib_budget__WEBPACK_IMPORTED_MODULE_6__.BudgetService),\n/* harmony export */   Constants: () => (/* reexport safe */ _database_types__WEBPACK_IMPORTED_MODULE_3__.Constants),\n/* harmony export */   ExpenseService: () => (/* reexport safe */ _lib_expenses__WEBPACK_IMPORTED_MODULE_5__.ExpenseService),\n/* harmony export */   RecurringTransactionService: () => (/* reexport safe */ _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_8__.RecurringTransactionService),\n/* harmony export */   budgetFormInputSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_12__.budgetFormInputSchema),\n/* harmony export */   budgetFormSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_12__.budgetFormSchema),\n/* harmony export */   budgetSchema: () => (/* reexport safe */ _schemas_budget__WEBPACK_IMPORTED_MODULE_12__.budgetSchema),\n/* harmony export */   calculateBudgetProgress: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.calculateBudgetProgress),\n/* harmony export */   categorySchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.categorySchema),\n/* harmony export */   expenseFormInputSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_11__.expenseFormInputSchema),\n/* harmony export */   expenseFormSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_11__.expenseFormSchema),\n/* harmony export */   expenseSchema: () => (/* reexport safe */ _schemas_expense__WEBPACK_IMPORTED_MODULE_11__.expenseSchema),\n/* harmony export */   formatCurrency: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatCurrency),\n/* harmony export */   formatDate: () => (/* reexport safe */ _utils__WEBPACK_IMPORTED_MODULE_1__.formatDate),\n/* harmony export */   resetPasswordSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_10__.resetPasswordSchema),\n/* harmony export */   signInSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_10__.signInSchema),\n/* harmony export */   signUpSchema: () => (/* reexport safe */ _schemas_auth__WEBPACK_IMPORTED_MODULE_10__.signUpSchema),\n/* harmony export */   supabase: () => (/* reexport safe */ _lib_supabase__WEBPACK_IMPORTED_MODULE_4__.supabase),\n/* harmony export */   supabaseMobile: () => (/* reexport safe */ _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_14__.supabase),\n/* harmony export */   transactionSchema: () => (/* reexport safe */ _validators__WEBPACK_IMPORTED_MODULE_2__.transactionSchema),\n/* harmony export */   useCurrencyStore: () => (/* reexport safe */ _stores_currencyStore__WEBPACK_IMPORTED_MODULE_13__.useCurrencyStore)\n/* harmony export */ });\n/* harmony import */ var _types__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./types */ \"(ssr)/../../packages/shared/src/types.ts\");\n/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils */ \"(ssr)/../../packages/shared/src/utils.ts\");\n/* harmony import */ var _validators__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./validators */ \"(ssr)/../../packages/shared/src/validators.ts\");\n/* harmony import */ var _database_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./database.types */ \"(ssr)/../../packages/shared/src/database.types.ts\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/supabase */ \"(ssr)/../../packages/shared/src/lib/supabase.ts\");\n/* harmony import */ var _lib_expenses__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./lib/expenses */ \"(ssr)/../../packages/shared/src/lib/expenses.ts\");\n/* harmony import */ var _lib_budget__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./lib/budget */ \"(ssr)/../../packages/shared/src/lib/budget.ts\");\n/* harmony import */ var _lib_analytics__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/analytics */ \"(ssr)/../../packages/shared/src/lib/analytics.ts\");\n/* harmony import */ var _lib_recurring_transactions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./lib/recurring-transactions */ \"(ssr)/../../packages/shared/src/lib/recurring-transactions.ts\");\n/* harmony import */ var _lib_biometric_web__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./lib/biometric.web */ \"(ssr)/../../packages/shared/src/lib/biometric.web.ts\");\n/* harmony import */ var _schemas_auth__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./schemas/auth */ \"(ssr)/../../packages/shared/src/schemas/auth.ts\");\n/* harmony import */ var _schemas_expense__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./schemas/expense */ \"(ssr)/../../packages/shared/src/schemas/expense.ts\");\n/* harmony import */ var _schemas_budget__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./schemas/budget */ \"(ssr)/../../packages/shared/src/schemas/budget.ts\");\n/* harmony import */ var _stores_currencyStore__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./stores/currencyStore */ \"(ssr)/../../packages/shared/src/stores/currencyStore.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./lib/supabase.mobile */ \"(ssr)/../../packages/shared/src/lib/supabase.mobile.ts\");\n/* harmony import */ var _lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_lib_supabase_mobile__WEBPACK_IMPORTED_MODULE_14__);\n// Shared business logic and utilities\n\n\n\n\n\n\n\n\n\n// Platform-specific biometric exports\n\n\n\n\n\n// Platform-specific exports (explicit re-export to avoid naming conflicts)\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvc2hhcmVkL3NyYy9pbmRleC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxzQ0FBc0M7QUFDZDtBQUNBO0FBQ0s7QUFDSTtBQUNGO0FBQ0E7QUFDRjtBQUNHO0FBQ2E7QUFDN0Msc0NBQXNDO0FBQ0Y7QUFDTDtBQUNHO0FBQ0Q7QUFDTTtBQUV2QywyRUFBMkU7QUFDUiIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9wYWNrYWdlcy9zaGFyZWQvc3JjL2luZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFNoYXJlZCBidXNpbmVzcyBsb2dpYyBhbmQgdXRpbGl0aWVzXG5leHBvcnQgKiBmcm9tICcuL3R5cGVzJztcbmV4cG9ydCAqIGZyb20gJy4vdXRpbHMnO1xuZXhwb3J0ICogZnJvbSAnLi92YWxpZGF0b3JzJztcbmV4cG9ydCAqIGZyb20gJy4vZGF0YWJhc2UudHlwZXMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvc3VwYWJhc2UnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvZXhwZW5zZXMnO1xuZXhwb3J0ICogZnJvbSAnLi9saWIvYnVkZ2V0JztcbmV4cG9ydCAqIGZyb20gJy4vbGliL2FuYWx5dGljcyc7XG5leHBvcnQgKiBmcm9tICcuL2xpYi9yZWN1cnJpbmctdHJhbnNhY3Rpb25zJztcbi8vIFBsYXRmb3JtLXNwZWNpZmljIGJpb21ldHJpYyBleHBvcnRzXG5leHBvcnQgKiBmcm9tICcuL2xpYi9iaW9tZXRyaWMud2ViJztcbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hcy9hdXRoJztcbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hcy9leHBlbnNlJztcbmV4cG9ydCAqIGZyb20gJy4vc2NoZW1hcy9idWRnZXQnO1xuZXhwb3J0ICogZnJvbSAnLi9zdG9yZXMvY3VycmVuY3lTdG9yZSc7XG5cbi8vIFBsYXRmb3JtLXNwZWNpZmljIGV4cG9ydHMgKGV4cGxpY2l0IHJlLWV4cG9ydCB0byBhdm9pZCBuYW1pbmcgY29uZmxpY3RzKVxuZXhwb3J0IHsgc3VwYWJhc2UgYXMgc3VwYWJhc2VNb2JpbGUgfSBmcm9tICcuL2xpYi9zdXBhYmFzZS5tb2JpbGUnOyJdLCJuYW1lcyI6WyJzdXBhYmFzZSIsInN1cGFiYXNlTW9iaWxlIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/index.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/lib/analytics.ts":
/*!**************************************************!*\
  !*** ../../packages/shared/src/lib/analytics.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AnalyticsService: () => (/* binding */ AnalyticsService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/../../packages/shared/src/lib/supabase.ts\");\n\nclass AnalyticsService {\n    static async getAnalyticsData(dateRange) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Default to last 6 months if no range provided\n        const endDate = dateRange?.endDate || new Date().toISOString().split('T')[0];\n        const startDate = dateRange?.startDate || new Date(Date.now() - 6 * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(`\n        *,\n        category:categories(*)\n      `).eq('user_id', user.id).gte('transaction_date', startDate).lte('transaction_date', endDate).order('transaction_date', {\n            ascending: true\n        });\n        if (error) {\n            throw new Error(`Failed to fetch analytics data: ${error.message}`);\n        }\n        const transactions = data;\n        return this.processAnalyticsData(transactions);\n    }\n    static processAnalyticsData(transactions) {\n        // Calculate totals\n        const totalIncome = transactions.filter((t)=>t.transaction_type === 'income').reduce((sum, t)=>sum + t.amount, 0);\n        const totalExpenses = transactions.filter((t)=>t.transaction_type === 'expense').reduce((sum, t)=>sum + t.amount, 0);\n        const netIncome = totalIncome - totalExpenses;\n        // Category breakdown\n        const categoryMap = {};\n        transactions.forEach((transaction)=>{\n            if (transaction.category) {\n                const categoryId = transaction.category.id;\n                if (!categoryMap[categoryId]) {\n                    categoryMap[categoryId] = {\n                        category: transaction.category,\n                        total: 0,\n                        count: 0\n                    };\n                }\n                categoryMap[categoryId].total += transaction.amount;\n                categoryMap[categoryId].count += 1;\n            }\n        });\n        const categoryBreakdown = Object.values(categoryMap).map((item)=>({\n                category: item.category,\n                total: item.total,\n                percentage: totalExpenses > 0 ? item.total / totalExpenses * 100 : 0\n            })).sort((a, b)=>b.total - a.total);\n        // Monthly trends\n        const monthlyMap = {};\n        transactions.forEach((transaction)=>{\n            const monthKey = transaction.transaction_date.substring(0, 7) // YYYY-MM\n            ;\n            if (!monthlyMap[monthKey]) {\n                monthlyMap[monthKey] = {\n                    income: 0,\n                    expenses: 0\n                };\n            }\n            if (transaction.transaction_type === 'income') {\n                monthlyMap[monthKey].income += transaction.amount;\n            } else {\n                monthlyMap[monthKey].expenses += transaction.amount;\n            }\n        });\n        const monthlyTrends = Object.entries(monthlyMap).map(([month, data])=>({\n                month,\n                income: data.income,\n                expenses: data.expenses,\n                net: data.income - data.expenses\n            })).sort((a, b)=>a.month.localeCompare(b.month));\n        // Top categories\n        const topCategories = Object.values(categoryMap).map((item)=>({\n                category: item.category,\n                total: item.total,\n                transactionCount: item.count\n            })).sort((a, b)=>b.total - a.total).slice(0, 5);\n        return {\n            totalIncome,\n            totalExpenses,\n            netIncome,\n            categoryBreakdown,\n            monthlyTrends,\n            topCategories\n        };\n    }\n    static async getSpendingTrends(months = 12) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const endDate = new Date();\n        const startDate = new Date();\n        startDate.setMonth(startDate.getMonth() - months);\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select('amount, transaction_type, transaction_date').eq('user_id', user.id).gte('transaction_date', startDate.toISOString().split('T')[0]).lte('transaction_date', endDate.toISOString().split('T')[0]).order('transaction_date', {\n            ascending: true\n        });\n        if (error) {\n            throw new Error(`Failed to fetch spending trends: ${error.message}`);\n        }\n        const transactions = data;\n        const monthlyData = {};\n        transactions.forEach((transaction)=>{\n            const monthKey = transaction.transaction_date.substring(0, 7);\n            if (!monthlyData[monthKey]) {\n                monthlyData[monthKey] = {\n                    income: 0,\n                    expenses: 0\n                };\n            }\n            if (transaction.transaction_type === 'income') {\n                monthlyData[monthKey].income += transaction.amount;\n            } else {\n                monthlyData[monthKey].expenses += transaction.amount;\n            }\n        });\n        return Object.entries(monthlyData).map(([month, data])=>({\n                month,\n                expenses: data.expenses,\n                income: data.income\n            })).sort((a, b)=>a.month.localeCompare(b.month));\n    }\n    static formatMonth(monthString) {\n        const date = new Date(monthString + '-01');\n        return date.toLocaleDateString('en-US', {\n            month: 'short',\n            year: 'numeric'\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/lib/analytics.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/lib/biometric.web.ts":
/*!******************************************************!*\
  !*** ../../packages/shared/src/lib/biometric.web.ts ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BiometricService: () => (/* binding */ BiometricService)\n/* harmony export */ });\nclass BiometricService {\n    /**\n   * Check if biometric authentication is available on the device\n   * Web implementation - always returns unavailable\n   */ static async getCapabilities() {\n        return {\n            isAvailable: false,\n            biometricType: [],\n            hasHardware: false,\n            isEnrolled: false\n        };\n    }\n    /**\n   * Authenticate user with biometrics\n   * Web implementation - always returns unavailable\n   */ static async authenticate(reason = 'Please authenticate to continue') {\n        return {\n            success: false,\n            error: 'Biometric authentication is not available on web'\n        };\n    }\n    /**\n   * Check if biometric login is enabled by the user\n   * Web implementation - always returns false\n   */ static async isBiometricEnabled() {\n        return false;\n    }\n    /**\n   * Enable or disable biometric login\n   * Web implementation - no-op\n   */ static async setBiometricEnabled(enabled) {\n    // No-op for web\n    }\n    /**\n   * Store encrypted credentials for biometric login\n   * Web implementation - no-op\n   */ static async storeCredentials(email, hashedPassword) {\n    // No-op for web\n    }\n    /**\n   * Retrieve stored credentials after successful biometric authentication\n   * Web implementation - always returns null\n   */ static async getStoredCredentials() {\n        return null;\n    }\n    /**\n   * Clear all biometric data\n   * Web implementation - no-op\n   */ static async clearBiometricData() {\n    // No-op for web\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/lib/biometric.web.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/lib/budget.ts":
/*!***********************************************!*\
  !*** ../../packages/shared/src/lib/budget.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BudgetService: () => (/* binding */ BudgetService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/../../packages/shared/src/lib/supabase.ts\");\n\nclass BudgetService {\n    static async createBudget(data) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const budgetData = {\n            name: data.name,\n            amount: data.amount,\n            period: data.period,\n            category_id: data.category_id || null,\n            start_date: data.start_date.toISOString().split('T')[0],\n            end_date: data.end_date ? data.end_date.toISOString().split('T')[0] : null,\n            user_id: user.id\n        };\n        const { data: budget, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('budgets').insert(budgetData).select(`\n        *,\n        category:categories(*)\n      `).single();\n        if (error) {\n            throw new Error(`Failed to create budget: ${error.message}`);\n        }\n        return budget;\n    }\n    static async getBudgets() {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('budgets').select(`\n        *,\n        category:categories(*)\n      `).eq('user_id', user.id).order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            throw new Error(`Failed to fetch budgets: ${error.message}`);\n        }\n        return data;\n    }\n    static async getBudgetWithProgress(budgetId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Fetch budget\n        const { data: budget, error: budgetError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('budgets').select(`\n        *,\n        category:categories(*)\n      `).eq('id', budgetId).eq('user_id', user.id).single();\n        if (budgetError) {\n            throw new Error(`Failed to fetch budget: ${budgetError.message}`);\n        }\n        // Calculate progress\n        const progress = await this.calculateBudgetProgress(budget);\n        return progress;\n    }\n    static async getBudgetsWithProgress() {\n        const budgets = await this.getBudgets();\n        const budgetsWithProgress = await Promise.all(budgets.map((budget)=>this.calculateBudgetProgress(budget)));\n        return budgetsWithProgress;\n    }\n    static async updateBudget(id, data) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const updateData = {};\n        if (data.name !== undefined) updateData.name = data.name;\n        if (data.amount !== undefined) updateData.amount = data.amount;\n        if (data.period !== undefined) updateData.period = data.period;\n        if (data.category_id !== undefined) updateData.category_id = data.category_id || null;\n        if (data.start_date !== undefined) {\n            updateData.start_date = data.start_date.toISOString().split('T')[0];\n        }\n        if (data.end_date !== undefined) {\n            updateData.end_date = data.end_date ? data.end_date.toISOString().split('T')[0] : null;\n        }\n        updateData.updated_at = new Date().toISOString();\n        const { data: budget, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('budgets').update(updateData).eq('id', id).eq('user_id', user.id).select(`\n        *,\n        category:categories(*)\n      `).single();\n        if (error) {\n            throw new Error(`Failed to update budget: ${error.message}`);\n        }\n        return budget;\n    }\n    static async deleteBudget(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('budgets').delete().eq('id', id).eq('user_id', user.id);\n        if (error) {\n            throw new Error(`Failed to delete budget: ${error.message}`);\n        }\n    }\n    static async calculateBudgetProgress(budget) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Calculate date range for budget period\n        const { startDate, endDate } = this.getBudgetDateRange(budget);\n        // Build query for transactions\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(`\n        *,\n        category:categories(*)\n      `).eq('user_id', user.id).eq('transaction_type', 'expense').gte('transaction_date', startDate).lte('transaction_date', endDate);\n        // Filter by category if budget is category-specific\n        if (budget.category_id) {\n            query = query.eq('category_id', budget.category_id);\n        }\n        const { data: transactions, error } = await query;\n        if (error) {\n            throw new Error(`Failed to fetch transactions for budget: ${error.message}`);\n        }\n        // Calculate spending\n        const spent = transactions?.reduce((sum, transaction)=>sum + transaction.amount, 0) || 0;\n        const remaining = Math.max(0, budget.amount - spent);\n        const progress = Math.min(100, spent / budget.amount * 100);\n        const isOverBudget = spent > budget.amount;\n        return {\n            ...budget,\n            spent,\n            remaining,\n            progress,\n            isOverBudget,\n            transactions: transactions\n        };\n    }\n    static getBudgetDateRange(budget) {\n        const budgetStartDate = new Date(budget.start_date);\n        const now = new Date();\n        // If budget has specific end date, use it\n        if (budget.end_date) {\n            return {\n                startDate: budget.start_date,\n                endDate: budget.end_date\n            };\n        }\n        // Calculate end date based on period\n        let endDate;\n        switch(budget.period){\n            case 'weekly':\n                endDate = new Date(budgetStartDate);\n                endDate.setDate(endDate.getDate() + 7);\n                break;\n            case 'monthly':\n                endDate = new Date(budgetStartDate);\n                endDate.setMonth(endDate.getMonth() + 1);\n                break;\n            case 'yearly':\n                endDate = new Date(budgetStartDate);\n                endDate.setFullYear(endDate.getFullYear() + 1);\n                break;\n            default:\n                endDate = new Date(budgetStartDate);\n                endDate.setMonth(endDate.getMonth() + 1);\n        }\n        // For recurring budgets, find the current period\n        while(endDate < now){\n            switch(budget.period){\n                case 'weekly':\n                    budgetStartDate.setDate(budgetStartDate.getDate() + 7);\n                    endDate.setDate(endDate.getDate() + 7);\n                    break;\n                case 'monthly':\n                    budgetStartDate.setMonth(budgetStartDate.getMonth() + 1);\n                    endDate.setMonth(endDate.getMonth() + 1);\n                    break;\n                case 'yearly':\n                    budgetStartDate.setFullYear(budgetStartDate.getFullYear() + 1);\n                    endDate.setFullYear(endDate.getFullYear() + 1);\n                    break;\n            }\n        }\n        return {\n            startDate: budgetStartDate.toISOString().split('T')[0],\n            endDate: endDate.toISOString().split('T')[0]\n        };\n    }\n    static async checkBudgetAlerts() {\n        const budgetsWithProgress = await this.getBudgetsWithProgress();\n        // Return budgets that are at 80% or over their limit\n        return budgetsWithProgress.filter((budget)=>budget.progress >= 80);\n    }\n    static async createRecurringBudgets() {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Get all budgets that don't have an end_date (recurring)\n        const { data: recurringBudgets, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('budgets').select('*').eq('user_id', user.id).is('end_date', null);\n        if (error) {\n            throw new Error(`Failed to fetch recurring budgets: ${error.message}`);\n        }\n        const now = new Date();\n        const newBudgets = [];\n        for (const budget of recurringBudgets || []){\n            const { endDate } = this.getBudgetDateRange(budget);\n            // If current period has ended, create new budget for next period\n            if (new Date(endDate) < now) {\n                const newStartDate = new Date(endDate);\n                newStartDate.setDate(newStartDate.getDate() + 1);\n                const newBudgetData = {\n                    name: budget.name,\n                    amount: budget.amount,\n                    period: budget.period,\n                    category_id: budget.category_id,\n                    start_date: newStartDate.toISOString().split('T')[0],\n                    end_date: null,\n                    user_id: user.id\n                };\n                const { data: newBudget, error: createError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('budgets').insert(newBudgetData).select(`\n            *,\n            category:categories(*)\n          `).single();\n                if (createError) {\n                    console.error(`Failed to create recurring budget: ${createError.message}`);\n                    continue;\n                }\n                newBudgets.push(newBudget);\n            }\n        }\n        return newBudgets;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvc2hhcmVkL3NyYy9saWIvYnVkZ2V0LnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFDO0FBWTlCLE1BQU1DO0lBQ1gsYUFBYUMsYUFBYUMsSUFBZ0IsRUFBb0I7UUFDNUQsTUFBTSxFQUFFQSxNQUFNLEVBQUVDLElBQUksRUFBRSxFQUFFLEdBQUcsTUFBTUosK0NBQVFBLENBQUNLLElBQUksQ0FBQ0MsT0FBTztRQUV0RCxJQUFJLENBQUNGLE1BQU07WUFDVCxNQUFNLElBQUlHLE1BQU07UUFDbEI7UUFFQSxNQUFNQyxhQUFhO1lBQ2pCQyxNQUFNTixLQUFLTSxJQUFJO1lBQ2ZDLFFBQVFQLEtBQUtPLE1BQU07WUFDbkJDLFFBQVFSLEtBQUtRLE1BQU07WUFDbkJDLGFBQWFULEtBQUtTLFdBQVcsSUFBSTtZQUNqQ0MsWUFBWVYsS0FBS1UsVUFBVSxDQUFDQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUN2REMsVUFBVWIsS0FBS2EsUUFBUSxHQUFHYixLQUFLYSxRQUFRLENBQUNGLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEdBQUc7WUFDdEVFLFNBQVNiLEtBQUtjLEVBQUU7UUFDbEI7UUFFQSxNQUFNLEVBQUVmLE1BQU1nQixNQUFNLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1wQiwrQ0FBUUEsQ0FDM0NxQixJQUFJLENBQUMsV0FDTEMsTUFBTSxDQUFDZCxZQUNQZSxNQUFNLENBQUMsQ0FBQzs7O01BR1QsQ0FBQyxFQUNBQyxNQUFNO1FBRVQsSUFBSUosT0FBTztZQUNULE1BQU0sSUFBSWIsTUFBTSxDQUFDLHlCQUF5QixFQUFFYSxNQUFNSyxPQUFPLEVBQUU7UUFDN0Q7UUFFQSxPQUFPTjtJQUNUO0lBRUEsYUFBYU8sYUFBaUM7UUFDNUMsTUFBTSxFQUFFdkIsTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRSxHQUFHLE1BQU1KLCtDQUFRQSxDQUFDSyxJQUFJLENBQUNDLE9BQU87UUFFdEQsSUFBSSxDQUFDRixNQUFNO1lBQ1QsTUFBTSxJQUFJRyxNQUFNO1FBQ2xCO1FBRUEsTUFBTSxFQUFFSixJQUFJLEVBQUVpQixLQUFLLEVBQUUsR0FBRyxNQUFNcEIsK0NBQVFBLENBQ25DcUIsSUFBSSxDQUFDLFdBQ0xFLE1BQU0sQ0FBQyxDQUFDOzs7TUFHVCxDQUFDLEVBQ0FJLEVBQUUsQ0FBQyxXQUFXdkIsS0FBS2MsRUFBRSxFQUNyQlUsS0FBSyxDQUFDLGNBQWM7WUFBRUMsV0FBVztRQUFNO1FBRTFDLElBQUlULE9BQU87WUFDVCxNQUFNLElBQUliLE1BQU0sQ0FBQyx5QkFBeUIsRUFBRWEsTUFBTUssT0FBTyxFQUFFO1FBQzdEO1FBRUEsT0FBT3RCO0lBQ1Q7SUFFQSxhQUFhMkIsc0JBQXNCQyxRQUFnQixFQUFnQztRQUNqRixNQUFNLEVBQUU1QixNQUFNLEVBQUVDLElBQUksRUFBRSxFQUFFLEdBQUcsTUFBTUosK0NBQVFBLENBQUNLLElBQUksQ0FBQ0MsT0FBTztRQUV0RCxJQUFJLENBQUNGLE1BQU07WUFDVCxNQUFNLElBQUlHLE1BQU07UUFDbEI7UUFFQSxlQUFlO1FBQ2YsTUFBTSxFQUFFSixNQUFNZ0IsTUFBTSxFQUFFQyxPQUFPWSxXQUFXLEVBQUUsR0FBRyxNQUFNaEMsK0NBQVFBLENBQ3hEcUIsSUFBSSxDQUFDLFdBQ0xFLE1BQU0sQ0FBQyxDQUFDOzs7TUFHVCxDQUFDLEVBQ0FJLEVBQUUsQ0FBQyxNQUFNSSxVQUNUSixFQUFFLENBQUMsV0FBV3ZCLEtBQUtjLEVBQUUsRUFDckJNLE1BQU07UUFFVCxJQUFJUSxhQUFhO1lBQ2YsTUFBTSxJQUFJekIsTUFBTSxDQUFDLHdCQUF3QixFQUFFeUIsWUFBWVAsT0FBTyxFQUFFO1FBQ2xFO1FBRUEscUJBQXFCO1FBQ3JCLE1BQU1RLFdBQVcsTUFBTSxJQUFJLENBQUNDLHVCQUF1QixDQUFDZjtRQUVwRCxPQUFPYztJQUNUO0lBRUEsYUFBYUUseUJBQXlEO1FBQ3BFLE1BQU1DLFVBQVUsTUFBTSxJQUFJLENBQUNWLFVBQVU7UUFFckMsTUFBTVcsc0JBQXNCLE1BQU1DLFFBQVFDLEdBQUcsQ0FDM0NILFFBQVFJLEdBQUcsQ0FBQyxDQUFDckIsU0FBVyxJQUFJLENBQUNlLHVCQUF1QixDQUFDZjtRQUd2RCxPQUFPa0I7SUFDVDtJQUVBLGFBQWFJLGFBQWF2QixFQUFVLEVBQUVmLElBQXlCLEVBQW9CO1FBQ2pGLE1BQU0sRUFBRUEsTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRSxHQUFHLE1BQU1KLCtDQUFRQSxDQUFDSyxJQUFJLENBQUNDLE9BQU87UUFFdEQsSUFBSSxDQUFDRixNQUFNO1lBQ1QsTUFBTSxJQUFJRyxNQUFNO1FBQ2xCO1FBRUEsTUFBTW1DLGFBQWtCLENBQUM7UUFFekIsSUFBSXZDLEtBQUtNLElBQUksS0FBS2tDLFdBQVdELFdBQVdqQyxJQUFJLEdBQUdOLEtBQUtNLElBQUk7UUFDeEQsSUFBSU4sS0FBS08sTUFBTSxLQUFLaUMsV0FBV0QsV0FBV2hDLE1BQU0sR0FBR1AsS0FBS08sTUFBTTtRQUM5RCxJQUFJUCxLQUFLUSxNQUFNLEtBQUtnQyxXQUFXRCxXQUFXL0IsTUFBTSxHQUFHUixLQUFLUSxNQUFNO1FBQzlELElBQUlSLEtBQUtTLFdBQVcsS0FBSytCLFdBQVdELFdBQVc5QixXQUFXLEdBQUdULEtBQUtTLFdBQVcsSUFBSTtRQUNqRixJQUFJVCxLQUFLVSxVQUFVLEtBQUs4QixXQUFXO1lBQ2pDRCxXQUFXN0IsVUFBVSxHQUFHVixLQUFLVSxVQUFVLENBQUNDLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFO1FBQ3JFO1FBQ0EsSUFBSVosS0FBS2EsUUFBUSxLQUFLMkIsV0FBVztZQUMvQkQsV0FBVzFCLFFBQVEsR0FBR2IsS0FBS2EsUUFBUSxHQUFHYixLQUFLYSxRQUFRLENBQUNGLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEdBQUc7UUFDcEY7UUFFQTJCLFdBQVdFLFVBQVUsR0FBRyxJQUFJQyxPQUFPL0IsV0FBVztRQUU5QyxNQUFNLEVBQUVYLE1BQU1nQixNQUFNLEVBQUVDLEtBQUssRUFBRSxHQUFHLE1BQU1wQiwrQ0FBUUEsQ0FDM0NxQixJQUFJLENBQUMsV0FDTHlCLE1BQU0sQ0FBQ0osWUFDUGYsRUFBRSxDQUFDLE1BQU1ULElBQ1RTLEVBQUUsQ0FBQyxXQUFXdkIsS0FBS2MsRUFBRSxFQUNyQkssTUFBTSxDQUFDLENBQUM7OztNQUdULENBQUMsRUFDQUMsTUFBTTtRQUVULElBQUlKLE9BQU87WUFDVCxNQUFNLElBQUliLE1BQU0sQ0FBQyx5QkFBeUIsRUFBRWEsTUFBTUssT0FBTyxFQUFFO1FBQzdEO1FBRUEsT0FBT047SUFDVDtJQUVBLGFBQWE0QixhQUFhN0IsRUFBVSxFQUFpQjtRQUNuRCxNQUFNLEVBQUVmLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEVBQUUsR0FBRyxNQUFNSiwrQ0FBUUEsQ0FBQ0ssSUFBSSxDQUFDQyxPQUFPO1FBRXRELElBQUksQ0FBQ0YsTUFBTTtZQUNULE1BQU0sSUFBSUcsTUFBTTtRQUNsQjtRQUVBLE1BQU0sRUFBRWEsS0FBSyxFQUFFLEdBQUcsTUFBTXBCLCtDQUFRQSxDQUM3QnFCLElBQUksQ0FBQyxXQUNMMkIsTUFBTSxHQUNOckIsRUFBRSxDQUFDLE1BQU1ULElBQ1RTLEVBQUUsQ0FBQyxXQUFXdkIsS0FBS2MsRUFBRTtRQUV4QixJQUFJRSxPQUFPO1lBQ1QsTUFBTSxJQUFJYixNQUFNLENBQUMseUJBQXlCLEVBQUVhLE1BQU1LLE9BQU8sRUFBRTtRQUM3RDtJQUNGO0lBRUEsYUFBcUJTLHdCQUF3QmYsTUFBZSxFQUFnQztRQUMxRixNQUFNLEVBQUVoQixNQUFNLEVBQUVDLElBQUksRUFBRSxFQUFFLEdBQUcsTUFBTUosK0NBQVFBLENBQUNLLElBQUksQ0FBQ0MsT0FBTztRQUV0RCxJQUFJLENBQUNGLE1BQU07WUFDVCxNQUFNLElBQUlHLE1BQU07UUFDbEI7UUFFQSx5Q0FBeUM7UUFDekMsTUFBTSxFQUFFMEMsU0FBUyxFQUFFQyxPQUFPLEVBQUUsR0FBRyxJQUFJLENBQUNDLGtCQUFrQixDQUFDaEM7UUFFdkQsK0JBQStCO1FBQy9CLElBQUlpQyxRQUFRcEQsK0NBQVFBLENBQ2pCcUIsSUFBSSxDQUFDLGdCQUNMRSxNQUFNLENBQUMsQ0FBQzs7O01BR1QsQ0FBQyxFQUNBSSxFQUFFLENBQUMsV0FBV3ZCLEtBQUtjLEVBQUUsRUFDckJTLEVBQUUsQ0FBQyxvQkFBb0IsV0FDdkIwQixHQUFHLENBQUMsb0JBQW9CSixXQUN4QkssR0FBRyxDQUFDLG9CQUFvQko7UUFFM0Isb0RBQW9EO1FBQ3BELElBQUkvQixPQUFPUCxXQUFXLEVBQUU7WUFDdEJ3QyxRQUFRQSxNQUFNekIsRUFBRSxDQUFDLGVBQWVSLE9BQU9QLFdBQVc7UUFDcEQ7UUFFQSxNQUFNLEVBQUVULE1BQU1vRCxZQUFZLEVBQUVuQyxLQUFLLEVBQUUsR0FBRyxNQUFNZ0M7UUFFNUMsSUFBSWhDLE9BQU87WUFDVCxNQUFNLElBQUliLE1BQU0sQ0FBQyx5Q0FBeUMsRUFBRWEsTUFBTUssT0FBTyxFQUFFO1FBQzdFO1FBRUEscUJBQXFCO1FBQ3JCLE1BQU0rQixRQUFRRCxjQUFjRSxPQUFPLENBQUNDLEtBQUtDLGNBQWdCRCxNQUFNQyxZQUFZakQsTUFBTSxFQUFFLE1BQU07UUFDekYsTUFBTWtELFlBQVlDLEtBQUtDLEdBQUcsQ0FBQyxHQUFHM0MsT0FBT1QsTUFBTSxHQUFHOEM7UUFDOUMsTUFBTXZCLFdBQVc0QixLQUFLRSxHQUFHLENBQUMsS0FBSyxRQUFTNUMsT0FBT1QsTUFBTSxHQUFJO1FBQ3pELE1BQU1zRCxlQUFlUixRQUFRckMsT0FBT1QsTUFBTTtRQUUxQyxPQUFPO1lBQ0wsR0FBR1MsTUFBTTtZQUNUcUM7WUFDQUk7WUFDQTNCO1lBQ0ErQjtZQUNBVCxjQUFjQTtRQUNoQjtJQUNGO0lBRUEsT0FBZUosbUJBQW1CaEMsTUFBZSxFQUEwQztRQUN6RixNQUFNOEMsa0JBQWtCLElBQUlwQixLQUFLMUIsT0FBT04sVUFBVTtRQUNsRCxNQUFNcUQsTUFBTSxJQUFJckI7UUFFaEIsMENBQTBDO1FBQzFDLElBQUkxQixPQUFPSCxRQUFRLEVBQUU7WUFDbkIsT0FBTztnQkFDTGlDLFdBQVc5QixPQUFPTixVQUFVO2dCQUM1QnFDLFNBQVMvQixPQUFPSCxRQUFRO1lBQzFCO1FBQ0Y7UUFFQSxxQ0FBcUM7UUFDckMsSUFBSWtDO1FBRUosT0FBUS9CLE9BQU9SLE1BQU07WUFDbkIsS0FBSztnQkFDSHVDLFVBQVUsSUFBSUwsS0FBS29CO2dCQUNuQmYsUUFBUWlCLE9BQU8sQ0FBQ2pCLFFBQVFrQixPQUFPLEtBQUs7Z0JBQ3BDO1lBQ0YsS0FBSztnQkFDSGxCLFVBQVUsSUFBSUwsS0FBS29CO2dCQUNuQmYsUUFBUW1CLFFBQVEsQ0FBQ25CLFFBQVFvQixRQUFRLEtBQUs7Z0JBQ3RDO1lBQ0YsS0FBSztnQkFDSHBCLFVBQVUsSUFBSUwsS0FBS29CO2dCQUNuQmYsUUFBUXFCLFdBQVcsQ0FBQ3JCLFFBQVFzQixXQUFXLEtBQUs7Z0JBQzVDO1lBQ0Y7Z0JBQ0V0QixVQUFVLElBQUlMLEtBQUtvQjtnQkFDbkJmLFFBQVFtQixRQUFRLENBQUNuQixRQUFRb0IsUUFBUSxLQUFLO1FBQzFDO1FBRUEsaURBQWlEO1FBQ2pELE1BQU9wQixVQUFVZ0IsSUFBSztZQUNwQixPQUFRL0MsT0FBT1IsTUFBTTtnQkFDbkIsS0FBSztvQkFDSHNELGdCQUFnQkUsT0FBTyxDQUFDRixnQkFBZ0JHLE9BQU8sS0FBSztvQkFDcERsQixRQUFRaUIsT0FBTyxDQUFDakIsUUFBUWtCLE9BQU8sS0FBSztvQkFDcEM7Z0JBQ0YsS0FBSztvQkFDSEgsZ0JBQWdCSSxRQUFRLENBQUNKLGdCQUFnQkssUUFBUSxLQUFLO29CQUN0RHBCLFFBQVFtQixRQUFRLENBQUNuQixRQUFRb0IsUUFBUSxLQUFLO29CQUN0QztnQkFDRixLQUFLO29CQUNITCxnQkFBZ0JNLFdBQVcsQ0FBQ04sZ0JBQWdCTyxXQUFXLEtBQUs7b0JBQzVEdEIsUUFBUXFCLFdBQVcsQ0FBQ3JCLFFBQVFzQixXQUFXLEtBQUs7b0JBQzVDO1lBQ0o7UUFDRjtRQUVBLE9BQU87WUFDTHZCLFdBQVdnQixnQkFBZ0JuRCxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtZQUN0RG1DLFNBQVNBLFFBQVFwQyxXQUFXLEdBQUdDLEtBQUssQ0FBQyxJQUFJLENBQUMsRUFBRTtRQUM5QztJQUNGO0lBRUEsYUFBYTBELG9CQUFvRDtRQUMvRCxNQUFNcEMsc0JBQXNCLE1BQU0sSUFBSSxDQUFDRixzQkFBc0I7UUFFN0QscURBQXFEO1FBQ3JELE9BQU9FLG9CQUFvQnFDLE1BQU0sQ0FBQ3ZELENBQUFBLFNBQVVBLE9BQU9jLFFBQVEsSUFBSTtJQUNqRTtJQUVBLGFBQWEwQyx5QkFBNkM7UUFDeEQsTUFBTSxFQUFFeEUsTUFBTSxFQUFFQyxJQUFJLEVBQUUsRUFBRSxHQUFHLE1BQU1KLCtDQUFRQSxDQUFDSyxJQUFJLENBQUNDLE9BQU87UUFFdEQsSUFBSSxDQUFDRixNQUFNO1lBQ1QsTUFBTSxJQUFJRyxNQUFNO1FBQ2xCO1FBRUEsMERBQTBEO1FBQzFELE1BQU0sRUFBRUosTUFBTXlFLGdCQUFnQixFQUFFeEQsS0FBSyxFQUFFLEdBQUcsTUFBTXBCLCtDQUFRQSxDQUNyRHFCLElBQUksQ0FBQyxXQUNMRSxNQUFNLENBQUMsS0FDUEksRUFBRSxDQUFDLFdBQVd2QixLQUFLYyxFQUFFLEVBQ3JCMkQsRUFBRSxDQUFDLFlBQVk7UUFFbEIsSUFBSXpELE9BQU87WUFDVCxNQUFNLElBQUliLE1BQU0sQ0FBQyxtQ0FBbUMsRUFBRWEsTUFBTUssT0FBTyxFQUFFO1FBQ3ZFO1FBRUEsTUFBTXlDLE1BQU0sSUFBSXJCO1FBQ2hCLE1BQU1pQyxhQUF3QixFQUFFO1FBRWhDLEtBQUssTUFBTTNELFVBQVV5RCxvQkFBb0IsRUFBRSxDQUFFO1lBQzNDLE1BQU0sRUFBRTFCLE9BQU8sRUFBRSxHQUFHLElBQUksQ0FBQ0Msa0JBQWtCLENBQUNoQztZQUU1QyxpRUFBaUU7WUFDakUsSUFBSSxJQUFJMEIsS0FBS0ssV0FBV2dCLEtBQUs7Z0JBQzNCLE1BQU1hLGVBQWUsSUFBSWxDLEtBQUtLO2dCQUM5QjZCLGFBQWFaLE9BQU8sQ0FBQ1ksYUFBYVgsT0FBTyxLQUFLO2dCQUU5QyxNQUFNWSxnQkFBZ0I7b0JBQ3BCdkUsTUFBTVUsT0FBT1YsSUFBSTtvQkFDakJDLFFBQVFTLE9BQU9ULE1BQU07b0JBQ3JCQyxRQUFRUSxPQUFPUixNQUFNO29CQUNyQkMsYUFBYU8sT0FBT1AsV0FBVztvQkFDL0JDLFlBQVlrRSxhQUFhakUsV0FBVyxHQUFHQyxLQUFLLENBQUMsSUFBSSxDQUFDLEVBQUU7b0JBQ3BEQyxVQUFVO29CQUNWQyxTQUFTYixLQUFLYyxFQUFFO2dCQUNsQjtnQkFFQSxNQUFNLEVBQUVmLE1BQU04RSxTQUFTLEVBQUU3RCxPQUFPOEQsV0FBVyxFQUFFLEdBQUcsTUFBTWxGLCtDQUFRQSxDQUMzRHFCLElBQUksQ0FBQyxXQUNMQyxNQUFNLENBQUMwRCxlQUNQekQsTUFBTSxDQUFDLENBQUM7OztVQUdULENBQUMsRUFDQUMsTUFBTTtnQkFFVCxJQUFJMEQsYUFBYTtvQkFDZkMsUUFBUS9ELEtBQUssQ0FBQyxDQUFDLG1DQUFtQyxFQUFFOEQsWUFBWXpELE9BQU8sRUFBRTtvQkFDekU7Z0JBQ0Y7Z0JBRUFxRCxXQUFXTSxJQUFJLENBQUNIO1lBQ2xCO1FBQ0Y7UUFFQSxPQUFPSDtJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hcmF2aW50aC93b3Jrc3BhY2VzL3N0YXJ0dXAvcG9ydGZvbGlvX3RyYWNrZXIvcGFja2FnZXMvc2hhcmVkL3NyYy9saWIvYnVkZ2V0LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHN1cGFiYXNlIH0gZnJvbSAnLi9zdXBhYmFzZSdcbmltcG9ydCB0eXBlIHsgSUJ1ZGdldCwgSUNhdGVnb3J5LCBJVHJhbnNhY3Rpb24gfSBmcm9tICcuLi90eXBlcydcbmltcG9ydCB0eXBlIHsgQnVkZ2V0RGF0YSB9IGZyb20gJy4uL3NjaGVtYXMvYnVkZ2V0J1xuXG5leHBvcnQgaW50ZXJmYWNlIElCdWRnZXRXaXRoUHJvZ3Jlc3MgZXh0ZW5kcyBJQnVkZ2V0IHtcbiAgc3BlbnQ6IG51bWJlclxuICByZW1haW5pbmc6IG51bWJlclxuICBwcm9ncmVzczogbnVtYmVyXG4gIGlzT3ZlckJ1ZGdldDogYm9vbGVhblxuICB0cmFuc2FjdGlvbnM/OiBJVHJhbnNhY3Rpb25bXVxufVxuXG5leHBvcnQgY2xhc3MgQnVkZ2V0U2VydmljZSB7XG4gIHN0YXRpYyBhc3luYyBjcmVhdGVCdWRnZXQoZGF0YTogQnVkZ2V0RGF0YSk6IFByb21pc2U8SUJ1ZGdldD4ge1xuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKVxuICAgIH1cblxuICAgIGNvbnN0IGJ1ZGdldERhdGEgPSB7XG4gICAgICBuYW1lOiBkYXRhLm5hbWUsXG4gICAgICBhbW91bnQ6IGRhdGEuYW1vdW50LFxuICAgICAgcGVyaW9kOiBkYXRhLnBlcmlvZCxcbiAgICAgIGNhdGVnb3J5X2lkOiBkYXRhLmNhdGVnb3J5X2lkIHx8IG51bGwsXG4gICAgICBzdGFydF9kYXRlOiBkYXRhLnN0YXJ0X2RhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdLFxuICAgICAgZW5kX2RhdGU6IGRhdGEuZW5kX2RhdGUgPyBkYXRhLmVuZF9kYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSA6IG51bGwsXG4gICAgICB1c2VyX2lkOiB1c2VyLmlkLFxuICAgIH1cblxuICAgIGNvbnN0IHsgZGF0YTogYnVkZ2V0LCBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdidWRnZXRzJylcbiAgICAgIC5pbnNlcnQoYnVkZ2V0RGF0YSlcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBjYXRlZ29yeTpjYXRlZ29yaWVzKCopXG4gICAgICBgKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGNyZWF0ZSBidWRnZXQ6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIHJldHVybiBidWRnZXQgYXMgSUJ1ZGdldFxuICB9XG5cbiAgc3RhdGljIGFzeW5jIGdldEJ1ZGdldHMoKTogUHJvbWlzZTxJQnVkZ2V0W10+IHtcbiAgICBjb25zdCB7IGRhdGE6IHsgdXNlciB9IH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKVxuICAgIFxuICAgIGlmICghdXNlcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdVc2VyIG5vdCBhdXRoZW50aWNhdGVkJylcbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2J1ZGdldHMnKVxuICAgICAgLnNlbGVjdChgXG4gICAgICAgICosXG4gICAgICAgIGNhdGVnb3J5OmNhdGVnb3JpZXMoKilcbiAgICAgIGApXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgICAgLm9yZGVyKCdjcmVhdGVkX2F0JywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIGJ1ZGdldHM6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIHJldHVybiBkYXRhIGFzIElCdWRnZXRbXVxuICB9XG5cbiAgc3RhdGljIGFzeW5jIGdldEJ1ZGdldFdpdGhQcm9ncmVzcyhidWRnZXRJZDogc3RyaW5nKTogUHJvbWlzZTxJQnVkZ2V0V2l0aFByb2dyZXNzPiB7XG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKClcbiAgICBcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignVXNlciBub3QgYXV0aGVudGljYXRlZCcpXG4gICAgfVxuXG4gICAgLy8gRmV0Y2ggYnVkZ2V0XG4gICAgY29uc3QgeyBkYXRhOiBidWRnZXQsIGVycm9yOiBidWRnZXRFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdidWRnZXRzJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBjYXRlZ29yeTpjYXRlZ29yaWVzKCopXG4gICAgICBgKVxuICAgICAgLmVxKCdpZCcsIGJ1ZGdldElkKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5zaW5nbGUoKVxuXG4gICAgaWYgKGJ1ZGdldEVycm9yKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBmZXRjaCBidWRnZXQ6ICR7YnVkZ2V0RXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIC8vIENhbGN1bGF0ZSBwcm9ncmVzc1xuICAgIGNvbnN0IHByb2dyZXNzID0gYXdhaXQgdGhpcy5jYWxjdWxhdGVCdWRnZXRQcm9ncmVzcyhidWRnZXQgYXMgSUJ1ZGdldClcbiAgICBcbiAgICByZXR1cm4gcHJvZ3Jlc3NcbiAgfVxuXG4gIHN0YXRpYyBhc3luYyBnZXRCdWRnZXRzV2l0aFByb2dyZXNzKCk6IFByb21pc2U8SUJ1ZGdldFdpdGhQcm9ncmVzc1tdPiB7XG4gICAgY29uc3QgYnVkZ2V0cyA9IGF3YWl0IHRoaXMuZ2V0QnVkZ2V0cygpXG4gICAgXG4gICAgY29uc3QgYnVkZ2V0c1dpdGhQcm9ncmVzcyA9IGF3YWl0IFByb21pc2UuYWxsKFxuICAgICAgYnVkZ2V0cy5tYXAoKGJ1ZGdldCkgPT4gdGhpcy5jYWxjdWxhdGVCdWRnZXRQcm9ncmVzcyhidWRnZXQpKVxuICAgIClcblxuICAgIHJldHVybiBidWRnZXRzV2l0aFByb2dyZXNzXG4gIH1cblxuICBzdGF0aWMgYXN5bmMgdXBkYXRlQnVkZ2V0KGlkOiBzdHJpbmcsIGRhdGE6IFBhcnRpYWw8QnVkZ2V0RGF0YT4pOiBQcm9taXNlPElCdWRnZXQ+IHtcbiAgICBjb25zdCB7IGRhdGE6IHsgdXNlciB9IH0gPSBhd2FpdCBzdXBhYmFzZS5hdXRoLmdldFVzZXIoKVxuICAgIFxuICAgIGlmICghdXNlcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdVc2VyIG5vdCBhdXRoZW50aWNhdGVkJylcbiAgICB9XG5cbiAgICBjb25zdCB1cGRhdGVEYXRhOiBhbnkgPSB7fVxuXG4gICAgaWYgKGRhdGEubmFtZSAhPT0gdW5kZWZpbmVkKSB1cGRhdGVEYXRhLm5hbWUgPSBkYXRhLm5hbWVcbiAgICBpZiAoZGF0YS5hbW91bnQgIT09IHVuZGVmaW5lZCkgdXBkYXRlRGF0YS5hbW91bnQgPSBkYXRhLmFtb3VudFxuICAgIGlmIChkYXRhLnBlcmlvZCAhPT0gdW5kZWZpbmVkKSB1cGRhdGVEYXRhLnBlcmlvZCA9IGRhdGEucGVyaW9kXG4gICAgaWYgKGRhdGEuY2F0ZWdvcnlfaWQgIT09IHVuZGVmaW5lZCkgdXBkYXRlRGF0YS5jYXRlZ29yeV9pZCA9IGRhdGEuY2F0ZWdvcnlfaWQgfHwgbnVsbFxuICAgIGlmIChkYXRhLnN0YXJ0X2RhdGUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgdXBkYXRlRGF0YS5zdGFydF9kYXRlID0gZGF0YS5zdGFydF9kYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXVxuICAgIH1cbiAgICBpZiAoZGF0YS5lbmRfZGF0ZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICB1cGRhdGVEYXRhLmVuZF9kYXRlID0gZGF0YS5lbmRfZGF0ZSA/IGRhdGEuZW5kX2RhdGUudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdIDogbnVsbFxuICAgIH1cblxuICAgIHVwZGF0ZURhdGEudXBkYXRlZF9hdCA9IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuXG4gICAgY29uc3QgeyBkYXRhOiBidWRnZXQsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2J1ZGdldHMnKVxuICAgICAgLnVwZGF0ZSh1cGRhdGVEYXRhKVxuICAgICAgLmVxKCdpZCcsIGlkKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBjYXRlZ29yeTpjYXRlZ29yaWVzKCopXG4gICAgICBgKVxuICAgICAgLnNpbmdsZSgpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHVwZGF0ZSBidWRnZXQ6ICR7ZXJyb3IubWVzc2FnZX1gKVxuICAgIH1cblxuICAgIHJldHVybiBidWRnZXQgYXMgSUJ1ZGdldFxuICB9XG5cbiAgc3RhdGljIGFzeW5jIGRlbGV0ZUJ1ZGdldChpZDogc3RyaW5nKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKClcbiAgICBcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignVXNlciBub3QgYXV0aGVudGljYXRlZCcpXG4gICAgfVxuXG4gICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgIC5mcm9tKCdidWRnZXRzJylcbiAgICAgIC5kZWxldGUoKVxuICAgICAgLmVxKCdpZCcsIGlkKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcblxuICAgIGlmIChlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZGVsZXRlIGJ1ZGdldDogJHtlcnJvci5tZXNzYWdlfWApXG4gICAgfVxuICB9XG5cbiAgcHJpdmF0ZSBzdGF0aWMgYXN5bmMgY2FsY3VsYXRlQnVkZ2V0UHJvZ3Jlc3MoYnVkZ2V0OiBJQnVkZ2V0KTogUHJvbWlzZTxJQnVkZ2V0V2l0aFByb2dyZXNzPiB7XG4gICAgY29uc3QgeyBkYXRhOiB7IHVzZXIgfSB9ID0gYXdhaXQgc3VwYWJhc2UuYXV0aC5nZXRVc2VyKClcbiAgICBcbiAgICBpZiAoIXVzZXIpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignVXNlciBub3QgYXV0aGVudGljYXRlZCcpXG4gICAgfVxuXG4gICAgLy8gQ2FsY3VsYXRlIGRhdGUgcmFuZ2UgZm9yIGJ1ZGdldCBwZXJpb2RcbiAgICBjb25zdCB7IHN0YXJ0RGF0ZSwgZW5kRGF0ZSB9ID0gdGhpcy5nZXRCdWRnZXREYXRlUmFuZ2UoYnVkZ2V0KVxuXG4gICAgLy8gQnVpbGQgcXVlcnkgZm9yIHRyYW5zYWN0aW9uc1xuICAgIGxldCBxdWVyeSA9IHN1cGFiYXNlXG4gICAgICAuZnJvbSgndHJhbnNhY3Rpb25zJylcbiAgICAgIC5zZWxlY3QoYFxuICAgICAgICAqLFxuICAgICAgICBjYXRlZ29yeTpjYXRlZ29yaWVzKCopXG4gICAgICBgKVxuICAgICAgLmVxKCd1c2VyX2lkJywgdXNlci5pZClcbiAgICAgIC5lcSgndHJhbnNhY3Rpb25fdHlwZScsICdleHBlbnNlJylcbiAgICAgIC5ndGUoJ3RyYW5zYWN0aW9uX2RhdGUnLCBzdGFydERhdGUpXG4gICAgICAubHRlKCd0cmFuc2FjdGlvbl9kYXRlJywgZW5kRGF0ZSlcblxuICAgIC8vIEZpbHRlciBieSBjYXRlZ29yeSBpZiBidWRnZXQgaXMgY2F0ZWdvcnktc3BlY2lmaWNcbiAgICBpZiAoYnVkZ2V0LmNhdGVnb3J5X2lkKSB7XG4gICAgICBxdWVyeSA9IHF1ZXJ5LmVxKCdjYXRlZ29yeV9pZCcsIGJ1ZGdldC5jYXRlZ29yeV9pZClcbiAgICB9XG5cbiAgICBjb25zdCB7IGRhdGE6IHRyYW5zYWN0aW9ucywgZXJyb3IgfSA9IGF3YWl0IHF1ZXJ5XG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIHRyYW5zYWN0aW9ucyBmb3IgYnVkZ2V0OiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICAvLyBDYWxjdWxhdGUgc3BlbmRpbmdcbiAgICBjb25zdCBzcGVudCA9IHRyYW5zYWN0aW9ucz8ucmVkdWNlKChzdW0sIHRyYW5zYWN0aW9uKSA9PiBzdW0gKyB0cmFuc2FjdGlvbi5hbW91bnQsIDApIHx8IDBcbiAgICBjb25zdCByZW1haW5pbmcgPSBNYXRoLm1heCgwLCBidWRnZXQuYW1vdW50IC0gc3BlbnQpXG4gICAgY29uc3QgcHJvZ3Jlc3MgPSBNYXRoLm1pbigxMDAsIChzcGVudCAvIGJ1ZGdldC5hbW91bnQpICogMTAwKVxuICAgIGNvbnN0IGlzT3ZlckJ1ZGdldCA9IHNwZW50ID4gYnVkZ2V0LmFtb3VudFxuXG4gICAgcmV0dXJuIHtcbiAgICAgIC4uLmJ1ZGdldCxcbiAgICAgIHNwZW50LFxuICAgICAgcmVtYWluaW5nLFxuICAgICAgcHJvZ3Jlc3MsXG4gICAgICBpc092ZXJCdWRnZXQsXG4gICAgICB0cmFuc2FjdGlvbnM6IHRyYW5zYWN0aW9ucyBhcyBJVHJhbnNhY3Rpb25bXVxuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgc3RhdGljIGdldEJ1ZGdldERhdGVSYW5nZShidWRnZXQ6IElCdWRnZXQpOiB7IHN0YXJ0RGF0ZTogc3RyaW5nOyBlbmREYXRlOiBzdHJpbmcgfSB7XG4gICAgY29uc3QgYnVkZ2V0U3RhcnREYXRlID0gbmV3IERhdGUoYnVkZ2V0LnN0YXJ0X2RhdGUpXG4gICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKVxuICAgIFxuICAgIC8vIElmIGJ1ZGdldCBoYXMgc3BlY2lmaWMgZW5kIGRhdGUsIHVzZSBpdFxuICAgIGlmIChidWRnZXQuZW5kX2RhdGUpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHN0YXJ0RGF0ZTogYnVkZ2V0LnN0YXJ0X2RhdGUsXG4gICAgICAgIGVuZERhdGU6IGJ1ZGdldC5lbmRfZGF0ZVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIENhbGN1bGF0ZSBlbmQgZGF0ZSBiYXNlZCBvbiBwZXJpb2RcbiAgICBsZXQgZW5kRGF0ZTogRGF0ZVxuXG4gICAgc3dpdGNoIChidWRnZXQucGVyaW9kKSB7XG4gICAgICBjYXNlICd3ZWVrbHknOlxuICAgICAgICBlbmREYXRlID0gbmV3IERhdGUoYnVkZ2V0U3RhcnREYXRlKVxuICAgICAgICBlbmREYXRlLnNldERhdGUoZW5kRGF0ZS5nZXREYXRlKCkgKyA3KVxuICAgICAgICBicmVha1xuICAgICAgY2FzZSAnbW9udGhseSc6XG4gICAgICAgIGVuZERhdGUgPSBuZXcgRGF0ZShidWRnZXRTdGFydERhdGUpXG4gICAgICAgIGVuZERhdGUuc2V0TW9udGgoZW5kRGF0ZS5nZXRNb250aCgpICsgMSlcbiAgICAgICAgYnJlYWtcbiAgICAgIGNhc2UgJ3llYXJseSc6XG4gICAgICAgIGVuZERhdGUgPSBuZXcgRGF0ZShidWRnZXRTdGFydERhdGUpXG4gICAgICAgIGVuZERhdGUuc2V0RnVsbFllYXIoZW5kRGF0ZS5nZXRGdWxsWWVhcigpICsgMSlcbiAgICAgICAgYnJlYWtcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIGVuZERhdGUgPSBuZXcgRGF0ZShidWRnZXRTdGFydERhdGUpXG4gICAgICAgIGVuZERhdGUuc2V0TW9udGgoZW5kRGF0ZS5nZXRNb250aCgpICsgMSlcbiAgICB9XG5cbiAgICAvLyBGb3IgcmVjdXJyaW5nIGJ1ZGdldHMsIGZpbmQgdGhlIGN1cnJlbnQgcGVyaW9kXG4gICAgd2hpbGUgKGVuZERhdGUgPCBub3cpIHtcbiAgICAgIHN3aXRjaCAoYnVkZ2V0LnBlcmlvZCkge1xuICAgICAgICBjYXNlICd3ZWVrbHknOlxuICAgICAgICAgIGJ1ZGdldFN0YXJ0RGF0ZS5zZXREYXRlKGJ1ZGdldFN0YXJ0RGF0ZS5nZXREYXRlKCkgKyA3KVxuICAgICAgICAgIGVuZERhdGUuc2V0RGF0ZShlbmREYXRlLmdldERhdGUoKSArIDcpXG4gICAgICAgICAgYnJlYWtcbiAgICAgICAgY2FzZSAnbW9udGhseSc6XG4gICAgICAgICAgYnVkZ2V0U3RhcnREYXRlLnNldE1vbnRoKGJ1ZGdldFN0YXJ0RGF0ZS5nZXRNb250aCgpICsgMSlcbiAgICAgICAgICBlbmREYXRlLnNldE1vbnRoKGVuZERhdGUuZ2V0TW9udGgoKSArIDEpXG4gICAgICAgICAgYnJlYWtcbiAgICAgICAgY2FzZSAneWVhcmx5JzpcbiAgICAgICAgICBidWRnZXRTdGFydERhdGUuc2V0RnVsbFllYXIoYnVkZ2V0U3RhcnREYXRlLmdldEZ1bGxZZWFyKCkgKyAxKVxuICAgICAgICAgIGVuZERhdGUuc2V0RnVsbFllYXIoZW5kRGF0ZS5nZXRGdWxsWWVhcigpICsgMSlcbiAgICAgICAgICBicmVha1xuICAgICAgfVxuICAgIH1cblxuICAgIHJldHVybiB7XG4gICAgICBzdGFydERhdGU6IGJ1ZGdldFN0YXJ0RGF0ZS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF0sXG4gICAgICBlbmREYXRlOiBlbmREYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXVxuICAgIH1cbiAgfVxuXG4gIHN0YXRpYyBhc3luYyBjaGVja0J1ZGdldEFsZXJ0cygpOiBQcm9taXNlPElCdWRnZXRXaXRoUHJvZ3Jlc3NbXT4ge1xuICAgIGNvbnN0IGJ1ZGdldHNXaXRoUHJvZ3Jlc3MgPSBhd2FpdCB0aGlzLmdldEJ1ZGdldHNXaXRoUHJvZ3Jlc3MoKVxuICAgIFxuICAgIC8vIFJldHVybiBidWRnZXRzIHRoYXQgYXJlIGF0IDgwJSBvciBvdmVyIHRoZWlyIGxpbWl0XG4gICAgcmV0dXJuIGJ1ZGdldHNXaXRoUHJvZ3Jlc3MuZmlsdGVyKGJ1ZGdldCA9PiBidWRnZXQucHJvZ3Jlc3MgPj0gODApXG4gIH1cblxuICBzdGF0aWMgYXN5bmMgY3JlYXRlUmVjdXJyaW5nQnVkZ2V0cygpOiBQcm9taXNlPElCdWRnZXRbXT4ge1xuICAgIGNvbnN0IHsgZGF0YTogeyB1c2VyIH0gfSA9IGF3YWl0IHN1cGFiYXNlLmF1dGguZ2V0VXNlcigpXG4gICAgXG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnKVxuICAgIH1cblxuICAgIC8vIEdldCBhbGwgYnVkZ2V0cyB0aGF0IGRvbid0IGhhdmUgYW4gZW5kX2RhdGUgKHJlY3VycmluZylcbiAgICBjb25zdCB7IGRhdGE6IHJlY3VycmluZ0J1ZGdldHMsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgLmZyb20oJ2J1ZGdldHMnKVxuICAgICAgLnNlbGVjdCgnKicpXG4gICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VyLmlkKVxuICAgICAgLmlzKCdlbmRfZGF0ZScsIG51bGwpXG5cbiAgICBpZiAoZXJyb3IpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIHJlY3VycmluZyBidWRnZXRzOiAke2Vycm9yLm1lc3NhZ2V9YClcbiAgICB9XG5cbiAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpXG4gICAgY29uc3QgbmV3QnVkZ2V0czogSUJ1ZGdldFtdID0gW11cblxuICAgIGZvciAoY29uc3QgYnVkZ2V0IG9mIHJlY3VycmluZ0J1ZGdldHMgfHwgW10pIHtcbiAgICAgIGNvbnN0IHsgZW5kRGF0ZSB9ID0gdGhpcy5nZXRCdWRnZXREYXRlUmFuZ2UoYnVkZ2V0IGFzIElCdWRnZXQpXG4gICAgICBcbiAgICAgIC8vIElmIGN1cnJlbnQgcGVyaW9kIGhhcyBlbmRlZCwgY3JlYXRlIG5ldyBidWRnZXQgZm9yIG5leHQgcGVyaW9kXG4gICAgICBpZiAobmV3IERhdGUoZW5kRGF0ZSkgPCBub3cpIHtcbiAgICAgICAgY29uc3QgbmV3U3RhcnREYXRlID0gbmV3IERhdGUoZW5kRGF0ZSlcbiAgICAgICAgbmV3U3RhcnREYXRlLnNldERhdGUobmV3U3RhcnREYXRlLmdldERhdGUoKSArIDEpXG5cbiAgICAgICAgY29uc3QgbmV3QnVkZ2V0RGF0YSA9IHtcbiAgICAgICAgICBuYW1lOiBidWRnZXQubmFtZSxcbiAgICAgICAgICBhbW91bnQ6IGJ1ZGdldC5hbW91bnQsXG4gICAgICAgICAgcGVyaW9kOiBidWRnZXQucGVyaW9kLFxuICAgICAgICAgIGNhdGVnb3J5X2lkOiBidWRnZXQuY2F0ZWdvcnlfaWQsXG4gICAgICAgICAgc3RhcnRfZGF0ZTogbmV3U3RhcnREYXRlLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXSxcbiAgICAgICAgICBlbmRfZGF0ZTogbnVsbCxcbiAgICAgICAgICB1c2VyX2lkOiB1c2VyLmlkLFxuICAgICAgICB9XG5cbiAgICAgICAgY29uc3QgeyBkYXRhOiBuZXdCdWRnZXQsIGVycm9yOiBjcmVhdGVFcnJvciB9ID0gYXdhaXQgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbSgnYnVkZ2V0cycpXG4gICAgICAgICAgLmluc2VydChuZXdCdWRnZXREYXRhKVxuICAgICAgICAgIC5zZWxlY3QoYFxuICAgICAgICAgICAgKixcbiAgICAgICAgICAgIGNhdGVnb3J5OmNhdGVnb3JpZXMoKilcbiAgICAgICAgICBgKVxuICAgICAgICAgIC5zaW5nbGUoKVxuXG4gICAgICAgIGlmIChjcmVhdGVFcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoYEZhaWxlZCB0byBjcmVhdGUgcmVjdXJyaW5nIGJ1ZGdldDogJHtjcmVhdGVFcnJvci5tZXNzYWdlfWApXG4gICAgICAgICAgY29udGludWVcbiAgICAgICAgfVxuXG4gICAgICAgIG5ld0J1ZGdldHMucHVzaChuZXdCdWRnZXQgYXMgSUJ1ZGdldClcbiAgICAgIH1cbiAgICB9XG5cbiAgICByZXR1cm4gbmV3QnVkZ2V0c1xuICB9XG59Il0sIm5hbWVzIjpbInN1cGFiYXNlIiwiQnVkZ2V0U2VydmljZSIsImNyZWF0ZUJ1ZGdldCIsImRhdGEiLCJ1c2VyIiwiYXV0aCIsImdldFVzZXIiLCJFcnJvciIsImJ1ZGdldERhdGEiLCJuYW1lIiwiYW1vdW50IiwicGVyaW9kIiwiY2F0ZWdvcnlfaWQiLCJzdGFydF9kYXRlIiwidG9JU09TdHJpbmciLCJzcGxpdCIsImVuZF9kYXRlIiwidXNlcl9pZCIsImlkIiwiYnVkZ2V0IiwiZXJyb3IiLCJmcm9tIiwiaW5zZXJ0Iiwic2VsZWN0Iiwic2luZ2xlIiwibWVzc2FnZSIsImdldEJ1ZGdldHMiLCJlcSIsIm9yZGVyIiwiYXNjZW5kaW5nIiwiZ2V0QnVkZ2V0V2l0aFByb2dyZXNzIiwiYnVkZ2V0SWQiLCJidWRnZXRFcnJvciIsInByb2dyZXNzIiwiY2FsY3VsYXRlQnVkZ2V0UHJvZ3Jlc3MiLCJnZXRCdWRnZXRzV2l0aFByb2dyZXNzIiwiYnVkZ2V0cyIsImJ1ZGdldHNXaXRoUHJvZ3Jlc3MiLCJQcm9taXNlIiwiYWxsIiwibWFwIiwidXBkYXRlQnVkZ2V0IiwidXBkYXRlRGF0YSIsInVuZGVmaW5lZCIsInVwZGF0ZWRfYXQiLCJEYXRlIiwidXBkYXRlIiwiZGVsZXRlQnVkZ2V0IiwiZGVsZXRlIiwic3RhcnREYXRlIiwiZW5kRGF0ZSIsImdldEJ1ZGdldERhdGVSYW5nZSIsInF1ZXJ5IiwiZ3RlIiwibHRlIiwidHJhbnNhY3Rpb25zIiwic3BlbnQiLCJyZWR1Y2UiLCJzdW0iLCJ0cmFuc2FjdGlvbiIsInJlbWFpbmluZyIsIk1hdGgiLCJtYXgiLCJtaW4iLCJpc092ZXJCdWRnZXQiLCJidWRnZXRTdGFydERhdGUiLCJub3ciLCJzZXREYXRlIiwiZ2V0RGF0ZSIsInNldE1vbnRoIiwiZ2V0TW9udGgiLCJzZXRGdWxsWWVhciIsImdldEZ1bGxZZWFyIiwiY2hlY2tCdWRnZXRBbGVydHMiLCJmaWx0ZXIiLCJjcmVhdGVSZWN1cnJpbmdCdWRnZXRzIiwicmVjdXJyaW5nQnVkZ2V0cyIsImlzIiwibmV3QnVkZ2V0cyIsIm5ld1N0YXJ0RGF0ZSIsIm5ld0J1ZGdldERhdGEiLCJuZXdCdWRnZXQiLCJjcmVhdGVFcnJvciIsImNvbnNvbGUiLCJwdXNoIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/lib/budget.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/lib/expenses.ts":
/*!*************************************************!*\
  !*** ../../packages/shared/src/lib/expenses.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExpenseService: () => (/* binding */ ExpenseService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/../../packages/shared/src/lib/supabase.ts\");\n\nclass ExpenseService {\n    static async createTransaction(data) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const transactionData = {\n            amount: data.amount,\n            description: data.description || null,\n            category_id: data.category_id,\n            transaction_type: data.transaction_type,\n            transaction_date: data.transaction_date.toISOString().split('T')[0],\n            user_id: user.id\n        };\n        const { data: transaction, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(`\n        *,\n        category:categories(*)\n      `).single();\n        if (error) {\n            throw new Error(`Failed to create transaction: ${error.message}`);\n        }\n        return transaction;\n    }\n    static async getTransactions(options) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        let query = _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(`\n        *,\n        category:categories(*)\n      `, {\n            count: 'exact'\n        }).eq('user_id', user.id).order('transaction_date', {\n            ascending: false\n        }).order('created_at', {\n            ascending: false\n        });\n        // Apply filters\n        if (options?.categoryId) {\n            query = query.eq('category_id', options.categoryId);\n        }\n        if (options?.startDate) {\n            query = query.gte('transaction_date', options.startDate);\n        }\n        if (options?.endDate) {\n            query = query.lte('transaction_date', options.endDate);\n        }\n        if (options?.transactionType) {\n            query = query.eq('transaction_type', options.transactionType);\n        }\n        if (options?.searchQuery) {\n            // Search in both description and category name\n            query = query.or(`description.ilike.%${options.searchQuery}%,` + `category.name.ilike.%${options.searchQuery}%`);\n        }\n        // Apply pagination\n        if (options?.limit) {\n            query = query.limit(options.limit);\n        }\n        if (options?.offset) {\n            query = query.range(options.offset, options.offset + (options.limit || 10) - 1);\n        }\n        const { data, error, count } = await query;\n        if (error) {\n            throw new Error(`Failed to fetch transactions: ${error.message}`);\n        }\n        return {\n            data: data,\n            count: count || 0\n        };\n    }\n    static async updateTransaction(id, data) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const updateData = {};\n        if (data.amount !== undefined) updateData.amount = data.amount;\n        if (data.description !== undefined) updateData.description = data.description;\n        if (data.category_id !== undefined) updateData.category_id = data.category_id;\n        if (data.transaction_type !== undefined) updateData.transaction_type = data.transaction_type;\n        if (data.transaction_date !== undefined) {\n            updateData.transaction_date = data.transaction_date.toISOString().split('T')[0];\n        }\n        updateData.updated_at = new Date().toISOString();\n        const { data: transaction, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').update(updateData).eq('id', id).eq('user_id', user.id).select(`\n        *,\n        category:categories(*)\n      `).single();\n        if (error) {\n            throw new Error(`Failed to update transaction: ${error.message}`);\n        }\n        return transaction;\n    }\n    static async deleteTransaction(id) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').delete().eq('id', id).eq('user_id', user.id);\n        if (error) {\n            throw new Error(`Failed to delete transaction: ${error.message}`);\n        }\n    }\n    static async getCategories() {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('*').or(`user_id.eq.${user.id},and(is_default.eq.true,user_id.is.null)`).order('name');\n        if (error) {\n            throw new Error(`Failed to fetch categories: ${error.message}`);\n        }\n        // If no categories exist for the user, create default categories\n        if (!data || data.length === 0) {\n            await this.createDefaultCategories();\n            // Fetch categories again after creating defaults\n            const { data: newData, error: newError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('*').or(`user_id.eq.${user.id},and(is_default.eq.true,user_id.is.null)`).order('name');\n            if (newError) {\n                throw new Error(`Failed to fetch categories after creating defaults: ${newError.message}`);\n            }\n            return newData;\n        }\n        return data;\n    }\n    static async createDefaultCategories() {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Check if user already has categories to prevent duplicates\n        const { data: existingCategories, error: checkError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').select('id').eq('user_id', user.id).limit(1);\n        if (checkError) {\n            throw new Error(`Failed to check existing categories: ${checkError.message}`);\n        }\n        // If user already has categories, don't create defaults\n        if (existingCategories && existingCategories.length > 0) {\n            return;\n        }\n        const defaultCategories = [\n            // Expense categories\n            {\n                name: 'Food & Dining',\n                icon: '🍽️',\n                color: '#FF6B6B',\n                type: 'expense'\n            },\n            {\n                name: 'Transportation',\n                icon: '🚗',\n                color: '#4ECDC4',\n                type: 'expense'\n            },\n            {\n                name: 'Shopping',\n                icon: '🛍️',\n                color: '#45B7D1',\n                type: 'expense'\n            },\n            {\n                name: 'Entertainment',\n                icon: '🎬',\n                color: '#96CEB4',\n                type: 'expense'\n            },\n            {\n                name: 'Bills & Utilities',\n                icon: '💡',\n                color: '#FFEAA7',\n                type: 'expense'\n            },\n            {\n                name: 'Healthcare',\n                icon: '🏥',\n                color: '#DDA0DD',\n                type: 'expense'\n            },\n            {\n                name: 'Education',\n                icon: '📚',\n                color: '#98D8C8',\n                type: 'expense'\n            },\n            {\n                name: 'Travel',\n                icon: '✈️',\n                color: '#F7DC6F',\n                type: 'expense'\n            },\n            {\n                name: 'Groceries',\n                icon: '🛒',\n                color: '#82E0AA',\n                type: 'expense'\n            },\n            {\n                name: 'Other',\n                icon: '📦',\n                color: '#BDC3C7',\n                type: 'expense'\n            },\n            // Income categories\n            {\n                name: 'Salary',\n                icon: '💰',\n                color: '#27AE60',\n                type: 'income'\n            },\n            {\n                name: 'Freelance',\n                icon: '💻',\n                color: '#2ECC71',\n                type: 'income'\n            },\n            {\n                name: 'Business',\n                icon: '🏢',\n                color: '#58D68D',\n                type: 'income'\n            },\n            {\n                name: 'Investment',\n                icon: '📈',\n                color: '#85C1E9',\n                type: 'income'\n            },\n            {\n                name: 'Gift',\n                icon: '🎁',\n                color: '#F8C471',\n                type: 'income'\n            },\n            {\n                name: 'Other Income',\n                icon: '💎',\n                color: '#D5DBDB',\n                type: 'income'\n            }\n        ];\n        const categoriesToInsert = defaultCategories.map((category)=>({\n                ...category,\n                user_id: user.id,\n                is_default: false\n            }));\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').insert(categoriesToInsert);\n        if (error) {\n            throw new Error(`Failed to create default categories: ${error.message}`);\n        }\n    }\n    static async createCategory(categoryData) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('categories').insert({\n            ...categoryData,\n            user_id: user.id,\n            is_default: false\n        }).select('*').single();\n        if (error) {\n            throw new Error(`Failed to create category: ${error.message}`);\n        }\n        return data;\n    }\n    static async getMonthlySpending(year, month) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const startDate = new Date(year, month - 1, 1).toISOString().split('T')[0];\n        const endDate = new Date(year, month, 0).toISOString().split('T')[0];\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').select(`\n        *,\n        category:categories(*)\n      `).eq('user_id', user.id).gte('transaction_date', startDate).lte('transaction_date', endDate);\n        if (error) {\n            throw new Error(`Failed to fetch monthly spending: ${error.message}`);\n        }\n        const transactions = data;\n        const totalIncome = transactions.filter((t)=>t.transaction_type === 'income').reduce((sum, t)=>sum + t.amount, 0);\n        const totalExpenses = transactions.filter((t)=>t.transaction_type === 'expense').reduce((sum, t)=>sum + t.amount, 0);\n        // Group by category\n        const categoryMap = {};\n        transactions.forEach((transaction)=>{\n            if (transaction.category) {\n                const categoryId = transaction.category.id;\n                if (!categoryMap[categoryId]) {\n                    categoryMap[categoryId] = {\n                        category: transaction.category,\n                        total: 0\n                    };\n                }\n                categoryMap[categoryId].total += transaction.amount;\n            }\n        });\n        const categoryBreakdown = Object.values(categoryMap);\n        return {\n            totalIncome,\n            totalExpenses,\n            categoryBreakdown\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/lib/expenses.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/lib/recurring-transactions.ts":
/*!***************************************************************!*\
  !*** ../../packages/shared/src/lib/recurring-transactions.ts ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecurringTransactionService: () => (/* binding */ RecurringTransactionService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(ssr)/../../packages/shared/src/lib/supabase.ts\");\n\nclass RecurringTransactionService {\n    /**\n   * Get all recurring templates for the current user\n   */ static async getRecurringTemplates() {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transaction_templates').select('*').eq('user_id', user.id).eq('is_recurring', true).order('name');\n        if (error) {\n            throw new Error(`Failed to fetch recurring templates: ${error.message}`);\n        }\n        return data || [];\n    }\n    /**\n   * Get all due recurring transactions for the current user\n   */ static async getDueRecurringTransactions() {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transaction_templates').select(`\n        *,\n        category:categories(*)\n      `).eq('user_id', user.id).eq('is_recurring', true).not('next_due_date', 'is', null).lte('next_due_date', new Date().toISOString().split('T')[0]);\n        if (error) {\n            throw new Error(`Failed to fetch due recurring transactions: ${error.message}`);\n        }\n        const templates = data || [];\n        const today = new Date();\n        return templates.map((template)=>{\n            const dueDate = new Date(template.next_due_date);\n            const daysOverdue = Math.floor((today.getTime() - dueDate.getTime()) / (1000 * 60 * 60 * 24));\n            return {\n                template: template,\n                daysOverdue: Math.max(0, daysOverdue),\n                nextDueDate: dueDate,\n                category: template.category\n            };\n        });\n    }\n    /**\n   * Calculate the next due date based on frequency\n   */ static calculateNextDueDate(currentDate, frequency) {\n        const nextDate = new Date(currentDate);\n        switch(frequency){\n            case 'weekly':\n                nextDate.setDate(nextDate.getDate() + 7);\n                break;\n            case 'monthly':\n                // Handle month-end edge cases\n                const originalDay = nextDate.getDate();\n                nextDate.setMonth(nextDate.getMonth() + 1);\n                // If the day doesn't exist in the next month (e.g., Jan 31 -> Feb 28)\n                if (nextDate.getDate() !== originalDay) {\n                    nextDate.setDate(0) // Set to last day of previous month\n                    ;\n                }\n                break;\n            case 'yearly':\n                nextDate.setFullYear(nextDate.getFullYear() + 1);\n                // Handle leap year edge case (Feb 29 -> Feb 28)\n                if (nextDate.getMonth() === 1 && nextDate.getDate() === 29) {\n                    // Check if next year is not a leap year\n                    const nextYear = nextDate.getFullYear();\n                    if (!this.isLeapYear(nextYear)) {\n                        nextDate.setDate(28);\n                    }\n                }\n                break;\n            default:\n                throw new Error(`Invalid frequency: ${frequency}`);\n        }\n        return nextDate;\n    }\n    /**\n   * Check if a year is a leap year\n   */ static isLeapYear(year) {\n        return year % 4 === 0 && year % 100 !== 0 || year % 400 === 0;\n    }\n    /**\n   * Create a transaction from a recurring template\n   */ static async createTransactionFromTemplate(template, transactionDate) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const date = transactionDate || new Date();\n        // Create the transaction\n        const transactionData = {\n            amount: template.amount,\n            category_id: template.category_id,\n            description: template.description,\n            transaction_type: template.transaction_type,\n            transaction_date: date.toISOString().split('T')[0],\n            user_id: user.id\n        };\n        const { data: transaction, error: transactionError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transactions').insert(transactionData).select(`\n        *,\n        category:categories(*)\n      `).single();\n        if (transactionError) {\n            throw new Error(`Failed to create transaction: ${transactionError.message}`);\n        }\n        // Update the template with the next due date and last created date\n        if (template.frequency) {\n            const nextDueDate = this.calculateNextDueDate(date, template.frequency);\n            const { error: updateError } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transaction_templates').update({\n                next_due_date: nextDueDate.toISOString().split('T')[0],\n                last_created_date: date.toISOString().split('T')[0]\n            }).eq('id', template.id);\n            if (updateError) {\n                console.error('Failed to update template next due date:', updateError);\n            // Don't throw error as transaction was created successfully\n            }\n        }\n        return transaction;\n    }\n    /**\n   * Create transactions from multiple templates\n   */ static async createTransactionsFromTemplates(templates, transactionDate) {\n        const success = [];\n        const errors = [];\n        for (const template of templates){\n            try {\n                const transaction = await this.createTransactionFromTemplate(template, transactionDate);\n                success.push(transaction);\n            } catch (error) {\n                errors.push({\n                    template,\n                    error: error instanceof Error ? error.message : 'Unknown error'\n                });\n            }\n        }\n        return {\n            success,\n            errors\n        };\n    }\n    /**\n   * Update a recurring template\n   */ static async updateRecurringTemplate(templateId, updates) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // If frequency changed, recalculate next due date\n        if (updates.frequency && updates.last_created_date) {\n            const lastCreated = new Date(updates.last_created_date);\n            updates.next_due_date = this.calculateNextDueDate(lastCreated, updates.frequency).toISOString().split('T')[0];\n        }\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transaction_templates').update(updates).eq('id', templateId).eq('user_id', user.id).select().single();\n        if (error) {\n            throw new Error(`Failed to update recurring template: ${error.message}`);\n        }\n        return data;\n    }\n    /**\n   * Create a new recurring template\n   */ static async createRecurringTemplate(templateData) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        // Calculate initial next due date if recurring\n        let nextDueDate = null;\n        if (templateData.is_recurring && templateData.frequency) {\n            const startDate = new Date();\n            nextDueDate = this.calculateNextDueDate(startDate, templateData.frequency).toISOString().split('T')[0];\n        }\n        const insertData = {\n            ...templateData,\n            user_id: user.id,\n            next_due_date: nextDueDate\n        };\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transaction_templates').insert(insertData).select().single();\n        if (error) {\n            throw new Error(`Failed to create recurring template: ${error.message}`);\n        }\n        return data;\n    }\n    /**\n   * Disable recurring for a template\n   */ static async disableRecurring(templateId) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transaction_templates').update({\n            is_recurring: false,\n            frequency: null,\n            next_due_date: null,\n            auto_create: false\n        }).eq('id', templateId).eq('user_id', user.id);\n        if (error) {\n            throw new Error(`Failed to disable recurring: ${error.message}`);\n        }\n    }\n    /**\n   * Process all due recurring transactions for auto-create templates\n   * This should be called on app startup/login\n   */ static async processAutoCreateRecurringTransactions() {\n        try {\n            const dueTransactions = await this.getDueRecurringTransactions();\n            const autoCreateTemplates = dueTransactions.filter(({ template })=>template.auto_create).map(({ template })=>template);\n            if (autoCreateTemplates.length === 0) {\n                return {\n                    created: 0,\n                    errors: []\n                };\n            }\n            const result = await this.createTransactionsFromTemplates(autoCreateTemplates);\n            return {\n                created: result.success.length,\n                errors: result.errors\n            };\n        } catch (error) {\n            console.error('Error processing auto-create recurring transactions:', error);\n            return {\n                created: 0,\n                errors: [\n                    {\n                        template: {},\n                        error: error instanceof Error ? error.message : 'Unknown error'\n                    }\n                ]\n            };\n        }\n    }\n    /**\n   * Get upcoming recurring transactions (next 30 days)\n   */ static async getUpcomingRecurringTransactions(days = 30) {\n        const { data: { user } } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.auth.getUser();\n        if (!user) {\n            throw new Error('User not authenticated');\n        }\n        const today = new Date();\n        const futureDate = new Date();\n        futureDate.setDate(today.getDate() + days);\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('transaction_templates').select(`\n        *,\n        category:categories(*)\n      `).eq('user_id', user.id).eq('is_recurring', true).not('next_due_date', 'is', null).gte('next_due_date', today.toISOString().split('T')[0]).lte('next_due_date', futureDate.toISOString().split('T')[0]);\n        if (error) {\n            throw new Error(`Failed to fetch upcoming recurring transactions: ${error.message}`);\n        }\n        const templates = data || [];\n        return templates.map((template)=>{\n            const dueDate = new Date(template.next_due_date);\n            const daysUntilDue = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));\n            return {\n                template: template,\n                dueDate,\n                daysUntilDue,\n                category: template.category\n            };\n        }).sort((a, b)=>a.daysUntilDue - b.daysUntilDue);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/lib/recurring-transactions.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/lib/supabase.mobile.ts":
/*!********************************************************!*\
  !*** ../../packages/shared/src/lib/supabase.mobile.ts ***!
  \********************************************************/
/***/ (() => {

eval("//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsImZpbGUiOiIoc3NyKS8uLi8uLi9wYWNrYWdlcy9zaGFyZWQvc3JjL2xpYi9zdXBhYmFzZS5tb2JpbGUudHMiLCJzb3VyY2VSb290IjoiIiwiaWdub3JlTGlzdCI6W119\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/lib/supabase.mobile.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/lib/supabase.ts":
/*!*************************************************!*\
  !*** ../../packages/shared/src/lib/supabase.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/../../node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://iconspmonvnujpnfmyqx.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imljb25zcG1vbnZudWpwbmZteXF4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAyMzQwMDEsImV4cCI6MjA2NTgxMDAwMX0.xKtYUC7fBGZXlrxrI_DJxMvzBL1th7h60H2Qn-x6-So\" || 0;\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error('Missing Supabase environment variables');\n}\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvc2hhcmVkL3NyYy9saWIvc3VwYWJhc2UudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBb0Q7QUFHcEQsTUFBTUMsY0FBY0MsMENBQW9DLElBQUlBLENBQW9DO0FBQ2hHLE1BQU1JLGtCQUFrQkosa05BQXlDLElBQUlBLENBQXlDO0FBRTlHLElBQUksQ0FBQ0QsZUFBZSxDQUFDSyxpQkFBaUI7SUFDcEMsTUFBTSxJQUFJRyxNQUFNO0FBQ2xCO0FBRU8sTUFBTUMsV0FBV1YsbUVBQVlBLENBQVdDLGFBQWFLLGlCQUFpQjtJQUMzRUssTUFBTTtRQUNKQyxrQkFBa0I7UUFDbEJDLGdCQUFnQjtRQUNoQkMsb0JBQW9CO0lBQ3RCO0FBQ0YsR0FBRSIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9wYWNrYWdlcy9zaGFyZWQvc3JjL2xpYi9zdXBhYmFzZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDbGllbnQgfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnXG5pbXBvcnQgdHlwZSB7IERhdGFiYXNlIH0gZnJvbSAnLi4vZGF0YWJhc2UudHlwZXMnXG5cbmNvbnN0IHN1cGFiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfVVJMIHx8IHByb2Nlc3MuZW52LkVYUE9fUFVCTElDX1NVUEFCQVNFX1VSTFxuY29uc3Qgc3VwYWJhc2VBbm9uS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkgfHwgcHJvY2Vzcy5lbnYuRVhQT19QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVlcblxuaWYgKCFzdXBhYmFzZVVybCB8fCAhc3VwYWJhc2VBbm9uS2V5KSB7XG4gIHRocm93IG5ldyBFcnJvcignTWlzc2luZyBTdXBhYmFzZSBlbnZpcm9ubWVudCB2YXJpYWJsZXMnKVxufVxuXG5leHBvcnQgY29uc3Qgc3VwYWJhc2UgPSBjcmVhdGVDbGllbnQ8RGF0YWJhc2U+KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUFub25LZXksIHtcbiAgYXV0aDoge1xuICAgIGF1dG9SZWZyZXNoVG9rZW46IHRydWUsXG4gICAgcGVyc2lzdFNlc3Npb246IHRydWUsXG4gICAgZGV0ZWN0U2Vzc2lvbkluVXJsOiB0cnVlLFxuICB9LFxufSlcblxuZXhwb3J0IHR5cGUgVXNlciA9IERhdGFiYXNlWydwdWJsaWMnXVsnVGFibGVzJ11bJ2NhdGVnb3JpZXMnXVsnUm93J11bJ3VzZXJfaWQnXSBleHRlbmRzIHN0cmluZyB8IG51bGwgXG4gID8geyBpZDogc3RyaW5nOyBlbWFpbD86IHN0cmluZzsgdXNlcl9tZXRhZGF0YT86IGFueSB9IFxuICA6IG5ldmVyIl0sIm5hbWVzIjpbImNyZWF0ZUNsaWVudCIsInN1cGFiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX1VSTCIsIkVYUE9fUFVCTElDX1NVUEFCQVNFX1VSTCIsInN1cGFiYXNlQW5vbktleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiRVhQT19QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJFcnJvciIsInN1cGFiYXNlIiwiYXV0aCIsImF1dG9SZWZyZXNoVG9rZW4iLCJwZXJzaXN0U2Vzc2lvbiIsImRldGVjdFNlc3Npb25JblVybCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/schemas/auth.ts":
/*!*************************************************!*\
  !*** ../../packages/shared/src/schemas/auth.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   resetPasswordSchema: () => (/* binding */ resetPasswordSchema),\n/* harmony export */   signInSchema: () => (/* binding */ signInSchema),\n/* harmony export */   signUpSchema: () => (/* binding */ signUpSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/zod/dist/esm/index.js\");\n\nconst signInSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email('Invalid email address'),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(6, 'Password must be at least 6 characters')\n});\nconst signUpSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(2, 'Name must be at least 2 characters'),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email('Invalid email address'),\n    password: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(6, 'Password must be at least 6 characters'),\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_0__.z.string()\n}).refine((data)=>data.password === data.confirmPassword, {\n    message: \"Passwords don't match\",\n    path: [\n        \"confirmPassword\"\n    ]\n});\nconst resetPasswordSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().email('Invalid email address')\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/schemas/auth.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/schemas/budget.ts":
/*!***************************************************!*\
  !*** ../../packages/shared/src/schemas/budget.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   budgetFormInputSchema: () => (/* binding */ budgetFormInputSchema),\n/* harmony export */   budgetFormSchema: () => (/* binding */ budgetFormSchema),\n/* harmony export */   budgetSchema: () => (/* binding */ budgetSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/zod/dist/esm/index.js\");\n\nconst budgetSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Budget name is required').max(100, 'Budget name must be under 100 characters'),\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Budget amount must be greater than 0'),\n    period: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'weekly',\n        'monthly',\n        'yearly'\n    ]),\n    category_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid('Please select a category').optional(),\n    start_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.date(),\n    end_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.date().optional()\n});\n// Base form schema without transform for form state\nconst budgetFormInputSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Budget name is required').max(100, 'Budget name must be under 100 characters'),\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Budget amount is required'),\n    period: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'weekly',\n        'monthly',\n        'yearly'\n    ]),\n    category_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    start_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.date(),\n    end_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.union([\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.date(),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z[\"null\"](),\n        zod__WEBPACK_IMPORTED_MODULE_0__.z.undefined()\n    ]).optional()\n});\n// Schema with transform for final validation\nconst budgetFormSchema = budgetFormInputSchema.transform((data)=>({\n        ...data,\n        amount: (()=>{\n            const num = parseFloat(data.amount.replace(/[^\\d.-]/g, ''));\n            if (isNaN(num) || num <= 0) {\n                throw new Error('Budget amount must be a positive number');\n            }\n            return num;\n        })(),\n        category_id: data.category_id || undefined,\n        end_date: data.end_date || undefined\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvc2hhcmVkL3NyYy9zY2hlbWFzL2J1ZGdldC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVCO0FBRWhCLE1BQU1DLGVBQWVELHlDQUFRLENBQUM7SUFDbkNHLE1BQU1ILHlDQUFRLEdBQUdLLEdBQUcsQ0FBQyxHQUFHLDJCQUEyQkMsR0FBRyxDQUFDLEtBQUs7SUFDNURDLFFBQVFQLHlDQUFRLEdBQUdTLFFBQVEsQ0FBQztJQUM1QkMsUUFBUVYsMENBQU0sQ0FBQztRQUFDO1FBQVU7UUFBVztLQUFTO0lBQzlDWSxhQUFhWix5Q0FBUSxHQUFHYSxJQUFJLENBQUMsNEJBQTRCQyxRQUFRO0lBQ2pFQyxZQUFZZix1Q0FBTTtJQUNsQmlCLFVBQVVqQix1Q0FBTSxHQUFHYyxRQUFRO0FBQzdCLEdBQUU7QUFFRixvREFBb0Q7QUFDN0MsTUFBTUksd0JBQXdCbEIseUNBQVEsQ0FBQztJQUM1Q0csTUFBTUgseUNBQVEsR0FBR0ssR0FBRyxDQUFDLEdBQUcsMkJBQTJCQyxHQUFHLENBQUMsS0FBSztJQUM1REMsUUFBUVAseUNBQVEsR0FBR0ssR0FBRyxDQUFDLEdBQUc7SUFDMUJLLFFBQVFWLDBDQUFNLENBQUM7UUFBQztRQUFVO1FBQVc7S0FBUztJQUM5Q1ksYUFBYVoseUNBQVEsR0FBR2MsUUFBUTtJQUNoQ0MsWUFBWWYsdUNBQU07SUFDbEJpQixVQUFVakIsd0NBQU8sQ0FBQztRQUFDQSx1Q0FBTTtRQUFJQSwwQ0FBTTtRQUFJQSw0Q0FBVztLQUFHLEVBQUVjLFFBQVE7QUFDakUsR0FBRTtBQUVGLDZDQUE2QztBQUN0QyxNQUFNUSxtQkFBbUJKLHNCQUFzQkssU0FBUyxDQUFDLENBQUNDLE9BQVU7UUFDekUsR0FBR0EsSUFBSTtRQUNQakIsUUFBUSxDQUFDO1lBQ1AsTUFBTWtCLE1BQU1DLFdBQVdGLEtBQUtqQixNQUFNLENBQUNvQixPQUFPLENBQUMsWUFBWTtZQUN2RCxJQUFJQyxNQUFNSCxRQUFRQSxPQUFPLEdBQUc7Z0JBQzFCLE1BQU0sSUFBSUksTUFBTTtZQUNsQjtZQUNBLE9BQU9KO1FBQ1Q7UUFDQWIsYUFBYVksS0FBS1osV0FBVyxJQUFJUztRQUNqQ0osVUFBVU8sS0FBS1AsUUFBUSxJQUFJSTtJQUM3QixJQUFHIiwic291cmNlcyI6WyIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL3BhY2thZ2VzL3NoYXJlZC9zcmMvc2NoZW1hcy9idWRnZXQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgeiB9IGZyb20gJ3pvZCdcblxuZXhwb3J0IGNvbnN0IGJ1ZGdldFNjaGVtYSA9IHoub2JqZWN0KHtcbiAgbmFtZTogei5zdHJpbmcoKS5taW4oMSwgJ0J1ZGdldCBuYW1lIGlzIHJlcXVpcmVkJykubWF4KDEwMCwgJ0J1ZGdldCBuYW1lIG11c3QgYmUgdW5kZXIgMTAwIGNoYXJhY3RlcnMnKSxcbiAgYW1vdW50OiB6Lm51bWJlcigpLnBvc2l0aXZlKCdCdWRnZXQgYW1vdW50IG11c3QgYmUgZ3JlYXRlciB0aGFuIDAnKSxcbiAgcGVyaW9kOiB6LmVudW0oWyd3ZWVrbHknLCAnbW9udGhseScsICd5ZWFybHknXSksXG4gIGNhdGVnb3J5X2lkOiB6LnN0cmluZygpLnV1aWQoJ1BsZWFzZSBzZWxlY3QgYSBjYXRlZ29yeScpLm9wdGlvbmFsKCksXG4gIHN0YXJ0X2RhdGU6IHouZGF0ZSgpLFxuICBlbmRfZGF0ZTogei5kYXRlKCkub3B0aW9uYWwoKSxcbn0pXG5cbi8vIEJhc2UgZm9ybSBzY2hlbWEgd2l0aG91dCB0cmFuc2Zvcm0gZm9yIGZvcm0gc3RhdGVcbmV4cG9ydCBjb25zdCBidWRnZXRGb3JtSW5wdXRTY2hlbWEgPSB6Lm9iamVjdCh7XG4gIG5hbWU6IHouc3RyaW5nKCkubWluKDEsICdCdWRnZXQgbmFtZSBpcyByZXF1aXJlZCcpLm1heCgxMDAsICdCdWRnZXQgbmFtZSBtdXN0IGJlIHVuZGVyIDEwMCBjaGFyYWN0ZXJzJyksXG4gIGFtb3VudDogei5zdHJpbmcoKS5taW4oMSwgJ0J1ZGdldCBhbW91bnQgaXMgcmVxdWlyZWQnKSxcbiAgcGVyaW9kOiB6LmVudW0oWyd3ZWVrbHknLCAnbW9udGhseScsICd5ZWFybHknXSksXG4gIGNhdGVnb3J5X2lkOiB6LnN0cmluZygpLm9wdGlvbmFsKCksXG4gIHN0YXJ0X2RhdGU6IHouZGF0ZSgpLFxuICBlbmRfZGF0ZTogei51bmlvbihbei5kYXRlKCksIHoubnVsbCgpLCB6LnVuZGVmaW5lZCgpXSkub3B0aW9uYWwoKSxcbn0pXG5cbi8vIFNjaGVtYSB3aXRoIHRyYW5zZm9ybSBmb3IgZmluYWwgdmFsaWRhdGlvblxuZXhwb3J0IGNvbnN0IGJ1ZGdldEZvcm1TY2hlbWEgPSBidWRnZXRGb3JtSW5wdXRTY2hlbWEudHJhbnNmb3JtKChkYXRhKSA9PiAoe1xuICAuLi5kYXRhLFxuICBhbW91bnQ6ICgoKSA9PiB7XG4gICAgY29uc3QgbnVtID0gcGFyc2VGbG9hdChkYXRhLmFtb3VudC5yZXBsYWNlKC9bXlxcZC4tXS9nLCAnJykpXG4gICAgaWYgKGlzTmFOKG51bSkgfHwgbnVtIDw9IDApIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignQnVkZ2V0IGFtb3VudCBtdXN0IGJlIGEgcG9zaXRpdmUgbnVtYmVyJylcbiAgICB9XG4gICAgcmV0dXJuIG51bVxuICB9KSgpLFxuICBjYXRlZ29yeV9pZDogZGF0YS5jYXRlZ29yeV9pZCB8fCB1bmRlZmluZWQsXG4gIGVuZF9kYXRlOiBkYXRhLmVuZF9kYXRlIHx8IHVuZGVmaW5lZCxcbn0pKVxuXG5leHBvcnQgdHlwZSBCdWRnZXRGb3JtSW5wdXREYXRhID0gei5pbmZlcjx0eXBlb2YgYnVkZ2V0Rm9ybUlucHV0U2NoZW1hPlxuZXhwb3J0IHR5cGUgQnVkZ2V0Rm9ybURhdGEgPSB6LmluZmVyPHR5cGVvZiBidWRnZXRGb3JtU2NoZW1hPlxuZXhwb3J0IHR5cGUgQnVkZ2V0RGF0YSA9IHouaW5mZXI8dHlwZW9mIGJ1ZGdldFNjaGVtYT4iXSwibmFtZXMiOlsieiIsImJ1ZGdldFNjaGVtYSIsIm9iamVjdCIsIm5hbWUiLCJzdHJpbmciLCJtaW4iLCJtYXgiLCJhbW91bnQiLCJudW1iZXIiLCJwb3NpdGl2ZSIsInBlcmlvZCIsImVudW0iLCJjYXRlZ29yeV9pZCIsInV1aWQiLCJvcHRpb25hbCIsInN0YXJ0X2RhdGUiLCJkYXRlIiwiZW5kX2RhdGUiLCJidWRnZXRGb3JtSW5wdXRTY2hlbWEiLCJ1bmlvbiIsIm51bGwiLCJ1bmRlZmluZWQiLCJidWRnZXRGb3JtU2NoZW1hIiwidHJhbnNmb3JtIiwiZGF0YSIsIm51bSIsInBhcnNlRmxvYXQiLCJyZXBsYWNlIiwiaXNOYU4iLCJFcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/schemas/budget.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/schemas/expense.ts":
/*!****************************************************!*\
  !*** ../../packages/shared/src/schemas/expense.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   expenseFormInputSchema: () => (/* binding */ expenseFormInputSchema),\n/* harmony export */   expenseFormSchema: () => (/* binding */ expenseFormSchema),\n/* harmony export */   expenseSchema: () => (/* binding */ expenseSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/zod/dist/esm/index.js\");\n\nconst expenseSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Amount must be greater than 0'),\n    category_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid('Please select a category'),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    transaction_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.date(),\n    transaction_type: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'income',\n        'expense'\n    ]),\n    attachments: zod__WEBPACK_IMPORTED_MODULE_0__.z.array(zod__WEBPACK_IMPORTED_MODULE_0__.z.any()).optional()\n});\n// Base form schema without transform for form state\nconst expenseFormInputSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Amount is required'),\n    category_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Please select a category'),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    transaction_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.date(),\n    transaction_type: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'income',\n        'expense'\n    ])\n});\n// Schema with transform for final validation\nconst expenseFormSchema = expenseFormInputSchema.transform((data)=>({\n        ...data,\n        amount: (()=>{\n            const num = parseFloat(data.amount.replace(/[^\\d.-]/g, ''));\n            if (isNaN(num) || num <= 0) {\n                throw new Error('Amount must be a positive number');\n            }\n            return num;\n        })()\n    }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/schemas/expense.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/stores/currencyStore.ts":
/*!*********************************************************!*\
  !*** ../../packages/shared/src/stores/currencyStore.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCurrencyStore: () => (/* binding */ useCurrencyStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/../../node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/../../node_modules/zustand/esm/middleware.mjs\");\n\n\n// Valid currency codes supported by the app\nconst VALID_CURRENCIES = [\n    'USD',\n    'EUR',\n    'GBP',\n    'INR',\n    'JPY',\n    'CAD',\n    'AUD',\n    'CHF',\n    'SEK',\n    'NOK',\n    'DKK',\n    'SGD',\n    'HKD',\n    'CNY',\n    'KRW',\n    'BRL',\n    'MXN',\n    'ARS',\n    'ZAR',\n    'RUB',\n    'AED',\n    'SAR',\n    'TRY',\n    'ILS',\n    'EGP',\n    'NGN',\n    'KES',\n    'THB',\n    'MYR',\n    'IDR',\n    'PHP',\n    'VND',\n    'BDT',\n    'PKR',\n    'LKR',\n    'NZD'\n];\nconst useCurrencyStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set, get)=>({\n        currency: 'USD',\n        setCurrency: (currency)=>{\n            // Validate currency code before setting\n            const validCurrency = VALID_CURRENCIES.includes(currency) ? currency : 'USD';\n            set({\n                currency: validCurrency\n            });\n        },\n        formatCurrency: (amount)=>{\n            const { currency } = get();\n            try {\n                // Ensure amount is a valid number\n                const validAmount = typeof amount === 'number' && !isNaN(amount) ? amount : 0;\n                return new Intl.NumberFormat('en-US', {\n                    style: 'currency',\n                    currency: currency,\n                    minimumFractionDigits: 2,\n                    maximumFractionDigits: 2\n                }).format(validAmount);\n            } catch (error) {\n                console.warn('Currency formatting error:', error);\n                // Fallback if currency is invalid\n                try {\n                    return new Intl.NumberFormat('en-US', {\n                        style: 'currency',\n                        currency: 'USD',\n                        minimumFractionDigits: 2,\n                        maximumFractionDigits: 2\n                    }).format(typeof amount === 'number' && !isNaN(amount) ? amount : 0);\n                } catch (fallbackError) {\n                    console.error('Fallback currency formatting failed:', fallbackError);\n                    return `$${(typeof amount === 'number' && !isNaN(amount) ? amount : 0).toFixed(2)}`;\n                }\n            }\n        }\n    }), {\n    name: 'currency-store'\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/stores/currencyStore.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/types.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/types.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n// Core types for the application\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/types.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/utils.ts":
/*!******************************************!*\
  !*** ../../packages/shared/src/utils.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateBudgetProgress: () => (/* binding */ calculateBudgetProgress),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate)\n/* harmony export */ });\n// Shared utility functions\nconst formatCurrency = (amount, currency = 'USD')=>{\n    return new Intl.NumberFormat('en-US', {\n        style: 'currency',\n        currency\n    }).format(amount);\n};\nconst formatDate = (date)=>{\n    const d = typeof date === 'string' ? new Date(date) : date;\n    return d.toLocaleDateString('en-US', {\n        year: 'numeric',\n        month: 'short',\n        day: 'numeric'\n    });\n};\nconst calculateBudgetProgress = (spent, budget)=>{\n    return Math.min(spent / budget * 100, 100);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vcGFja2FnZXMvc2hhcmVkL3NyYy91dGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQSwyQkFBMkI7QUFDcEIsTUFBTUEsaUJBQWlCLENBQzVCQyxRQUNBQyxXQUFtQixLQUFLO0lBRXhCLE9BQU8sSUFBSUMsS0FBS0MsWUFBWSxDQUFDLFNBQVM7UUFDcENDLE9BQU87UUFDUEg7SUFDRixHQUFHSSxNQUFNLENBQUNMO0FBQ1osRUFBRTtBQUVLLE1BQU1NLGFBQWEsQ0FBQ0M7SUFDekIsTUFBTUMsSUFBSSxPQUFPRCxTQUFTLFdBQVcsSUFBSUUsS0FBS0YsUUFBUUE7SUFDdEQsT0FBT0MsRUFBRUUsa0JBQWtCLENBQUMsU0FBUztRQUNuQ0MsTUFBTTtRQUNOQyxPQUFPO1FBQ1BDLEtBQUs7SUFDUDtBQUNGLEVBQUU7QUFFSyxNQUFNQywwQkFBMEIsQ0FDckNDLE9BQ0FDO0lBRUEsT0FBT0MsS0FBS0MsR0FBRyxDQUFDLFFBQVNGLFNBQVUsS0FBSztBQUMxQyxFQUFFIiwic291cmNlcyI6WyIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL3BhY2thZ2VzL3NoYXJlZC9zcmMvdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gU2hhcmVkIHV0aWxpdHkgZnVuY3Rpb25zXG5leHBvcnQgY29uc3QgZm9ybWF0Q3VycmVuY3kgPSAoXG4gIGFtb3VudDogbnVtYmVyLFxuICBjdXJyZW5jeTogc3RyaW5nID0gJ1VTRCdcbik6IHN0cmluZyA9PiB7XG4gIHJldHVybiBuZXcgSW50bC5OdW1iZXJGb3JtYXQoJ2VuLVVTJywge1xuICAgIHN0eWxlOiAnY3VycmVuY3knLFxuICAgIGN1cnJlbmN5LFxuICB9KS5mb3JtYXQoYW1vdW50KTtcbn07XG5cbmV4cG9ydCBjb25zdCBmb3JtYXREYXRlID0gKGRhdGU6IHN0cmluZyB8IERhdGUpOiBzdHJpbmcgPT4ge1xuICBjb25zdCBkID0gdHlwZW9mIGRhdGUgPT09ICdzdHJpbmcnID8gbmV3IERhdGUoZGF0ZSkgOiBkYXRlO1xuICByZXR1cm4gZC50b0xvY2FsZURhdGVTdHJpbmcoJ2VuLVVTJywge1xuICAgIHllYXI6ICdudW1lcmljJyxcbiAgICBtb250aDogJ3Nob3J0JyxcbiAgICBkYXk6ICdudW1lcmljJyxcbiAgfSk7XG59O1xuXG5leHBvcnQgY29uc3QgY2FsY3VsYXRlQnVkZ2V0UHJvZ3Jlc3MgPSAoXG4gIHNwZW50OiBudW1iZXIsXG4gIGJ1ZGdldDogbnVtYmVyXG4pOiBudW1iZXIgPT4ge1xuICByZXR1cm4gTWF0aC5taW4oKHNwZW50IC8gYnVkZ2V0KSAqIDEwMCwgMTAwKTtcbn07Il0sIm5hbWVzIjpbImZvcm1hdEN1cnJlbmN5IiwiYW1vdW50IiwiY3VycmVuY3kiLCJJbnRsIiwiTnVtYmVyRm9ybWF0Iiwic3R5bGUiLCJmb3JtYXQiLCJmb3JtYXREYXRlIiwiZGF0ZSIsImQiLCJEYXRlIiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwieWVhciIsIm1vbnRoIiwiZGF5IiwiY2FsY3VsYXRlQnVkZ2V0UHJvZ3Jlc3MiLCJzcGVudCIsImJ1ZGdldCIsIk1hdGgiLCJtaW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/utils.ts\n");

/***/ }),

/***/ "(ssr)/../../packages/shared/src/validators.ts":
/*!***********************************************!*\
  !*** ../../packages/shared/src/validators.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   categorySchema: () => (/* binding */ categorySchema),\n/* harmony export */   transactionSchema: () => (/* binding */ transactionSchema)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/../../node_modules/zod/dist/esm/index.js\");\n\n// Validation schemas using Zod\nconst transactionSchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    amount: zod__WEBPACK_IMPORTED_MODULE_0__.z.number().positive('Amount must be positive'),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().optional(),\n    category_id: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().uuid('Invalid category ID'),\n    transaction_type: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'income',\n        'expense'\n    ]),\n    transaction_date: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().or(zod__WEBPACK_IMPORTED_MODULE_0__.z.date())\n});\nconst categorySchema = zod__WEBPACK_IMPORTED_MODULE_0__.z.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Category name is required'),\n    icon: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().min(1, 'Icon is required'),\n    color: zod__WEBPACK_IMPORTED_MODULE_0__.z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),\n    type: zod__WEBPACK_IMPORTED_MODULE_0__.z[\"enum\"]([\n        'income',\n        'expense'\n    ])\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../packages/shared/src/validators.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJhdmludGglMkZ3b3Jrc3BhY2VzJTJGc3RhcnR1cCUyRnBvcnRmb2xpb190cmFja2VyJTJGYXBwcyUyRndlYiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmNsaWVudC1wYWdlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJhdmludGglMkZ3b3Jrc3BhY2VzJTJGc3RhcnR1cCUyRnBvcnRmb2xpb190cmFja2VyJTJGYXBwcyUyRndlYiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmNsaWVudC1zZWdtZW50LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJhdmludGglMkZ3b3Jrc3BhY2VzJTJGc3RhcnR1cCUyRnBvcnRmb2xpb190cmFja2VyJTJGYXBwcyUyRndlYiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJhdmludGglMkZ3b3Jrc3BhY2VzJTJGc3RhcnR1cCUyRnBvcnRmb2xpb190cmFja2VyJTJGYXBwcyUyRndlYiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmh0dHAtYWNjZXNzLWZhbGxiYWNrJTJGZXJyb3ItYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhcmF2aW50aCUyRndvcmtzcGFjZXMlMkZzdGFydHVwJTJGcG9ydGZvbGlvX3RyYWNrZXIlMkZhcHBzJTJGd2ViJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbGF5b3V0LXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFyYXZpbnRoJTJGd29ya3NwYWNlcyUyRnN0YXJ0dXAlMkZwb3J0Zm9saW9fdHJhY2tlciUyRmFwcHMlMkZ3ZWIlMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRmFzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJhdmludGglMkZ3b3Jrc3BhY2VzJTJGc3RhcnR1cCUyRnBvcnRmb2xpb190cmFja2VyJTJGYXBwcyUyRndlYiUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRm1ldGFkYXRhJTJGbWV0YWRhdGEtYm91bmRhcnkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhcmF2aW50aCUyRndvcmtzcGFjZXMlMkZzdGFydHVwJTJGcG9ydGZvbGlvX3RyYWNrZXIlMkZhcHBzJTJGd2ViJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb09BQTBKO0FBQzFKO0FBQ0EsME9BQTZKO0FBQzdKO0FBQ0EsME9BQTZKO0FBQzdKO0FBQ0Esb1JBQWtMO0FBQ2xMO0FBQ0Esd09BQTRKO0FBQzVKO0FBQ0EsNFBBQXNLO0FBQ3RLO0FBQ0Esa1FBQXlLO0FBQ3pLO0FBQ0Esc1FBQTJLIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL2FwcHMvd2ViL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hcmF2aW50aC93b3Jrc3BhY2VzL3N0YXJ0dXAvcG9ydGZvbGlvX3RyYWNrZXIvYXBwcy93ZWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9jbGllbnQtc2VnbWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9hcHBzL3dlYi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2Vycm9yLWJvdW5kYXJ5LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL2FwcHMvd2ViL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvaHR0cC1hY2Nlc3MtZmFsbGJhY2svZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hcmF2aW50aC93b3Jrc3BhY2VzL3N0YXJ0dXAvcG9ydGZvbGlvX3RyYWNrZXIvYXBwcy93ZWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9sYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL2FwcHMvd2ViL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbWV0YWRhdGEvYXN5bmMtbWV0YWRhdGEuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hcmF2aW50aC93b3Jrc3BhY2VzL3N0YXJ0dXAvcG9ydGZvbGlvX3RyYWNrZXIvYXBwcy93ZWIvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9tZXRhZGF0YS9tZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9hcHBzL3dlYi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL3JlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanNcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Fauth%2Fforgot-password%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Fauth%2Fforgot-password%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/auth/forgot-password/page.tsx */ \"(ssr)/./src/app/auth/forgot-password/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJhdmludGglMkZ3b3Jrc3BhY2VzJTJGc3RhcnR1cCUyRnBvcnRmb2xpb190cmFja2VyJTJGYXBwcyUyRndlYiUyRnNyYyUyRmFwcCUyRmF1dGglMkZmb3Jnb3QtcGFzc3dvcmQlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMExBQXdJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL2FwcHMvd2ViL3NyYy9hcHAvYXV0aC9mb3Jnb3QtcGFzc3dvcmQvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Fauth%2Fforgot-password%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(ssr)/./src/app/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYXJhdmludGglMkZ3b3Jrc3BhY2VzJTJGc3RhcnR1cCUyRnBvcnRmb2xpb190cmFja2VyJTJGYXBwcyUyRndlYiUyRnNyYyUyRmFwcCUyRmxheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9KQUFxSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9hcHBzL3dlYi9zcmMvYXBwL2xheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp%2Flayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/auth/forgot-password/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/auth/forgot-password/page.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ForgotPasswordPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/../../node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/../../node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @repo/shared */ \"(ssr)/../../packages/shared/src/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction ForgotPasswordPage() {\n    const { resetPassword } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { register, handleSubmit, formState: { errors } } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(_repo_shared__WEBPACK_IMPORTED_MODULE_5__.resetPasswordSchema)\n    });\n    const onSubmit = async (data)=>{\n        try {\n            setIsLoading(true);\n            setError(null);\n            const { error } = await resetPassword(data.email);\n            if (error) {\n                setError(error.message);\n            } else {\n                setSuccess(true);\n            }\n        } catch (err) {\n            setError('An unexpected error occurred');\n            console.error('Reset password error:', err);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    if (success) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Check your email\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-md bg-green-50 p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-green-700\",\n                        children: \"We've sent a password reset link to your email address. Please check your inbox and follow the instructions to reset your password.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                        href: \"/auth/signin\",\n                        className: \"font-medium text-indigo-600 hover:text-indigo-500\",\n                        children: \"Back to sign in\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n            lineNumber: 46,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"Reset your password\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-sm text-gray-600\",\n                        children: \"Enter your email address and we'll send you a link to reset your password.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                className: \"space-y-6\",\n                onSubmit: handleSubmit(onSubmit),\n                children: [\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"rounded-md bg-red-50 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-red-700\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                htmlFor: \"email\",\n                                className: \"block text-sm font-medium text-gray-700\",\n                                children: \"Email address\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        ...register('email'),\n                                        type: \"email\",\n                                        autoComplete: \"email\",\n                                        className: \"appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-red-600\",\n                                        children: errors.email.message\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            className: \"group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isLoading ? 'Sending...' : 'Send reset link'\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/auth/signin\",\n                            className: \"font-medium text-indigo-600 hover:text-indigo-500\",\n                            children: \"Back to sign in\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/auth/forgot-password/page.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2F1dGgvZm9yZ290LXBhc3N3b3JkL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUVnQztBQUNKO0FBQ2E7QUFDWTtBQUNMO0FBQzhCO0FBRS9ELFNBQVNNO0lBQ3RCLE1BQU0sRUFBRUMsYUFBYSxFQUFFLEdBQUdILDhEQUFPQTtJQUNqQyxNQUFNLENBQUNJLFdBQVdDLGFBQWEsR0FBR1QsK0NBQVFBLENBQUM7SUFDM0MsTUFBTSxDQUFDVSxPQUFPQyxTQUFTLEdBQUdYLCtDQUFRQSxDQUFnQjtJQUNsRCxNQUFNLENBQUNZLFNBQVNDLFdBQVcsR0FBR2IsK0NBQVFBLENBQUM7SUFFdkMsTUFBTSxFQUNKYyxRQUFRLEVBQ1JDLFlBQVksRUFDWkMsV0FBVyxFQUFFQyxNQUFNLEVBQUUsRUFDdEIsR0FBR2Ysd0RBQU9BLENBQXdCO1FBQ2pDZ0IsVUFBVWYsb0VBQVdBLENBQUNFLDZEQUFtQkE7SUFDM0M7SUFFQSxNQUFNYyxXQUFXLE9BQU9DO1FBQ3RCLElBQUk7WUFDRlgsYUFBYTtZQUNiRSxTQUFTO1lBRVQsTUFBTSxFQUFFRCxLQUFLLEVBQUUsR0FBRyxNQUFNSCxjQUFjYSxLQUFLQyxLQUFLO1lBRWhELElBQUlYLE9BQU87Z0JBQ1RDLFNBQVNELE1BQU1ZLE9BQU87WUFDeEIsT0FBTztnQkFDTFQsV0FBVztZQUNiO1FBQ0YsRUFBRSxPQUFPVSxLQUFjO1lBQ3JCWixTQUFTO1lBQ1RhLFFBQVFkLEtBQUssQ0FBQyx5QkFBeUJhO1FBQ3pDLFNBQVU7WUFDUmQsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxJQUFJRyxTQUFTO1FBQ1gscUJBQ0UsOERBQUNhO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDRDs4QkFDQyw0RUFBQ0U7d0JBQUdELFdBQVU7a0NBQW1DOzs7Ozs7Ozs7Ozs4QkFFbkQsOERBQUNEO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDRDt3QkFBSUMsV0FBVTtrQ0FBeUI7Ozs7Ozs7Ozs7OzhCQUkxQyw4REFBQ0Q7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUN6QixrREFBSUE7d0JBQUMyQixNQUFLO3dCQUFlRixXQUFVO2tDQUFvRDs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFNaEc7SUFFQSxxQkFDRSw4REFBQ0Q7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNEOztrQ0FDQyw4REFBQ0U7d0JBQUdELFdBQVU7a0NBQW1DOzs7Ozs7a0NBQ2pELDhEQUFDRzt3QkFBRUgsV0FBVTtrQ0FBNkI7Ozs7Ozs7Ozs7OzswQkFLNUMsOERBQUNJO2dCQUFLSixXQUFVO2dCQUFZUCxVQUFVSixhQUFhSTs7b0JBQ2hEVCx1QkFDQyw4REFBQ2U7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNEOzRCQUFJQyxXQUFVO3NDQUF3QmhCOzs7Ozs7Ozs7OztrQ0FJM0MsOERBQUNlOzswQ0FDQyw4REFBQ007Z0NBQU1DLFNBQVE7Z0NBQVFOLFdBQVU7MENBQTBDOzs7Ozs7MENBRzNFLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNPO3dDQUNFLEdBQUduQixTQUFTLFFBQVE7d0NBQ3JCb0IsTUFBSzt3Q0FDTEMsY0FBYTt3Q0FDYlQsV0FBVTs7Ozs7O29DQUVYVCxPQUFPSSxLQUFLLGtCQUNYLDhEQUFDUTt3Q0FBRUgsV0FBVTtrREFBNkJULE9BQU9JLEtBQUssQ0FBQ0MsT0FBTzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtwRSw4REFBQ0c7a0NBQ0MsNEVBQUNXOzRCQUNDRixNQUFLOzRCQUNMRyxVQUFVN0I7NEJBQ1ZrQixXQUFVO3NDQUVUbEIsWUFBWSxlQUFlOzs7Ozs7Ozs7OztrQ0FJaEMsOERBQUNpQjt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ3pCLGtEQUFJQTs0QkFBQzJCLE1BQUs7NEJBQWVGLFdBQVU7c0NBQW9EOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9sRyIsInNvdXJjZXMiOlsiL1VzZXJzL2FyYXZpbnRoL3dvcmtzcGFjZXMvc3RhcnR1cC9wb3J0Zm9saW9fdHJhY2tlci9hcHBzL3dlYi9zcmMvYXBwL2F1dGgvZm9yZ290LXBhc3N3b3JkL3BhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuaW1wb3J0IHsgdXNlRm9ybSB9IGZyb20gJ3JlYWN0LWhvb2stZm9ybSdcbmltcG9ydCB7IHpvZFJlc29sdmVyIH0gZnJvbSAnQGhvb2tmb3JtL3Jlc29sdmVycy96b2QnXG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCdcbmltcG9ydCB7IHJlc2V0UGFzc3dvcmRTY2hlbWEsIHR5cGUgUmVzZXRQYXNzd29yZEZvcm1EYXRhIH0gZnJvbSAnQHJlcG8vc2hhcmVkJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBGb3Jnb3RQYXNzd29yZFBhZ2UoKSB7XG4gIGNvbnN0IHsgcmVzZXRQYXNzd29yZCB9ID0gdXNlQXV0aCgpXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKVxuICBjb25zdCBbc3VjY2Vzcywgc2V0U3VjY2Vzc10gPSB1c2VTdGF0ZShmYWxzZSlcblxuICBjb25zdCB7XG4gICAgcmVnaXN0ZXIsXG4gICAgaGFuZGxlU3VibWl0LFxuICAgIGZvcm1TdGF0ZTogeyBlcnJvcnMgfSxcbiAgfSA9IHVzZUZvcm08UmVzZXRQYXNzd29yZEZvcm1EYXRhPih7XG4gICAgcmVzb2x2ZXI6IHpvZFJlc29sdmVyKHJlc2V0UGFzc3dvcmRTY2hlbWEpLFxuICB9KVxuXG4gIGNvbnN0IG9uU3VibWl0ID0gYXN5bmMgKGRhdGE6IFJlc2V0UGFzc3dvcmRGb3JtRGF0YSkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRJc0xvYWRpbmcodHJ1ZSlcbiAgICAgIHNldEVycm9yKG51bGwpXG4gICAgICBcbiAgICAgIGNvbnN0IHsgZXJyb3IgfSA9IGF3YWl0IHJlc2V0UGFzc3dvcmQoZGF0YS5lbWFpbClcbiAgICAgIFxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIHNldEVycm9yKGVycm9yLm1lc3NhZ2UpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRTdWNjZXNzKHRydWUpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyOiB1bmtub3duKSB7XG4gICAgICBzZXRFcnJvcignQW4gdW5leHBlY3RlZCBlcnJvciBvY2N1cnJlZCcpXG4gICAgICBjb25zb2xlLmVycm9yKCdSZXNldCBwYXNzd29yZCBlcnJvcjonLCBlcnIpXG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBpZiAoc3VjY2Vzcykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPkNoZWNrIHlvdXIgZW1haWw8L2gyPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyb3VuZGVkLW1kIGJnLWdyZWVuLTUwIHAtNFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyZWVuLTcwMFwiPlxuICAgICAgICAgICAgV2UmYXBvczt2ZSBzZW50IGEgcGFzc3dvcmQgcmVzZXQgbGluayB0byB5b3VyIGVtYWlsIGFkZHJlc3MuIFBsZWFzZSBjaGVjayB5b3VyIGluYm94IGFuZCBmb2xsb3cgdGhlIGluc3RydWN0aW9ucyB0byByZXNldCB5b3VyIHBhc3N3b3JkLlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYXV0aC9zaWduaW5cIiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWluZGlnby02MDAgaG92ZXI6dGV4dC1pbmRpZ28tNTAwXCI+XG4gICAgICAgICAgICBCYWNrIHRvIHNpZ24gaW5cbiAgICAgICAgICA8L0xpbms+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgPGRpdj5cbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwXCI+UmVzZXQgeW91ciBwYXNzd29yZDwvaDI+XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cIm10LTIgdGV4dC1zbSB0ZXh0LWdyYXktNjAwXCI+XG4gICAgICAgICAgRW50ZXIgeW91ciBlbWFpbCBhZGRyZXNzIGFuZCB3ZSZhcG9zO2xsIHNlbmQgeW91IGEgbGluayB0byByZXNldCB5b3VyIHBhc3N3b3JkLlxuICAgICAgICA8L3A+XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGZvcm0gY2xhc3NOYW1lPVwic3BhY2UteS02XCIgb25TdWJtaXQ9e2hhbmRsZVN1Ym1pdChvblN1Ym1pdCl9PlxuICAgICAgICB7ZXJyb3IgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicm91bmRlZC1tZCBiZy1yZWQtNTAgcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtc20gdGV4dC1yZWQtNzAwXCI+e2Vycm9yfTwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGxhYmVsIGh0bWxGb3I9XCJlbWFpbFwiIGNsYXNzTmFtZT1cImJsb2NrIHRleHQtc20gZm9udC1tZWRpdW0gdGV4dC1ncmF5LTcwMFwiPlxuICAgICAgICAgICAgRW1haWwgYWRkcmVzc1xuICAgICAgICAgIDwvbGFiZWw+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0xXCI+XG4gICAgICAgICAgICA8aW5wdXRcbiAgICAgICAgICAgICAgey4uLnJlZ2lzdGVyKCdlbWFpbCcpfVxuICAgICAgICAgICAgICB0eXBlPVwiZW1haWxcIlxuICAgICAgICAgICAgICBhdXRvQ29tcGxldGU9XCJlbWFpbFwiXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImFwcGVhcmFuY2Utbm9uZSBibG9jayB3LWZ1bGwgcHgtMyBweS0yIGJvcmRlciBib3JkZXItZ3JheS0zMDAgcm91bmRlZC1tZCBwbGFjZWhvbGRlci1ncmF5LTQwMCBmb2N1czpvdXRsaW5lLW5vbmUgZm9jdXM6cmluZy1pbmRpZ28tNTAwIGZvY3VzOmJvcmRlci1pbmRpZ28tNTAwIHNtOnRleHQtc21cIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIHtlcnJvcnMuZW1haWwgJiYgKFxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0yIHRleHQtc20gdGV4dC1yZWQtNjAwXCI+e2Vycm9ycy5lbWFpbC5tZXNzYWdlfTwvcD5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgdHlwZT1cInN1Ym1pdFwiXG4gICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgcmVsYXRpdmUgdy1mdWxsIGZsZXgganVzdGlmeS1jZW50ZXIgcHktMiBweC00IGJvcmRlciBib3JkZXItdHJhbnNwYXJlbnQgdGV4dC1zbSBmb250LW1lZGl1bSByb3VuZGVkLW1kIHRleHQtd2hpdGUgYmctaW5kaWdvLTYwMCBob3ZlcjpiZy1pbmRpZ28tNzAwIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1vZmZzZXQtMiBmb2N1czpyaW5nLWluZGlnby01MDAgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxuICAgICAgICAgID5cbiAgICAgICAgICAgIHtpc0xvYWRpbmcgPyAnU2VuZGluZy4uLicgOiAnU2VuZCByZXNldCBsaW5rJ31cbiAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvYXV0aC9zaWduaW5cIiBjbGFzc05hbWU9XCJmb250LW1lZGl1bSB0ZXh0LWluZGlnby02MDAgaG92ZXI6dGV4dC1pbmRpZ28tNTAwXCI+XG4gICAgICAgICAgICBCYWNrIHRvIHNpZ24gaW5cbiAgICAgICAgICA8L0xpbms+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9mb3JtPlxuICAgIDwvZGl2PlxuICApXG59Il0sIm5hbWVzIjpbInVzZVN0YXRlIiwiTGluayIsInVzZUZvcm0iLCJ6b2RSZXNvbHZlciIsInVzZUF1dGgiLCJyZXNldFBhc3N3b3JkU2NoZW1hIiwiRm9yZ290UGFzc3dvcmRQYWdlIiwicmVzZXRQYXNzd29yZCIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJzdWNjZXNzIiwic2V0U3VjY2VzcyIsInJlZ2lzdGVyIiwiaGFuZGxlU3VibWl0IiwiZm9ybVN0YXRlIiwiZXJyb3JzIiwicmVzb2x2ZXIiLCJvblN1Ym1pdCIsImRhdGEiLCJlbWFpbCIsIm1lc3NhZ2UiLCJlcnIiLCJjb25zb2xlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDIiLCJocmVmIiwicCIsImZvcm0iLCJsYWJlbCIsImh0bWxGb3IiLCJpbnB1dCIsInR5cGUiLCJhdXRvQ29tcGxldGUiLCJidXR0b24iLCJkaXNhYmxlZCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/auth/forgot-password/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0c0f88d439f9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYXJhdmludGgvd29ya3NwYWNlcy9zdGFydHVwL3BvcnRmb2xpb190cmFja2VyL2FwcHMvd2ViL3NyYy9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwYzBmODhkNDM5ZjlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/globals.css\n");

/***/ }),

/***/ "(ssr)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist\",\"arguments\":[{\"variable\":\"--font-inter\",\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-inter\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"variable\":\"--font-geist-mono\",\"subsets\":[\"latin\"]}],\"variableName\":\"geistMono\"} */ \"(ssr)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../contexts/AuthContext */ \"(ssr)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../contexts/ThemeContext */ \"(ssr)/./src/contexts/ThemeContext.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/../../node_modules/react-hot-toast/dist/index.mjs\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./globals.css */ \"(ssr)/./src/app/globals.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_arguments_variable_font_inter_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().variable)} ${(next_font_google_target_css_path_src_app_layout_tsx_import_Geist_Mono_arguments_variable_font_geist_mono_subsets_latin_variableName_geistMono___WEBPACK_IMPORTED_MODULE_6___default().variable)} font-sans antialiased bg-background text-text-primary`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_ThemeContext__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 4000,\n                                style: {\n                                    background: 'var(--surface-elevated)',\n                                    color: 'var(--text-primary)',\n                                    border: '1px solid var(--border)',\n                                    borderRadius: 'var(--radius-lg)',\n                                    boxShadow: 'var(--shadow-lg)'\n                                },\n                                success: {\n                                    iconTheme: {\n                                        primary: 'var(--success-green)',\n                                        secondary: 'white'\n                                    }\n                                },\n                                error: {\n                                    iconTheme: {\n                                        primary: 'var(--error-red)',\n                                        secondary: 'white'\n                                    }\n                                }\n                            }\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/app/layout.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBU01BO0FBS0FDO0FBVmlEO0FBQ0U7QUFDZjtBQUNuQjtBQVlSLFNBQVNJLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUNDQyxXQUFXLEdBQUdWLGtMQUFjLENBQUMsQ0FBQyxFQUFFQyxnTUFBa0IsQ0FBQyxzREFBc0QsQ0FBQztzQkFFMUcsNEVBQUNFLGlFQUFhQTswQkFDWiw0RUFBQ0QsK0RBQVlBOzt3QkFDVkk7c0NBQ0QsOERBQUNGLG9EQUFPQTs0QkFDTlEsVUFBUzs0QkFDVEMsY0FBYztnQ0FDWkMsVUFBVTtnQ0FDVkMsT0FBTztvQ0FDTEMsWUFBWTtvQ0FDWkMsT0FBTztvQ0FDUEMsUUFBUTtvQ0FDUkMsY0FBYztvQ0FDZEMsV0FBVztnQ0FDYjtnQ0FDQUMsU0FBUztvQ0FDUEMsV0FBVzt3Q0FDVEMsU0FBUzt3Q0FDVEMsV0FBVztvQ0FDYjtnQ0FDRjtnQ0FDQUMsT0FBTztvQ0FDTEgsV0FBVzt3Q0FDVEMsU0FBUzt3Q0FDVEMsV0FBVztvQ0FDYjtnQ0FDRjs0QkFDRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT2QiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hcmF2aW50aC93b3Jrc3BhY2VzL3N0YXJ0dXAvcG9ydGZvbGlvX3RyYWNrZXIvYXBwcy93ZWIvc3JjL2FwcC9sYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuXG4vLyBpbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSBcIm5leHRcIjtcbmltcG9ydCB7IEdlaXN0LCBHZWlzdF9Nb25vIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCB7IEF1dGhQcm92aWRlciB9IGZyb20gJy4uL2NvbnRleHRzL0F1dGhDb250ZXh0JztcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tICcuLi9jb250ZXh0cy9UaGVtZUNvbnRleHQnO1xuaW1wb3J0IHsgVG9hc3RlciB9IGZyb20gJ3JlYWN0LWhvdC10b2FzdCc7XG5pbXBvcnQgXCIuL2dsb2JhbHMuY3NzXCI7XG5cbmNvbnN0IGludGVyID0gR2Vpc3Qoe1xuICB2YXJpYWJsZTogXCItLWZvbnQtaW50ZXJcIixcbiAgc3Vic2V0czogW1wibGF0aW5cIl0sXG59KTtcblxuY29uc3QgZ2Vpc3RNb25vID0gR2Vpc3RfTW9ubyh7XG4gIHZhcmlhYmxlOiBcIi0tZm9udC1nZWlzdC1tb25vXCIsXG4gIHN1YnNldHM6IFtcImxhdGluXCJdLFxufSk7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IFJlYWRvbmx5PHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZTtcbn0+KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keVxuICAgICAgICBjbGFzc05hbWU9e2Ake2ludGVyLnZhcmlhYmxlfSAke2dlaXN0TW9uby52YXJpYWJsZX0gZm9udC1zYW5zIGFudGlhbGlhc2VkIGJnLWJhY2tncm91bmQgdGV4dC10ZXh0LXByaW1hcnlgfVxuICAgICAgPlxuICAgICAgICA8VGhlbWVQcm92aWRlcj5cbiAgICAgICAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgICAgICAge2NoaWxkcmVufVxuICAgICAgICAgICAgPFRvYXN0ZXIgXG4gICAgICAgICAgICAgIHBvc2l0aW9uPVwidG9wLXJpZ2h0XCJcbiAgICAgICAgICAgICAgdG9hc3RPcHRpb25zPXt7XG4gICAgICAgICAgICAgICAgZHVyYXRpb246IDQwMDAsXG4gICAgICAgICAgICAgICAgc3R5bGU6IHtcbiAgICAgICAgICAgICAgICAgIGJhY2tncm91bmQ6ICd2YXIoLS1zdXJmYWNlLWVsZXZhdGVkKScsXG4gICAgICAgICAgICAgICAgICBjb2xvcjogJ3ZhcigtLXRleHQtcHJpbWFyeSknLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyOiAnMXB4IHNvbGlkIHZhcigtLWJvcmRlciknLFxuICAgICAgICAgICAgICAgICAgYm9yZGVyUmFkaXVzOiAndmFyKC0tcmFkaXVzLWxnKScsXG4gICAgICAgICAgICAgICAgICBib3hTaGFkb3c6ICd2YXIoLS1zaGFkb3ctbGcpJyxcbiAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIHN1Y2Nlc3M6IHtcbiAgICAgICAgICAgICAgICAgIGljb25UaGVtZToge1xuICAgICAgICAgICAgICAgICAgICBwcmltYXJ5OiAndmFyKC0tc3VjY2Vzcy1ncmVlbiknLFxuICAgICAgICAgICAgICAgICAgICBzZWNvbmRhcnk6ICd3aGl0ZScsXG4gICAgICAgICAgICAgICAgICB9LFxuICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgZXJyb3I6IHtcbiAgICAgICAgICAgICAgICAgIGljb25UaGVtZToge1xuICAgICAgICAgICAgICAgICAgICBwcmltYXJ5OiAndmFyKC0tZXJyb3ItcmVkKScsXG4gICAgICAgICAgICAgICAgICAgIHNlY29uZGFyeTogJ3doaXRlJyxcbiAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgfX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9BdXRoUHJvdmlkZXI+XG4gICAgICAgIDwvVGhlbWVQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiaW50ZXIiLCJnZWlzdE1vbm8iLCJBdXRoUHJvdmlkZXIiLCJUaGVtZVByb3ZpZGVyIiwiVG9hc3RlciIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSIsInZhcmlhYmxlIiwicG9zaXRpb24iLCJ0b2FzdE9wdGlvbnMiLCJkdXJhdGlvbiIsInN0eWxlIiwiYmFja2dyb3VuZCIsImNvbG9yIiwiYm9yZGVyIiwiYm9yZGVyUmFkaXVzIiwiYm94U2hhZG93Iiwic3VjY2VzcyIsImljb25UaGVtZSIsInByaW1hcnkiLCJzZWNvbmRhcnkiLCJlcnJvciJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/AuthContext.tsx":
/*!**************************************!*\
  !*** ./src/contexts/AuthContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _repo_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @repo/shared */ \"(ssr)/../../packages/shared/src/index.ts\");\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AuthProvider.useEffect\": ()=>{\n            // Get initial session\n            _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.getSession().then({\n                \"AuthProvider.useEffect\": ({ data: { session } })=>{\n                    setSession(session);\n                    setUser(session?.user ? {\n                        id: session.user.id,\n                        email: session.user.email,\n                        user_metadata: session.user.user_metadata\n                    } : null);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            // Listen for auth changes\n            const { data: { subscription } } = _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.onAuthStateChange({\n                \"AuthProvider.useEffect\": async (event, session)=>{\n                    setSession(session);\n                    setUser(session?.user ? {\n                        id: session.user.id,\n                        email: session.user.email,\n                        user_metadata: session.user.user_metadata\n                    } : null);\n                    setLoading(false);\n                }\n            }[\"AuthProvider.useEffect\"]);\n            return ({\n                \"AuthProvider.useEffect\": ()=>subscription.unsubscribe()\n            })[\"AuthProvider.useEffect\"];\n        }\n    }[\"AuthProvider.useEffect\"], []);\n    const signIn = async (email, password)=>{\n        const { error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        return {\n            error\n        };\n    };\n    const signUp = async (email, password, name)=>{\n        const { error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    name\n                }\n            }\n        });\n        return {\n            error\n        };\n    };\n    const signOut = async ()=>{\n        const { error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.signOut();\n        return {\n            error\n        };\n    };\n    const resetPassword = async (email)=>{\n        const { error } = await _repo_shared__WEBPACK_IMPORTED_MODULE_2__.supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/auth/reset-password`\n        });\n        return {\n            error\n        };\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        resetPassword\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/contexts/AuthContext.tsx\",\n        lineNumber: 103,\n        columnNumber: 10\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error('useAuth must be used within an AuthProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/AuthContext.tsx\n");

/***/ }),

/***/ "(ssr)/./src/contexts/ThemeContext.tsx":
/*!***************************************!*\
  !*** ./src/contexts/ThemeContext.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider),\n/* harmony export */   useTheme: () => (/* binding */ useTheme)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ThemeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction ThemeProvider({ children }) {\n    const [theme, setThemeState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('system');\n    const [resolvedTheme, setResolvedTheme] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('light');\n    // Load theme from localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const savedTheme = localStorage.getItem('theme');\n            if (savedTheme && [\n                'light',\n                'dark',\n                'system'\n            ].includes(savedTheme)) {\n                setThemeState(savedTheme);\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], []);\n    // Update resolved theme based on theme preference and system preference\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const updateResolvedTheme = {\n                \"ThemeProvider.useEffect.updateResolvedTheme\": ()=>{\n                    if (theme === 'system') {\n                        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n                        setResolvedTheme(systemPrefersDark ? 'dark' : 'light');\n                    } else {\n                        setResolvedTheme(theme);\n                    }\n                }\n            }[\"ThemeProvider.useEffect.updateResolvedTheme\"];\n            updateResolvedTheme();\n            // Listen for system theme changes\n            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            const handleChange = {\n                \"ThemeProvider.useEffect.handleChange\": ()=>{\n                    if (theme === 'system') {\n                        updateResolvedTheme();\n                    }\n                }\n            }[\"ThemeProvider.useEffect.handleChange\"];\n            mediaQuery.addEventListener('change', handleChange);\n            return ({\n                \"ThemeProvider.useEffect\": ()=>mediaQuery.removeEventListener('change', handleChange)\n            })[\"ThemeProvider.useEffect\"];\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        theme\n    ]);\n    // Apply theme to document\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ThemeProvider.useEffect\": ()=>{\n            const root = document.documentElement;\n            if (resolvedTheme === 'dark') {\n                root.classList.add('dark');\n                root.setAttribute('data-theme', 'dark');\n            } else {\n                root.classList.remove('dark');\n                root.setAttribute('data-theme', 'light');\n            }\n        }\n    }[\"ThemeProvider.useEffect\"], [\n        resolvedTheme\n    ]);\n    const setTheme = (newTheme)=>{\n        setThemeState(newTheme);\n        localStorage.setItem('theme', newTheme);\n    };\n    const toggleTheme = ()=>{\n        if (theme === 'light') {\n            setTheme('dark');\n        } else if (theme === 'dark') {\n            setTheme('system');\n        } else {\n            setTheme('light');\n        }\n    };\n    const value = {\n        theme,\n        resolvedTheme,\n        setTheme,\n        toggleTheme\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ThemeContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/workspaces/startup/portfolio_tracker/apps/web/src/contexts/ThemeContext.tsx\",\n        lineNumber: 89,\n        columnNumber: 10\n    }, this);\n}\nfunction useTheme() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n    if (context === undefined) {\n        throw new Error('useTheme must be used within a ThemeProvider');\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29udGV4dHMvVGhlbWVDb250ZXh0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTZFO0FBVzdFLE1BQU1LLDZCQUFlSixvREFBYUEsQ0FBK0JLO0FBTTFELFNBQVNDLGNBQWMsRUFBRUMsUUFBUSxFQUFzQjtJQUM1RCxNQUFNLENBQUNDLE9BQU9DLGNBQWMsR0FBR04sK0NBQVFBLENBQVE7SUFDL0MsTUFBTSxDQUFDTyxlQUFlQyxpQkFBaUIsR0FBR1IsK0NBQVFBLENBQW1CO0lBRXJFLHdDQUF3QztJQUN4Q0QsZ0RBQVNBO21DQUFDO1lBQ1IsTUFBTVUsYUFBYUMsYUFBYUMsT0FBTyxDQUFDO1lBQ3hDLElBQUlGLGNBQWM7Z0JBQUM7Z0JBQVM7Z0JBQVE7YUFBUyxDQUFDRyxRQUFRLENBQUNILGFBQWE7Z0JBQ2xFSCxjQUFjRztZQUNoQjtRQUNGO2tDQUFHLEVBQUU7SUFFTCx3RUFBd0U7SUFDeEVWLGdEQUFTQTttQ0FBQztZQUNSLE1BQU1jOytEQUFzQjtvQkFDMUIsSUFBSVIsVUFBVSxVQUFVO3dCQUN0QixNQUFNUyxvQkFBb0JDLE9BQU9DLFVBQVUsQ0FBQyxnQ0FBZ0NDLE9BQU87d0JBQ25GVCxpQkFBaUJNLG9CQUFvQixTQUFTO29CQUNoRCxPQUFPO3dCQUNMTixpQkFBaUJIO29CQUNuQjtnQkFDRjs7WUFFQVE7WUFFQSxrQ0FBa0M7WUFDbEMsTUFBTUssYUFBYUgsT0FBT0MsVUFBVSxDQUFDO1lBQ3JDLE1BQU1HO3dEQUFlO29CQUNuQixJQUFJZCxVQUFVLFVBQVU7d0JBQ3RCUTtvQkFDRjtnQkFDRjs7WUFFQUssV0FBV0UsZ0JBQWdCLENBQUMsVUFBVUQ7WUFDdEM7MkNBQU8sSUFBTUQsV0FBV0csbUJBQW1CLENBQUMsVUFBVUY7O1FBQ3hEO2tDQUFHO1FBQUNkO0tBQU07SUFFViwwQkFBMEI7SUFDMUJOLGdEQUFTQTttQ0FBQztZQUNSLE1BQU11QixPQUFPQyxTQUFTQyxlQUFlO1lBQ3JDLElBQUlqQixrQkFBa0IsUUFBUTtnQkFDNUJlLEtBQUtHLFNBQVMsQ0FBQ0MsR0FBRyxDQUFDO2dCQUNuQkosS0FBS0ssWUFBWSxDQUFDLGNBQWM7WUFDbEMsT0FBTztnQkFDTEwsS0FBS0csU0FBUyxDQUFDRyxNQUFNLENBQUM7Z0JBQ3RCTixLQUFLSyxZQUFZLENBQUMsY0FBYztZQUNsQztRQUNGO2tDQUFHO1FBQUNwQjtLQUFjO0lBRWxCLE1BQU1zQixXQUFXLENBQUNDO1FBQ2hCeEIsY0FBY3dCO1FBQ2RwQixhQUFhcUIsT0FBTyxDQUFDLFNBQVNEO0lBQ2hDO0lBRUEsTUFBTUUsY0FBYztRQUNsQixJQUFJM0IsVUFBVSxTQUFTO1lBQ3JCd0IsU0FBUztRQUNYLE9BQU8sSUFBSXhCLFVBQVUsUUFBUTtZQUMzQndCLFNBQVM7UUFDWCxPQUFPO1lBQ0xBLFNBQVM7UUFDWDtJQUNGO0lBRUEsTUFBTUksUUFBMEI7UUFDOUI1QjtRQUNBRTtRQUNBc0I7UUFDQUc7SUFDRjtJQUVBLHFCQUFPLDhEQUFDL0IsYUFBYWlDLFFBQVE7UUFBQ0QsT0FBT0E7a0JBQVE3Qjs7Ozs7O0FBQy9DO0FBRU8sU0FBUytCO0lBQ2QsTUFBTUMsVUFBVXRDLGlEQUFVQSxDQUFDRztJQUMzQixJQUFJbUMsWUFBWWxDLFdBQVc7UUFDekIsTUFBTSxJQUFJbUMsTUFBTTtJQUNsQjtJQUNBLE9BQU9EO0FBQ1QiLCJzb3VyY2VzIjpbIi9Vc2Vycy9hcmF2aW50aC93b3Jrc3BhY2VzL3N0YXJ0dXAvcG9ydGZvbGlvX3RyYWNrZXIvYXBwcy93ZWIvc3JjL2NvbnRleHRzL1RoZW1lQ29udGV4dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IGNyZWF0ZUNvbnRleHQsIHVzZUNvbnRleHQsIHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcblxuZXhwb3J0IHR5cGUgVGhlbWUgPSAnbGlnaHQnIHwgJ2RhcmsnIHwgJ3N5c3RlbSdcblxuaW50ZXJmYWNlIFRoZW1lQ29udGV4dFR5cGUge1xuICB0aGVtZTogVGhlbWVcbiAgcmVzb2x2ZWRUaGVtZTogJ2xpZ2h0JyB8ICdkYXJrJ1xuICBzZXRUaGVtZTogKHRoZW1lOiBUaGVtZSkgPT4gdm9pZFxuICB0b2dnbGVUaGVtZTogKCkgPT4gdm9pZFxufVxuXG5jb25zdCBUaGVtZUNvbnRleHQgPSBjcmVhdGVDb250ZXh0PFRoZW1lQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZClcblxuZXhwb3J0IGludGVyZmFjZSBUaGVtZVByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBUaGVtZVByb3ZpZGVyKHsgY2hpbGRyZW4gfTogVGhlbWVQcm92aWRlclByb3BzKSB7XG4gIGNvbnN0IFt0aGVtZSwgc2V0VGhlbWVTdGF0ZV0gPSB1c2VTdGF0ZTxUaGVtZT4oJ3N5c3RlbScpXG4gIGNvbnN0IFtyZXNvbHZlZFRoZW1lLCBzZXRSZXNvbHZlZFRoZW1lXSA9IHVzZVN0YXRlPCdsaWdodCcgfCAnZGFyayc+KCdsaWdodCcpXG5cbiAgLy8gTG9hZCB0aGVtZSBmcm9tIGxvY2FsU3RvcmFnZSBvbiBtb3VudFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHNhdmVkVGhlbWUgPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndGhlbWUnKSBhcyBUaGVtZVxuICAgIGlmIChzYXZlZFRoZW1lICYmIFsnbGlnaHQnLCAnZGFyaycsICdzeXN0ZW0nXS5pbmNsdWRlcyhzYXZlZFRoZW1lKSkge1xuICAgICAgc2V0VGhlbWVTdGF0ZShzYXZlZFRoZW1lKVxuICAgIH1cbiAgfSwgW10pXG5cbiAgLy8gVXBkYXRlIHJlc29sdmVkIHRoZW1lIGJhc2VkIG9uIHRoZW1lIHByZWZlcmVuY2UgYW5kIHN5c3RlbSBwcmVmZXJlbmNlXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc3QgdXBkYXRlUmVzb2x2ZWRUaGVtZSA9ICgpID0+IHtcbiAgICAgIGlmICh0aGVtZSA9PT0gJ3N5c3RlbScpIHtcbiAgICAgICAgY29uc3Qgc3lzdGVtUHJlZmVyc0RhcmsgPSB3aW5kb3cubWF0Y2hNZWRpYSgnKHByZWZlcnMtY29sb3Itc2NoZW1lOiBkYXJrKScpLm1hdGNoZXNcbiAgICAgICAgc2V0UmVzb2x2ZWRUaGVtZShzeXN0ZW1QcmVmZXJzRGFyayA/ICdkYXJrJyA6ICdsaWdodCcpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICBzZXRSZXNvbHZlZFRoZW1lKHRoZW1lKVxuICAgICAgfVxuICAgIH1cblxuICAgIHVwZGF0ZVJlc29sdmVkVGhlbWUoKVxuXG4gICAgLy8gTGlzdGVuIGZvciBzeXN0ZW0gdGhlbWUgY2hhbmdlc1xuICAgIGNvbnN0IG1lZGlhUXVlcnkgPSB3aW5kb3cubWF0Y2hNZWRpYSgnKHByZWZlcnMtY29sb3Itc2NoZW1lOiBkYXJrKScpXG4gICAgY29uc3QgaGFuZGxlQ2hhbmdlID0gKCkgPT4ge1xuICAgICAgaWYgKHRoZW1lID09PSAnc3lzdGVtJykge1xuICAgICAgICB1cGRhdGVSZXNvbHZlZFRoZW1lKClcbiAgICAgIH1cbiAgICB9XG5cbiAgICBtZWRpYVF1ZXJ5LmFkZEV2ZW50TGlzdGVuZXIoJ2NoYW5nZScsIGhhbmRsZUNoYW5nZSlcbiAgICByZXR1cm4gKCkgPT4gbWVkaWFRdWVyeS5yZW1vdmVFdmVudExpc3RlbmVyKCdjaGFuZ2UnLCBoYW5kbGVDaGFuZ2UpXG4gIH0sIFt0aGVtZV0pXG5cbiAgLy8gQXBwbHkgdGhlbWUgdG8gZG9jdW1lbnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCByb290ID0gZG9jdW1lbnQuZG9jdW1lbnRFbGVtZW50XG4gICAgaWYgKHJlc29sdmVkVGhlbWUgPT09ICdkYXJrJykge1xuICAgICAgcm9vdC5jbGFzc0xpc3QuYWRkKCdkYXJrJylcbiAgICAgIHJvb3Quc2V0QXR0cmlidXRlKCdkYXRhLXRoZW1lJywgJ2RhcmsnKVxuICAgIH0gZWxzZSB7XG4gICAgICByb290LmNsYXNzTGlzdC5yZW1vdmUoJ2RhcmsnKVxuICAgICAgcm9vdC5zZXRBdHRyaWJ1dGUoJ2RhdGEtdGhlbWUnLCAnbGlnaHQnKVxuICAgIH1cbiAgfSwgW3Jlc29sdmVkVGhlbWVdKVxuXG4gIGNvbnN0IHNldFRoZW1lID0gKG5ld1RoZW1lOiBUaGVtZSkgPT4ge1xuICAgIHNldFRoZW1lU3RhdGUobmV3VGhlbWUpXG4gICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3RoZW1lJywgbmV3VGhlbWUpXG4gIH1cblxuICBjb25zdCB0b2dnbGVUaGVtZSA9ICgpID0+IHtcbiAgICBpZiAodGhlbWUgPT09ICdsaWdodCcpIHtcbiAgICAgIHNldFRoZW1lKCdkYXJrJylcbiAgICB9IGVsc2UgaWYgKHRoZW1lID09PSAnZGFyaycpIHtcbiAgICAgIHNldFRoZW1lKCdzeXN0ZW0nKVxuICAgIH0gZWxzZSB7XG4gICAgICBzZXRUaGVtZSgnbGlnaHQnKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHZhbHVlOiBUaGVtZUNvbnRleHRUeXBlID0ge1xuICAgIHRoZW1lLFxuICAgIHJlc29sdmVkVGhlbWUsXG4gICAgc2V0VGhlbWUsXG4gICAgdG9nZ2xlVGhlbWUsXG4gIH1cblxuICByZXR1cm4gPFRoZW1lQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17dmFsdWV9PntjaGlsZHJlbn08L1RoZW1lQ29udGV4dC5Qcm92aWRlcj5cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZVRoZW1lKCkge1xuICBjb25zdCBjb250ZXh0ID0gdXNlQ29udGV4dChUaGVtZUNvbnRleHQpXG4gIGlmIChjb250ZXh0ID09PSB1bmRlZmluZWQpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoJ3VzZVRoZW1lIG11c3QgYmUgdXNlZCB3aXRoaW4gYSBUaGVtZVByb3ZpZGVyJylcbiAgfVxuICByZXR1cm4gY29udGV4dFxufSJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJUaGVtZUNvbnRleHQiLCJ1bmRlZmluZWQiLCJUaGVtZVByb3ZpZGVyIiwiY2hpbGRyZW4iLCJ0aGVtZSIsInNldFRoZW1lU3RhdGUiLCJyZXNvbHZlZFRoZW1lIiwic2V0UmVzb2x2ZWRUaGVtZSIsInNhdmVkVGhlbWUiLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiaW5jbHVkZXMiLCJ1cGRhdGVSZXNvbHZlZFRoZW1lIiwic3lzdGVtUHJlZmVyc0RhcmsiLCJ3aW5kb3ciLCJtYXRjaE1lZGlhIiwibWF0Y2hlcyIsIm1lZGlhUXVlcnkiLCJoYW5kbGVDaGFuZ2UiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsInJvb3QiLCJkb2N1bWVudCIsImRvY3VtZW50RWxlbWVudCIsImNsYXNzTGlzdCIsImFkZCIsInNldEF0dHJpYnV0ZSIsInJlbW92ZSIsInNldFRoZW1lIiwibmV3VGhlbWUiLCJzZXRJdGVtIiwidG9nZ2xlVGhlbWUiLCJ2YWx1ZSIsIlByb3ZpZGVyIiwidXNlVGhlbWUiLCJjb250ZXh0IiwiRXJyb3IiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/ThemeContext.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?3713":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?8e41":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/zod","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/zustand","vendor-chunks/react-hot-toast","vendor-chunks/webidl-conversions","vendor-chunks/goober","vendor-chunks/@swc","vendor-chunks/react-hook-form","vendor-chunks/@hookform"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fauth%2Fforgot-password%2Fpage&page=%2Fauth%2Fforgot-password%2Fpage&appPaths=%2Fauth%2Fforgot-password%2Fpage&pagePath=private-next-app-dir%2Fauth%2Fforgot-password%2Fpage.tsx&appDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faravinth%2Fworkspaces%2Fstartup%2Fportfolio_tracker%2Fapps%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();