import * as React from 'react'
import { useState, useRef, useEffect } from 'react'
import { ExpenseService, type ITransaction, type ICategory, useCurrencyStore } from '@repo/shared'
import { toast } from 'react-hot-toast'

// Temporary placeholder types
interface TransactionListProps {
  // Add props as needed
}

interface TransactionFilters {
  searchQuery: string
  categoryId: string
  startDate: string
  endDate: string
  transactionType: 'all' | 'income' | 'expense'
}

interface TransactionListWebProps extends TransactionListProps {
  onEditTransaction?: (transaction: ITransaction) => void
  key?: number // For refresh functionality
}

export function TransactionListWeb({ 
  onEditTransaction,
  key,
  ...props 
}: TransactionListWebProps) {
  const { formatCurrency } = useCurrencyStore()
  const [transactions, setTransactions] = useState<ITransaction[]>([])
  const [categories, setCategories] = useState<ICategory[]>([])
  const [loading, setLoading] = useState(true)
  const [loadingMore, setLoadingMore] = useState(false)
  const [error, setError] = useState('')
  const [totalCount, setTotalCount] = useState(0)
  const [filters, setFilters] = useState<TransactionFilters>({
    searchQuery: '',
    categoryId: '',
    startDate: '',
    endDate: '',
    transactionType: 'all'
  })
  const [offset, setOffset] = useState(0)
  const [hasMoreItems, setHasMoreItems] = useState(false)
  const ITEMS_PER_PAGE = 20

  const loadTransactions = async (reset = false) => {
    try {
      const currentOffset = reset ? 0 : offset
      if (reset) {
        setLoading(true)
        setError('')
      } else {
        setLoadingMore(true)
      }

      const options = {
        limit: ITEMS_PER_PAGE,
        offset: currentOffset,
        ...(filters.categoryId && { categoryId: filters.categoryId }),
        ...(filters.startDate && { startDate: filters.startDate }),
        ...(filters.endDate && { endDate: filters.endDate }),
        ...(filters.transactionType !== 'all' && { transactionType: filters.transactionType as 'income' | 'expense' }),
        ...(filters.searchQuery && { searchQuery: filters.searchQuery }),
      }

      const result = await ExpenseService.getTransactions(options)

      if (reset) {
        setTransactions(result.data)
        setOffset(ITEMS_PER_PAGE)
      } else {
        setTransactions(prev => [...prev, ...result.data])
        setOffset(prev => prev + ITEMS_PER_PAGE)
      }

      setTotalCount(result.count)
      setHasMoreItems(result.data.length === ITEMS_PER_PAGE && (currentOffset + ITEMS_PER_PAGE) < result.count)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load transactions')
      toast.error('Failed to load transactions')
    } finally {
      setLoading(false)
      setLoadingMore(false)
    }
  }

  const loadCategories = async () => {
    try {
      const categoriesData = await ExpenseService.getCategories()
      setCategories(categoriesData)
    } catch (err) {
      console.error('Failed to load categories:', err)
    }
  }

  // Initial load
  useEffect(() => {
    loadCategories()
    loadTransactions(true)
  }, [])

  // Reload when key changes (for refresh from parent)
  useEffect(() => {
    if (key !== undefined) {
      loadTransactions(true)
    }
  }, [key])

  // Reload when filters change
  useEffect(() => {
    loadTransactions(true)
  }, [filters])

  const handleRefresh = () => {
    loadTransactions(true)
  }

  const handleLoadMore = () => {
    if (!loadingMore && hasMoreItems) {
      loadTransactions(false)
    }
  }

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, searchQuery: query }))
  }

  const handleFilterChange = (newFilters: Partial<TransactionFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  const handleEdit = (transaction: ITransaction) => { 
    onEditTransaction?.(transaction) 
  }

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this transaction?')) {
      return
    }

    try {
      await ExpenseService.deleteTransaction(id)
      toast.success('Transaction deleted successfully')
      loadTransactions(true)
    } catch (err) {
      toast.error('Failed to delete transaction')
    }
  }

  const formatDate = (date: string | Date) => new Date(date).toLocaleDateString()

  // Quick date filter functions
  const [activeQuickFilter, setActiveQuickFilter] = useState<string | null>(null)
  
  const setQuickDateFilter = (period: 'week' | 'month' | 'year') => {
    const today = new Date()
    let startDate: Date

    switch (period) {
      case 'week':
        startDate = new Date(today)
        startDate.setDate(today.getDate() - 7)
        break
      case 'month':
        startDate = new Date(today)
        startDate.setMonth(today.getMonth() - 1)
        break
      case 'year':
        startDate = new Date(today)
        startDate.setFullYear(today.getFullYear() - 1)
        break
    }

    setActiveQuickFilter(period)
    setFilters(prev => ({
      ...prev,
      startDate: startDate.toISOString().split('T')[0],
      endDate: today.toISOString().split('T')[0]
    }))
  }
  
  const clearDateFilter = () => {
    setActiveQuickFilter('all')
    setFilters(prev => ({ ...prev, startDate: '', endDate: '' }))
  }

  const [showFilters, setShowFilters] = useState(false)
  const [searchInput, setSearchInput] = useState('')
  const loadMoreRef = useRef<HTMLDivElement>(null)

  // Sync search input with filters
  useEffect(() => {
    setSearchInput(filters.searchQuery)
  }, [filters.searchQuery])

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchInput !== filters.searchQuery) {
        setFilters(prev => ({ ...prev, searchQuery: searchInput }))
      }
    }, 500)

    return () => clearTimeout(timeoutId)
  }, [searchInput, filters.searchQuery])

  // Infinite scroll implementation
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting && hasMoreItems && !loadingMore) {
          handleLoadMore()
        }
      },
      { threshold: 0.1 }
    )

    if (loadMoreRef.current) {
      observer.observe(loadMoreRef.current)
    }

    return () => observer.disconnect()
  }, [hasMoreItems, loadingMore, handleLoadMore])

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    handleSearch(searchInput)
  }

  const clearFilters = () => {
    const clearedFilters = {
      searchQuery: '',
      categoryId: '',
      startDate: '',
      endDate: '',
      transactionType: 'all' as const
    }
    setActiveQuickFilter(null)
    setFilters(clearedFilters)
  }

  const hasActiveFilters = filters.searchQuery || filters.categoryId || 
    filters.startDate || filters.endDate || filters.transactionType !== 'all'

  return (
    <div className="bg-white rounded-xl shadow-lg border border-gray-200">
      {/* Header with Search and Filters */}
      <div className="px-6 py-5 border-b border-gray-200">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              Transactions
            </h2>
            {totalCount > 0 && (
              <p className="text-sm text-gray-500 mt-1">
                {totalCount} transaction{(totalCount as number) === 1 ? '' : 's'} found
              </p>
            )}
          </div>
          
          <div className="flex items-center gap-3">
            {/* Search Form */}
            <form onSubmit={handleSearchSubmit} className="flex-1 sm:flex-initial">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search by description or category..."
                  value={searchInput}
                  onChange={(e) => setSearchInput(e.target.value)}
                  className="w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>
            </form>

            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                showFilters
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <span className="flex items-center gap-2">
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
                </svg>
                Filters
                {hasActiveFilters && (
                  <span className="bg-red-500 text-white rounded-full w-2 h-2"></span>
                )}
              </span>
            </button>

            {/* Refresh Button */}
            <button
              onClick={handleRefresh}
              disabled={loading}
              className="px-4 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg font-medium transition-colors disabled:opacity-50"
            >
              <svg className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>

        {/* Filter Panel */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            {/* Quick Date Filters */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quick Date Filters
              </label>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => setQuickDateFilter('week')}
                  className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                    activeQuickFilter === 'week'
                      ? 'bg-blue-600 text-white'
                      : 'bg-blue-100 hover:bg-blue-200 text-blue-700'
                  }`}
                >
                  Last 7 Days
                </button>
                <button
                  onClick={() => setQuickDateFilter('month')}
                  className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                    activeQuickFilter === 'month'
                      ? 'bg-blue-600 text-white'
                      : 'bg-blue-100 hover:bg-blue-200 text-blue-700'
                  }`}
                >
                  Last Month
                </button>
                <button
                  onClick={() => setQuickDateFilter('year')}
                  className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                    activeQuickFilter === 'year'
                      ? 'bg-blue-600 text-white'
                      : 'bg-blue-100 hover:bg-blue-200 text-blue-700'
                  }`}
                >
                  Last Year
                </button>
                <button
                  onClick={clearDateFilter}
                  className={`px-3 py-1 text-sm rounded-lg transition-colors ${
                    activeQuickFilter === 'all'
                      ? 'bg-gray-600 text-white'
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                  }`}
                >
                  All Time
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Category Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <select
                  value={filters.categoryId}
                  onChange={(e) => handleFilterChange({ categoryId: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">All Categories</option>
                  {categories.map((category) => (
                    <option key={category.id} value={category.id}>
                      {category.icon} {category.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Transaction Type Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Type
                </label>
                <select
                  value={filters.transactionType}
                  onChange={(e) => handleFilterChange({ 
                    transactionType: e.target.value as 'all' | 'income' | 'expense' 
                  })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Types</option>
                  <option value="income">Income</option>
                  <option value="expense">Expense</option>
                </select>
              </div>

              {/* Start Date Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  From Date
                </label>
                <input
                  type="date"
                  value={filters.startDate}
                  onChange={(e) => handleFilterChange({ startDate: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>

              {/* End Date Filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  To Date
                </label>
                <input
                  type="date"
                  value={filters.endDate}
                  onChange={(e) => handleFilterChange({ endDate: e.target.value })}
                  className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Clear Filters */}
            {hasActiveFilters && (
              <div className="mt-4">
                <button
                  onClick={clearFilters}
                  className="text-sm text-blue-600 hover:text-blue-800 font-medium"
                >
                  Clear all filters
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 bg-red-50 border-l-4 border-red-400">
          <div className="flex">
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading ? (
        <div className="p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading transactions...</p>
        </div>
      ) : transactions.length === 0 ? (
        <div className="py-16 px-8 text-center">
          <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-6">
            <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No transactions found</h3>
          <p className="text-gray-600 max-w-sm mx-auto">
            {hasActiveFilters 
              ? 'Try adjusting your filters or search terms to find more transactions'
              : 'Add your first expense or income to get started!'
            }
          </p>
        </div>
      ) : (
        <>
          {/* Transaction List */}
          <div className="divide-y divide-gray-200">
            {transactions.map((transaction) => (
              <div 
                key={transaction.id} 
                className="px-4 py-3 hover:bg-gray-50 transition-colors group"
              >
                <div className="flex justify-between items-center">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <span className="text-xl flex-shrink-0">
                      {transaction.category?.icon || '💰'}
                    </span>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className="font-medium text-gray-900 truncate">
                          {transaction.category?.name || 'Uncategorized'}
                        </h3>
                        <span className="text-xs text-gray-500 ml-2 flex-shrink-0">
                          {formatDate(transaction.transaction_date)}
                        </span>
                      </div>
                      {transaction.description && (
                        <p className="text-sm text-gray-600 truncate">
                          {transaction.description}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3 ml-4">
                    <div className="text-right">
                      <p className={`text-base font-semibold ${
                        transaction.transaction_type === 'income' 
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }`}>
                        {transaction.transaction_type === 'income' ? '+' : '-'}
                        {formatCurrency(transaction.amount)}
                      </p>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      {onEditTransaction && (
                        <button
                          onClick={() => handleEdit(transaction)}
                          className="p-1.5 text-gray-400 hover:text-blue-600 transition-colors"
                          title="Edit transaction"
                        >
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                      )}
                      <button
                        onClick={() => handleDelete(transaction.id)}
                        className="p-1.5 text-gray-400 hover:text-red-600 transition-colors"
                        title="Delete transaction"
                      >
                        <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Load More */}
          {hasMoreItems && (
            <div ref={loadMoreRef} className="p-6 text-center border-t">
              {loadingMore ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-gray-600">Loading more...</span>
                </div>
              ) : (
                <button
                  onClick={handleLoadMore}
                  className="text-blue-600 hover:text-blue-800 font-medium"
                >
                  Load More Transactions
                </button>
              )}
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default TransactionListWeb