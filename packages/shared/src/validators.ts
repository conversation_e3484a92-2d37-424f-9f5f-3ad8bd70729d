import { z } from 'zod';

// Validation schemas using Zod
export const transactionSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  description: z.string().optional(),
  category_id: z.string().uuid('Invalid category ID'),
  transaction_type: z.enum(['income', 'expense']),
  transaction_date: z.string().or(z.date()),
});

export const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  icon: z.string().min(1, 'Icon is required'),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format'),
  type: z.enum(['income', 'expense']),
});

export type TTransactionForm = z.infer<typeof transactionSchema>;
export type TCategoryForm = z.infer<typeof categorySchema>;