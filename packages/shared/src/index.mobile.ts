// Shared business logic and utilities
export * from './types';
export * from './utils';
export * from './validators';
export * from './database.types';
export * from './lib/supabase';
export * from './lib/expenses';
export * from './lib/budget';
export * from './lib/analytics';
export * from './lib/recurring-transactions';
export * from './lib/accounts';
export * from './lib/transfers';
export * from './lib/investments';
export * from './lib/categories';
export * from './lib/biometric.mobile';
export * from './schemas/auth';
export * from './schemas/expense';
export * from './schemas/budget';
export * from './stores/currencyStore';

// Platform-specific exports (explicit re-export to avoid naming conflicts)
export { supabase as supabaseMobile } from './lib/supabase.mobile';