// Core types for the application
export interface IUser {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  currency_preference: string;
  created_at: string;
  updated_at: string;
}

export interface IUserProfile {
  id: string;
  user_id: string;
  display_name?: string;
  avatar_url?: string;
  currency_preference: string;
  country?: string;
  notification_preferences: {
    email_notifications?: boolean;
    push_notifications?: boolean;
    budget_alerts?: boolean;
    weekly_summary?: boolean;
  };
  created_at: string;
  updated_at: string;
}

export interface ICategory {
  id: string;
  name: string;
  icon?: string;
  color?: string;
  type?: 'income' | 'expense';
  user_id?: string;
  is_default: boolean;
  is_system: boolean; // System categories cannot be deleted
  is_active: boolean;
  parent_category_id?: string;
  sort_order: number;
  category_metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
  // Relations
  parent_category?: ICategory;
  subcategories?: ICategory[];
}

// Account types
export type AccountType = 'bank' | 'investment' | 'savings' | 'credit_card' | 'cash';
export type TransactionType = 'income' | 'expense' | 'transfer' | 'investment_buy' | 'investment_sell' | 'dividend';
export type TransactionStatus = 'pending' | 'completed' | 'cancelled' | 'failed';

export interface IAccount {
  id: string;
  user_id: string;
  name: string;
  account_type: AccountType;
  account_number?: string;
  institution_name?: string;
  currency: string;
  current_balance: number;
  available_balance?: number; // For credit cards
  credit_limit?: number; // For credit cards
  interest_rate?: number; // For savings/investment accounts
  is_active: boolean;
  is_primary: boolean;
  account_metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

export interface ITransaction {
  id: string;
  amount: number;
  description?: string;
  category_id?: string;
  account_id?: string;
  to_account_id?: string; // For transfers
  transfer_id?: string; // Links related transfer transactions
  transaction_type: TransactionType;
  transaction_date: string;
  transaction_status: TransactionStatus;
  fees?: number;
  balance_after?: number;
  // Investment-specific fields
  investment_symbol?: string;
  investment_quantity?: number;
  investment_price?: number;
  // Standard fields
  user_id: string;
  receipt_url?: string;
  created_at: string;
  updated_at: string;
  // Relations
  category?: ICategory;
  account?: IAccount;
  to_account?: IAccount;
}

export interface ITransferTransaction {
  id: string;
  amount: number;
  description?: string;
  from_account_id: string;
  to_account_id: string;
  transfer_id: string;
  transaction_date: string;
  fees?: number;
  user_id: string;
  from_account?: IAccount;
  to_account?: IAccount;
}

export interface IInvestmentTransaction {
  id: string;
  amount: number;
  description?: string;
  account_id: string;
  investment_symbol: string;
  investment_quantity: number;
  investment_price: number;
  transaction_type: 'investment_buy' | 'investment_sell';
  transaction_date: string;
  fees?: number;
  user_id: string;
  account?: IAccount;
}

export interface IBudget {
  id: string;
  name: string;
  amount: number;
  period: 'weekly' | 'monthly' | 'yearly';
  category_id?: string;
  user_id: string;
  start_date: string;
  end_date?: string;
  created_at: string;
  updated_at: string;
  category?: ICategory;
}

export interface IAnalyticsData {
  totalIncome: number;
  totalExpenses: number;
  netIncome: number;
  categoryBreakdown: Array<{
    category: ICategory;
    total: number;
    percentage: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    income: number;
    expenses: number;
    net: number;
  }>;
  topCategories: Array<{
    category: ICategory;
    total: number;
    transactionCount: number;
  }>;
}

export interface IInvestmentHolding {
  id: string;
  account_id: string;
  symbol: string;
  quantity: number;
  average_cost: number;
  current_price?: number;
  market_value?: number;
  unrealized_gain_loss?: number;
  last_updated: string;
  created_at: string;
  account?: IAccount;
}

export interface IAccountBalanceHistory {
  id: string;
  account_id: string;
  balance: number;
  balance_date: string;
  transaction_id?: string;
  created_at: string;
  account?: IAccount;
  transaction?: ITransaction;
}

export interface IDateRange {
  startDate: string;
  endDate: string;
}

// Form interfaces for creating/updating entities
export interface IAccountForm {
  name: string;
  account_type: AccountType;
  account_number?: string;
  institution_name?: string;
  currency?: string;
  current_balance?: number;
  available_balance?: number;
  credit_limit?: number;
  interest_rate?: number;
  is_primary?: boolean;
}

export interface ITransferForm {
  amount: number;
  description?: string;
  from_account_id: string;
  to_account_id: string;
  transaction_date: string;
  fees?: number;
}

export interface IInvestmentForm {
  amount: number;
  description?: string;
  account_id: string;
  investment_symbol: string;
  investment_quantity: number;
  investment_price: number;
  transaction_type: 'investment_buy' | 'investment_sell';
  transaction_date: string;
  fees?: number;
}

export interface ICategoryForm {
  name: string;
  icon?: string;
  color?: string;
  type?: 'income' | 'expense';
  parent_category_id?: string;
  sort_order?: number;
}