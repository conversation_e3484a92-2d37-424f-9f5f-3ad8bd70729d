// Core types for the application
export interface IUser {
  id: string;
  email: string;
  name: string;
  avatar_url?: string;
  currency_preference: string;
  created_at: string;
  updated_at: string;
}

export interface IUserProfile {
  id: string;
  user_id: string;
  display_name?: string;
  avatar_url?: string;
  currency_preference: string;
  country?: string;
  notification_preferences: {
    email_notifications?: boolean;
    push_notifications?: boolean;
    budget_alerts?: boolean;
    weekly_summary?: boolean;
  };
  created_at: string;
  updated_at: string;
}

export interface ICategory {
  id: string;
  name: string;
  icon: string;
  color: string;
  type: 'income' | 'expense';
  user_id: string;
  is_default: boolean;
  created_at: string;
  updated_at: string;
}

export interface ITransaction {
  id: string;
  amount: number;
  description?: string;
  category_id: string;
  transaction_type: 'income' | 'expense';
  transaction_date: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  category?: ICategory;
}

export interface IBudget {
  id: string;
  name: string;
  amount: number;
  period: 'weekly' | 'monthly' | 'yearly';
  category_id?: string;
  user_id: string;
  start_date: string;
  end_date?: string;
  created_at: string;
  updated_at: string;
  category?: ICategory;
}

export interface IAnalyticsData {
  totalIncome: number;
  totalExpenses: number;
  netIncome: number;
  categoryBreakdown: Array<{
    category: ICategory;
    total: number;
    percentage: number;
  }>;
  monthlyTrends: Array<{
    month: string;
    income: number;
    expenses: number;
    net: number;
  }>;
  topCategories: Array<{
    category: ICategory;
    total: number;
    transactionCount: number;
  }>;
}

export interface IDateRange {
  startDate: string;
  endDate: string;
}